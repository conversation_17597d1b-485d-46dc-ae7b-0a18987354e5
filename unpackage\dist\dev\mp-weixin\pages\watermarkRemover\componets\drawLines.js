"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  props: {
    width: {
      type: String,
      default: ""
    },
    height: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      type: null,
      Tress: false,
      ctxs: null,
      penLastPoint: null,
      penPath: [],
      currentPenColor: "#000",
      currentPenSize: 2,
      currentPenStyle: "",
      info: {}
    };
  },
  watch: {
    info: {
      handler(newVal, oldVal) {
        console.log(this.info, "colorLine");
      },
      deep: true
    }
  },
  mounted() {
    this.ctxs = common_vendor.index.createCanvasContext("drawLines", this);
    common_vendor.index.$on("colorLine", (e) => {
      this.type = e;
      this.Tress = e.tool == "brush" ? true : false;
    });
  },
  onReady() {
  },
  methods: {
    handleTouchMove(e) {
      console.log(this.ctxs, "123123");
      if (!this.ctxs)
        return;
      const touch = e.touches[0];
      if (!this.penLastPoint) {
        this.penLastPoint = { x: touch.x, y: touch.y };
        if (!this.penPath)
          this.penPath = [];
        this.penPath.push({ x: touch.x, y: touch.y });
        this.currentPenColor = this.type.color;
        this.currentPenSize = this.type.size;
        this.currentPenStyle = this.type.type;
        console.log("开始新线条，颜色:", this.currentPenColor, "大小:", this.currentPenSize);
      } else {
        this.penPath.push({ x: touch.x, y: touch.y });
        if (!this.ctxs) {
          this.ctxs = common_vendor.index.createCanvasContext("canvasId", this);
        }
        this.ctxs.beginPath();
        this.ctxs.setStrokeStyle(this.currentPenColor);
        this.ctxs.setLineWidth(this.currentPenSize);
        if (this.currentPenStyle === "dashed") {
          this.ctxs.setLineDash && this.ctxs.setLineDash([10, 8], 0);
        } else {
          this.ctxs.setLineDash && this.ctxs.setLineDash([], 0);
        }
        this.ctxs.moveTo(this.penLastPoint.x, this.penLastPoint.y);
        this.ctxs.lineTo(touch.x, touch.y);
        this.ctxs.stroke();
        this.ctxs.draw(true);
        this.penLastPoint = { x: touch.x, y: touch.y };
      }
    },
    handleTouchStart() {
    },
    handleTouchEnd(e) {
      if (!this.penPath || this.penPath.length < 2) {
        console.log("没有有效的线条可保存，路径为空或点数不足");
        return;
      }
      const xs = this.penPath.map((p) => p.x);
      const ys = this.penPath.map((p) => p.y);
      const minX = Math.min(...xs);
      const maxX = Math.max(...xs);
      const minY = Math.min(...ys);
      const maxY = Math.max(...ys);
      console.log(this.penPath, "this.penPath");
      const newPenItem = {
        type: "pen",
        path: this.penPath,
        color: this.currentPenColor,
        size: this.currentPenSize,
        style: this.currentPenStyle,
        selected: false,
        switch: true,
        x: minX,
        y: minY,
        w: Math.max(1, maxX - minX),
        h: Math.max(1, maxY - minY),
        rotate: 0
      };
      this.optimizePath(newPenItem);
    },
    optimizePath(path) {
      const tempCtx = this.ctxs;
      console.log(path, "path");
      tempCtx.beginPath();
      tempCtx.setStrokeStyle(path.color || "#8dd800");
      tempCtx.setLineWidth(path.size || 6);
      if (path.style === "dashed") {
        tempCtx.setLineDash && tempCtx.setLineDash([10, 8], 0);
      } else {
        tempCtx.setLineDash && tempCtx.setLineDash([], 0);
      }
      tempCtx.save();
      const cx = path.x + path.w / 2;
      const cy = path.y + path.h / 2;
      tempCtx.translate(cx, cy);
      tempCtx.rotate(path.rotate || 0);
      const firstPoint = path.path[0];
      tempCtx.moveTo(firstPoint.x - cx, firstPoint.y - cy);
      for (let i = 1; i < path.path.length; i++) {
        tempCtx.lineTo(path.path[i].x - cx, path.path[i].y - cy);
      }
      tempCtx.stroke();
      tempCtx.restore();
      tempCtx.draw();
      var that = this;
      common_vendor.index.canvasToTempFilePath({
        canvasId: "drawLines",
        x: path.x - 10,
        y: path.y - 10,
        width: path.w + 20,
        height: path.h + 20,
        success: (res) => {
          common_vendor.index.$emit("drawLines", {
            ...path,
            url: res.tempFilePath,
            w: path.w + 15,
            h: path.h + 15,
            x: path.x - 10,
            y: path.y - 10,
            type: "",
            path: null,
            name: "图片",
            size: 0,
            rotate: 0,
            display: false,
            color: "",
            closes: true
          });
          that.clearRects();
        },
        fail: (err) => {
          console.error(err, "❌ 保存失败");
        }
      }, this);
    },
    clearRects() {
      this.ctxs.clearRect(0, 0, this.width, this.height);
      this.ctxs.draw();
      this.isDrawing = false;
      this.currentStroke = null;
      this.penPath = [];
      this.penLastPoint = null;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $props.width + "px",
    b: $props.height + "px",
    c: $data.info.left + "px",
    d: $data.info.top + "px",
    e: common_vendor.o((...args) => $options.handleTouchStart && $options.handleTouchStart(...args)),
    f: common_vendor.o((...args) => $options.handleTouchMove && $options.handleTouchMove(...args)),
    g: common_vendor.o((...args) => $options.handleTouchEnd && $options.handleTouchEnd(...args)),
    h: common_vendor.n($data.Tress ? "drawLines" : "displayFalse")
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-3735a16b"]]);
wx.createComponent(Component);
