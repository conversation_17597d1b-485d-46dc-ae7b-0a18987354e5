<template>
    <canvas :style="{ width: width + 'px', height: height + 'px' }" canvas-id="firstCanvas" class="firstCanvas"
        id="firstCanvas">
    </canvas>
</template>

<script>
export default {
    props: {
        width: {
            type: String,
            default: ''
        },
        height: {
            type: String,
            default: ''
        },
        info: {
            type: Object,
            default: {}
        }
    },
    data() {
        return {
            ctx: null,
            data: null,
            dataTrans: []
        }
    },
    watch: {
        dataTrans: {
            handler(newVal, oldVal) {
                if (this.dataTrans.length == this.data.length) {
                    this.draws()
                }
            },
            deep: true
        }
    },
    mounted() {
        this.ctx = uni.createCanvasContext('firstCanvas', this);
        uni.$on('draws', async (e) => {
            this.data = e
            this.imageOpen()
        })
    },
    methods: {
        imageOpen() {
            const that = this
            this.data.forEach((item, index) => {
                if (item.type != 'text') {
                    uni.getImageInfo({
                        src: item.url,
                        success: (res) => {
                            if (item.type == 'background') {
                                item.w = res.width
                                item.h = res.height
                                item.url = res.path
                                that.dataTrans.push(true)
                            } else {
                                item.url = res.path
                                that.dataTrans.push(true)
                            }
                        }
                    })
                } else {
                    that.dataTrans.push(true)
                }
            })

        },
        draws() {
            const { ctx, data, width, height, info } = this;

            ctx.clearRect(0, 0, info.width, info.height);
            ctx.draw()

            console.log(ctx, data[0], 'ctx');
            // 设置画布背景色（可选）

            ctx.clearRect(0, 0, info.width, info.height);

            // 根据实际画布尺寸调整
            ctx.fillRect(0, 0, info.width, info.height);

            const i = data.filter(v => v.type == 'background')
            console.log(!i.length, 'i');

            if (!i.length) {
                ctx.setFillStyle('rgba(245, 245, 245, 0.2)');
            }

            // 遍历所有元素并绘制
            data.forEach(item => {
                console.log(item, 'item.url');
                ctx.save(); // 保存当前状态
                if (item.type == 'background') {

                    // 画布尺寸
                    const canvasW = info.width;
                    const canvasH = info.height;
                    // 以高度为基准铺满画布，高度撑满，宽度等比缩放，左右可能裁剪
                    const scale = canvasH / item.h;
                    const drawW = item.w * scale;
                    const drawH = canvasH;
                    const offsetX = (canvasW - drawW) / 2;
                    const offsetY = 0;
                    ctx.drawImage(item.url, offsetX, offsetY, drawW, drawH);

                    // ctx.drawImage(item.url, imgOriginalWidth / 2, 0, targetWidth, boxHeight);
                } else {

                    // 将画布原点移动到图片的中心位置
                    ctx.translate(item.x + item.w / 2, item.y + item.h / 2);

                    // 旋转画布
                    ctx.rotate(item.rotate);

                    // 将画布原点移回原来的位置
                    ctx.translate(-(item.x + item.w / 2), -(item.y + item.h / 2));

                    if (item.type === 'text') {
                        // 绘制文字
                        const t = item.size + 'px' + ' ' + item.textTpey
                        ctx.setFillStyle(item.color || '#000000');
                        // ctx.setFontSize(item.size);
                        ctx.setTextAlign('left');
                        ctx.setTextBaseline('top');
                        ctx.font = t
                        ctx.fillText(item.text, item.x, item.y); // 在变换后的坐标系中绘制
                    } else {
                        console.log('item:', item);
                        // 绘制图片 - 在变换后的坐标系中绘制
                        ctx.drawImage(item.url, item.x, item.y, item.w, item.h);
                    }
                }
                ctx.restore(); // 恢复状态
            });

            setTimeout(() => {
                ctx.draw(false, () => {
                    console.log('画布绘制完成');
                    uni.canvasToTempFilePath({
                        canvasId: 'firstCanvas',
                        fileType: 'png',
                        quality: 1,
                        success: (res) => {
                            console.log('生成图片成功:', res.tempFilePath);

                            // 保存到相册
                            uni.saveImageToPhotosAlbum({
                                filePath: res.tempFilePath,
                                success: () => {
                                    uni.showToast({
                                        title: '保存成功',
                                        icon: 'none'
                                    });
                                },
                                fail: (err) => {
                                    console.error('保存失败:', err);
                                    uni.showToast({
                                        title: '保存失败',
                                        icon: 'none'
                                    });
                                }
                            });
                        },
                        fail: (err) => {
                            console.error('生成图片失败:', err);
                        }
                    }, this);
                });
            }, 1000)
        },
    }
}

</script>

<style scoped>
.firstCanvas {
    position: fixed;
    top: -99999px;
    left: -99999px;
    /* position: absolute;
    top: 100px;
    left: 10px;
    border: 1px solid red; */
}
</style>