"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      Weights: false,
      currentTool: "",
      activeCopyOff: 0,
      selectedFont: 0,
      fontSize: 14,
      selectedColor: "#8BC34A",
      currentText: "",
      searchText: "",
      activeFilter: "all",
      // 画笔相关数据
      brushSize: 2,
      selectedBrushColor: "#4CAF50",
      selectedBrushType: "solid",
      brushColors: [
        "#4CAF50",
        // 绿色 (默认选中)
        "#9E9E9E",
        // 灰色
        "#757575",
        // 深灰色
        "#424242",
        // 更深灰色
        "#212121",
        // 黑色
        "#FFEB3B",
        // 黄色
        "#FF9800",
        // 橙色
        "#FF5722",
        // 深橙色
        "#F44336"
        // 红色
      ],
      // 复制面板相关数据
      copySearchText: "",
      activeCopyCategory: "favorite",
      copyCategories: [
        { key: "favorite", name: "收藏" },
        { key: "myMaterial", name: "我的素材" },
        { key: "artPot", name: "艺术盆栽" },
        { key: "flowerPot", name: "花卉盆栽" },
        { key: "shapePlant", name: "造型植物" },
        { key: "craftDecor", name: "工艺装饰" }
      ],
      selectedFontOries: [
        { name: "宋体", key: '"SimSun", serif' },
        { name: "微软雅黑", key: '"Microsoft YaHei", sans-serif' },
        { name: "黑体", key: '"SimHei", sans-serif' },
        { name: "楷体", key: '"KaiTi", serif' },
        { name: "仿宋", key: '"FangSong", serif' },
        { name: "Arial", key: '"Arial", sans-serif' },
        { name: "Helvetica", key: '"Helvetica", sans-serif' },
        { name: "Times New Roman", key: '"Times New Roman", serif' },
        { name: "Georgia", key: '"Georgia", serif' },
        { name: "Courier New", key: '"Courier New", monospace' },
        { name: "Verdana", key: '"Verdana", sans-serif' },
        { name: "Tahoma", key: '"Tahoma", sans-serif' },
        { name: "华文宋体", key: '"STSong", serif' },
        { name: "华文黑体", key: '"STHeiti", sans-serif' },
        { name: "华文楷体", key: '"STKaiti", serif' },
        { name: "Consolas", key: '"Consolas", monospace' },
        { name: "苹方", key: '"PingFang SC", sans-serif' },
        { name: "通用无衬线", key: "sans-serif" }
      ],
      fuls: [
        {
          name: "裁剪",
          icon: "裁",
          id: 1
        },
        {
          name: "变形",
          icon: "形",
          id: 2
        }
      ],
      currenEinet: 1,
      copyItems: [
        // 艺术盆栽
        {
          id: 1,
          title: "宝莲灯盆栽",
          image: "https://c-ssl.duitang.com/uploads/blog/202201/07/20220107201202_cc681.jpg",
          category: "artPot"
        },
        {
          id: 2,
          title: "玉兰灯盆栽",
          image: "https://ts3.tc.mm.bing.net/th/id/OIP-C.3vN88r7hCrYG33J2aTPPmAHaNK?rs=1&pid=ImgDetMain&o=7&rm=3",
          category: "artPot"
        },
        {
          id: 3,
          title: "宝莲灯盆栽",
          image: "https://tse3-mm.cn.bing.net/th/id/OIP-C.3qI2PjjJqsRdXaPWF90DsgHaNK?o=7rm=3&rs=1&pid=ImgDetMain&o=7&rm=3",
          category: "artPot"
        },
        // 花卉盆栽
        {
          id: 4,
          title: "北美冬青盆栽",
          image: "https://bpic.588ku.com/photo_water_img/24/03/20/995cf0b8d89b5eda394b76e0f4f0578a.jpg!/fw/351/quality/100/unsharp/true/compress/true",
          category: "flowerPot"
        },
        {
          id: 5,
          title: "北美冬青盆栽",
          image: "https://img.shetu66.com/zt/1661509292582_be304c32.jpg",
          category: "flowerPot"
        }
      ],
      colorPalette: [
        "#8BC34A",
        // 绿色 (选中状态)
        "#9E9E9E",
        // 灰色
        "#757575",
        // 深灰色
        "#424242",
        // 更深灰色
        "#212121",
        // 黑色
        "#FFEB3B",
        // 黄色
        "#FF9800",
        // 橙色
        "#FF5722",
        // 深橙色
        "#F44336"
        // 红色
      ],
      scaleItems: [
        {
          id: 1,
          title: "新 庭院造景",
          image: "https://c-ssl.duitang.com/uploads/blog/202201/07/20220107201202_cc681.jpg",
          views: 448,
          category: "garden"
        },
        {
          id: 2,
          title: "组合造景",
          image: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRkY5ODAwIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iNjAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0Ij7nu4TlkojpgKDmma88L3RleHQ+Cjwvc3ZnPg==",
          views: 325,
          category: "combine"
        },
        {
          id: 3,
          title: "室内装修",
          image: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjMjE5NkYzIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iNjAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0Ij7lrqTlhoXoo4Xkv6w8L3RleHQ+Cjwvc3ZnPg==",
          views: 567,
          category: "indoor"
        },
        {
          id: 4,
          title: "家具装饰",
          image: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjOUMyN0IwIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iNjAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0Ij7lrrblhbfoo4Xpo7w8L3RleHQ+Cjwvc3ZnPg==",
          views: 234,
          category: "furniture"
        },
        {
          id: 5,
          title: "花园景观",
          image: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRkY1NzIyIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iNjAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0Ij7oirHlm63mma/op4I8L3RleHQ+Cjwvc3ZnPg==",
          views: 189,
          category: "garden"
        },
        {
          id: 6,
          title: "其他装饰",
          image: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjNjA3RDhCIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iNjAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0Ij7lhbbku5boo4Xpo7w8L3RleHQ+Cjwvc3ZnPg==",
          views: 156,
          category: "other"
        }
      ]
    };
  },
  props: {
    width: {
      type: String,
      default: ""
    },
    height: {
      type: String,
      default: ""
    }
  },
  computed: {
    filteredItems() {
      let items = this.scaleItems;
      if (this.activeFilter !== "all") {
        items = items.filter((item) => item.category === this.activeFilter);
      }
      if (this.searchText) {
        items = items.filter(
          (item) => item.title.toLowerCase().includes(this.searchText.toLowerCase())
        );
      }
      return items;
    },
    filteredCopyItems() {
      let items = this.copyItems;
      if (this.activeCopyCategory !== "favorite" && this.activeCopyCategory !== "myMaterial") {
        items = items.filter((item) => item.category === this.activeCopyCategory);
      } else if (this.activeCopyCategory === "favorite") {
        items = this.copyItems;
      } else if (this.activeCopyCategory === "myMaterial") {
        items = this.copyItems;
      }
      if (this.copySearchText) {
        items = items.filter(
          (item) => item.title.toLowerCase().includes(this.copySearchText.toLowerCase())
        );
      }
      return items;
    }
  },
  methods: {
    enitsd(it) {
      this.currenEinet = it.id;
      common_vendor.index.$emit("edit", it);
    },
    selectTool(tool) {
      this.currentTool = tool;
      console.log("选择工具:", tool);
      if (tool === "add") {
        var that = this;
        common_vendor.index.chooseImage({
          count: 1,
          sourceType: ["album"],
          success: function(res) {
            console.log(res, "==");
            common_vendor.index.getImageInfo({
              src: res.tempFilePaths[0],
              success: function(image) {
                console.log(image, "M==");
                const whuse = image.width > that.width || image.height > that.height ? image.width / 6 : image.width / 3;
                const huse = image.width > that.width || image.height > that.height ? image.height / 6 : image.height / 3;
                common_vendor.index.$emit("selectScaleItem", {
                  name: "add",
                  x: 100,
                  y: 100,
                  w: whuse,
                  h: huse,
                  type: "add",
                  size: 0,
                  url: image.path,
                  text: "",
                  rotate: 0,
                  display: false,
                  color: "",
                  closes: true,
                  path: ""
                });
              }
            });
          }
        });
      } else if (tool === "edit") {
        common_vendor.index.$emit("edit", this.fuls.find((e) => e.id == this.currenEinet));
      }
      this.colorLine();
    },
    handleScale() {
      this.showScalePanel = true;
    },
    handleAdd() {
      common_vendor.index.showToast({
        title: "加载功能",
        icon: "none"
      });
    },
    getToolName(tool) {
      const toolNames = {
        "copy": "复制",
        "scale": "缩放",
        "text": "文字",
        "brush": "画笔",
        "add": "加载"
      };
      return toolNames[tool] || "未知";
    },
    getToolDescription(tool) {
      const descriptions = {
        "copy": "点击复制按钮来复制选中的内容",
        "scale": "使用缩放工具来调整内容大小",
        "text": "选择文字工具来添加或编辑文本",
        "brush": "使用画笔工具来进行绘制操作",
        "add": "点击加载按钮来添加新的内容"
      };
      return descriptions[tool] || "请选择一个工具开始操作";
    },
    // 面板相关方法
    closeTextPanel() {
      this.currentTool = "";
      this.colorLine();
      common_vendor.index.$emit("edit", null);
    },
    closePanels() {
    },
    closeBrushPanel() {
      this.showBrushPanel = false;
    },
    selectFont(font) {
      this.selectedFont = font;
      console.log("选择字体:", font);
    },
    onFontSizeChange(e) {
      this.fontSize = e.detail.value;
      console.log("字体大小:", this.fontSize);
    },
    selectColor(color) {
      this.selectedColor = color;
      console.log("选择颜色:", color);
    },
    // 缩放面板相关方法
    setFilter(filter) {
      this.activeFilter = filter;
      console.log("设置过滤器:", filter);
    },
    getTextDimensions(text, fontSize) {
      const maxCharsPerLine = 18;
      const lineCount = Math.ceil(text.length / maxCharsPerLine);
      let maxLineWidth = 0;
      for (let i = 0; i < lineCount; i++) {
        const start = i * maxCharsPerLine;
        const end = Math.min(start + maxCharsPerLine, text.length);
        const lineText = text.substring(start, end);
        let lineWidth = 0;
        for (let j = 0; j < lineText.length; j++) {
          lineWidth += /[\u4e00-\u9fa5]/.test(lineText[j]) ? fontSize : fontSize * 0.5;
        }
        maxLineWidth = Math.max(maxLineWidth, lineWidth);
      }
      const height = lineCount * fontSize;
      return {
        width: Math.round(maxLineWidth),
        height: Math.round(height)
      };
    },
    selectScaleItem(item) {
      console.log("选择缩放项目:", item);
      common_vendor.index.showToast({
        title: `选择了: ${item.title || item}`,
        icon: "none"
      });
      var that = this;
      if (item == "text") {
        const { width, height } = that.getTextDimensions(that.currentText, that.fontSize);
        common_vendor.index.$emit("selectScaleItem", {
          name: item.title || "文字",
          x: 100,
          y: 100,
          w: width + 5,
          h: height + 7,
          type: "text",
          textTpey: that.selectedFontOries[that.selectedFont].key,
          size: that.fontSize || 0,
          url: "",
          text: that.currentText,
          rotate: 0,
          display: false,
          color: that.selectedColor,
          closes: true,
          path: "",
          Weights: that.Weights
        });
        that.currentText = "";
      } else {
        common_vendor.index.getImageInfo({
          src: item.image,
          success: function(image) {
            console.log(image);
            console.log(image.height, that.height);
            const whuse = image.width > that.width || image.height > that.height ? image.width / 6 : image.width / 3;
            const huse = image.width > that.width || image.height > that.height ? image.height / 6 : image.height / 3;
            common_vendor.index.$emit("selectScaleItem", {
              name: item.title,
              x: 100,
              y: 100,
              w: whuse,
              h: huse,
              type: "",
              size: 0,
              url: image.path,
              text: "",
              rotate: 0,
              display: false,
              color: "",
              closes: true,
              path: item.image
            });
          }
        });
      }
    },
    // 画笔面板相关方法
    onBrushSizeChange(e) {
      this.brushSize = e.detail.value;
      console.log("画笔大小改变:", this.brushSize);
      this.colorLine();
    },
    selectBrushColor(color) {
      this.selectedBrushColor = color;
      console.log("选择画笔颜色:", color);
      this.colorLine();
    },
    selectBrushType(type) {
      this.selectedBrushType = type;
      console.log("选择画笔类型:", type);
      this.colorLine();
    },
    colorLine() {
      common_vendor.index.$emit("colorLine", {
        tool: this.currentTool,
        size: this.brushSize,
        color: this.selectedBrushColor,
        type: this.selectedBrushType
      });
    },
    // 复制面板相关方法
    setCopyCategory(category) {
      this.activeCopyCategory = category;
      console.log("设置复制分类:", category);
    },
    selectCopyItem(item) {
      console.log("选择复制项目:", item);
      common_vendor.index.showToast({
        title: `选择了: ${item.title}`,
        icon: "success"
      });
      this.showCopyPanel = false;
    },
    closeCopyPanel() {
      this.showCopyPanel = false;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.currentTool === "text"
  }, $data.currentTool === "text" ? {
    b: common_vendor.o(($event) => $options.selectScaleItem("text")),
    c: common_vendor.o((...args) => $options.closeTextPanel && $options.closeTextPanel(...args)),
    d: $data.currentText,
    e: common_vendor.o(($event) => $data.currentText = $event.detail.value),
    f: common_vendor.o(($event) => $data.Weights = !$data.Weights),
    g: $data.Weights ? 1 : "",
    h: common_vendor.f($data.selectedFontOries, (it, index, i0) => {
      return {
        a: common_vendor.t(it.name),
        b: index,
        c: $data.selectedFont === index ? 1 : "",
        d: common_vendor.o(($event) => $options.selectFont(index), index)
      };
    }),
    i: $data.fontSize,
    j: common_vendor.o((...args) => $options.onFontSizeChange && $options.onFontSizeChange(...args)),
    k: common_vendor.t($data.fontSize),
    l: common_vendor.f($data.colorPalette, (color, index, i0) => {
      return {
        a: index,
        b: color,
        c: $data.selectedColor === color ? 1 : "",
        d: common_vendor.o(($event) => $options.selectColor(color), index)
      };
    }),
    m: common_vendor.o(() => {
    })
  } : {}, {
    n: $data.currentTool === "scale"
  }, $data.currentTool === "scale" ? {
    o: common_vendor.o((...args) => $options.closeTextPanel && $options.closeTextPanel(...args)),
    p: $data.searchText,
    q: common_vendor.o(($event) => $data.searchText = $event.detail.value),
    r: $data.activeFilter === "all" ? 1 : "",
    s: common_vendor.o(($event) => $options.setFilter("all")),
    t: $data.activeFilter === "indoor" ? 1 : "",
    v: common_vendor.o(($event) => $options.setFilter("indoor")),
    w: $data.activeFilter === "combine" ? 1 : "",
    x: common_vendor.o(($event) => $options.setFilter("combine")),
    y: $data.activeFilter === "furniture" ? 1 : "",
    z: common_vendor.o(($event) => $options.setFilter("furniture")),
    A: $data.activeFilter === "garden" ? 1 : "",
    B: common_vendor.o(($event) => $options.setFilter("garden")),
    C: $data.activeFilter === "other" ? 1 : "",
    D: common_vendor.o(($event) => $options.setFilter("other")),
    E: $data.activeFilter === "other" ? 1 : "",
    F: common_vendor.o(($event) => $options.setFilter("other")),
    G: $data.activeFilter === "other" ? 1 : "",
    H: common_vendor.o(($event) => $options.setFilter("other")),
    I: $data.activeFilter === "other" ? 1 : "",
    J: common_vendor.o(($event) => $options.setFilter("other")),
    K: $data.activeFilter === "other" ? 1 : "",
    L: common_vendor.o(($event) => $options.setFilter("other")),
    M: $data.activeFilter === "other" ? 1 : "",
    N: common_vendor.o(($event) => $options.setFilter("other")),
    O: $data.activeFilter === "other" ? 1 : "",
    P: common_vendor.o(($event) => $options.setFilter("other")),
    Q: common_vendor.f($options.filteredItems, (item, index, i0) => {
      return {
        a: item.image,
        b: common_vendor.t(item.title),
        c: common_vendor.t(item.views),
        d: index,
        e: common_vendor.o(($event) => $options.selectScaleItem(item), index)
      };
    }),
    R: common_vendor.o(() => {
    })
  } : {}, {
    S: $data.currentTool === "brush"
  }, $data.currentTool === "brush" ? {
    T: common_vendor.o((...args) => $options.closeTextPanel && $options.closeTextPanel(...args)),
    U: $data.brushSize,
    V: common_vendor.o((...args) => $options.onBrushSizeChange && $options.onBrushSizeChange(...args)),
    W: common_vendor.t($data.brushSize),
    X: common_vendor.f($data.brushColors, (color, index, i0) => {
      return common_vendor.e({
        a: $data.selectedBrushColor === color
      }, $data.selectedBrushColor === color ? {} : {}, {
        b: index,
        c: color,
        d: $data.selectedBrushColor === color ? 1 : "",
        e: common_vendor.o(($event) => $options.selectBrushColor(color), index)
      });
    }),
    Y: $data.selectedBrushType === "solid" ? 1 : "",
    Z: common_vendor.o(($event) => $options.selectBrushType("solid")),
    aa: $data.selectedBrushType === "dashed" ? 1 : "",
    ab: common_vendor.o(($event) => $options.selectBrushType("dashed")),
    ac: common_vendor.o(() => {
    })
  } : {}, {
    ad: $data.currentTool === "edit"
  }, $data.currentTool === "edit" ? {
    ae: common_vendor.o((...args) => $options.closeTextPanel && $options.closeTextPanel(...args)),
    af: common_vendor.f($data.fuls, (it, index, i0) => {
      return {
        a: common_vendor.t(it.icon),
        b: common_vendor.t(it.name),
        c: index,
        d: $data.currenEinet === it.id ? 1 : "",
        e: common_vendor.o(($event) => $options.enitsd(it), index)
      };
    }),
    ag: common_vendor.o(() => {
    })
  } : {}, {
    ah: $data.currentTool == "copy"
  }, $data.currentTool == "copy" ? {
    ai: common_vendor.o((...args) => $options.closeTextPanel && $options.closeTextPanel(...args)),
    aj: $data.copySearchText,
    ak: common_vendor.o(($event) => $data.copySearchText = $event.detail.value),
    al: common_vendor.f($data.copyCategories, (category, k0, i0) => {
      return {
        a: common_vendor.t(category.name),
        b: $data.activeCopyCategory === category.key ? 1 : "",
        c: category.key,
        d: common_vendor.o(($event) => $options.setCopyCategory(category.key), category.key)
      };
    }),
    am: common_vendor.f($data.copyCategories, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: index,
        c: common_vendor.s($data.activeCopyOff == index ? "color: #4CAF50" : ""),
        d: common_vendor.o(($event) => $data.activeCopyOff = index, index)
      };
    }),
    an: common_vendor.f($options.filteredCopyItems, (item, index, i0) => {
      return {
        a: item.image,
        b: common_vendor.t(item.title),
        c: common_vendor.t(item.views),
        d: index,
        e: common_vendor.o(($event) => $options.selectScaleItem(item), index)
      };
    }),
    ao: common_vendor.o(() => {
    })
  } : {}, {
    ap: $data.currentTool === "copy" ? 1 : "",
    aq: common_vendor.o(($event) => $options.selectTool("copy")),
    ar: $data.currentTool === "scale" ? 1 : "",
    as: common_vendor.o(($event) => $options.selectTool("scale")),
    at: $data.currentTool === "text" ? 1 : "",
    av: common_vendor.o(($event) => $options.selectTool("text")),
    aw: $data.currentTool === "edit" ? 1 : "",
    ax: common_vendor.o(($event) => $options.selectTool("edit")),
    ay: $data.currentTool === "brush" ? 1 : "",
    az: common_vendor.o(($event) => $options.selectTool("brush")),
    aA: $data.currentTool === "add" ? 1 : "",
    aB: common_vendor.o(($event) => $options.selectTool("add"))
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-b411d5cf"]]);
wx.createComponent(Component);
