<template>
  <view class="editor-container">
    <!-- Top navigation bar -->
    <view class="top-bar">
      <text class="back-btn" @click="goBack">回首页</text>
      <text class="save-btn" @click="saveImage">保存</text>
    </view>

    <!-- Main editor area -->
    <view class="image-area" @touchstart="onCanvasTouchStart" @touchmove="onCanvasTouchMove"
      @touchend="onCanvasTouchEnd">
      <image class="transparent-bg" :src="imageSrc || defaultImage" mode="aspectFit" :style="{
        filter: activeToolType === 'blur' ? `blur(${blurValue}px)` : 'none',
        transform: `scale(${scale})`,
        transition: 'transform 0.3s ease'
      }"></image>

      <!-- Material layers -->
      <view v-for="(layer, index) in materialLayers" :key="index" class="material-layer"
        :class="{ 'selected': layer.isSelected }"
        :style="{
          position: 'absolute',
          left: layer.x + 'px',
          top: layer.y + 'px',
          width: layer.width + 'px',
          height: layer.height + 'px',
          transform: `rotate(${layer.rotation}deg)`,
          transformOrigin: 'center center',
          zIndex: layer.isSelected ? 100 : 10 + index,
          opacity: layer.visible ? 1 : 0.3
        }"
        @touchstart.stop="onLayerTouchStart($event, index)"
        @touchmove.stop="onLayerTouchMove($event, index)"
        @touchend.stop="onLayerTouchEnd">
        <image :src="layer.image" mode="aspectFill" class="layer-image"></image>

        <!-- Controls when layer is selected -->
        <template v-if="layer.isSelected">
          <!-- Border with control points -->
          <view class="layer-border">
            <!-- Top-left control point -->
            <view class="control-point top-left"></view>

            <!-- Top-center control point -->
            <view class="control-point top-center"></view>

            <!-- Top-right control point (delete) -->
            <view class="control-point top-right delete-point" @touchstart.stop="deleteMaterialLayer(index)">
              <text class="control-icon">×</text>
            </view>

            <!-- Middle-left control point -->
            <view class="control-point middle-left"></view>

            <!-- Middle-right control point (rotate) -->
            <view class="control-point middle-right rotate-point" @touchstart.stop="onRotationStart($event, index)">
              <text class="control-icon">⟳</text>
            </view>

            <!-- Bottom-left control point (duplicate) -->
            <view class="control-point bottom-left duplicate-point" @touchstart.stop="duplicateLayer(index)">
              <text class="control-icon">+</text>
            </view>

            <!-- Bottom-center control point -->
            <view class="control-point bottom-center"></view>

            <!-- Bottom-right control point (resize) -->
            <view class="control-point bottom-right resize-point" @touchstart.stop="onResizeStart($event, index)">
              <text class="control-icon">⤡</text>
            </view>
          </view>
        </template>
      </view>

      <!-- Text layers -->
      <view v-for="(layer, index) in textLayers" :key="index" class="text-layer"
        :class="{ 'selecteds': layer.isSelected }" :style="{
          position: 'absolute',
          left: layer.x + 'px',
          top: layer.y + 'px',
          width: layer.width + 'px',
          height: layer.height + 'px',
          transform: `rotate(${layer.rotation}deg)`,
          zIndex: layer.isSelected ? 100 : 20 + index,
          opacity: layer.visible ? 1 : 0.3,
          pointerEvents: 'auto'
        }" @touchstart.stop="onTextLayerTouchStart($event, index)">
        <view class="text-content-wrapper">
          <text class="text-content" :style="{
            color: layer.color,
            fontFamily: layer.font,
            fontSize: layer.size + 'px',
            lineHeight: (layer.size * 1.2) + 'px',
            textAlign: 'center'
          }">{{ layer.text }}</text>
        </view>

        <!-- Controls when text layer is selected -->
        <template v-if="layer.isSelected">
          <!-- Border with control points -->
          <view class="layer-border">
            <!-- Top-left control point -->
            <view class="control-point top-left"></view>

            <!-- Top-center control point -->
            <view class="control-point top-center"></view>

            <!-- Top-right control point (delete) -->
            <view class="control-point top-right delete-point" @touchstart.stop="deleteTextLayer(index)">
              <text class="control-icon">×</text>
            </view>

            <!-- Middle-left control point -->
            <view class="control-point middle-left"></view>

            <!-- Middle-right control point (rotate) -->
            <view class="control-point middle-right rotate-point" @touchstart.stop="onTextRotationStart($event, index)">
              <text class="control-icon">⟳</text>
            </view>

            <!-- Bottom-left control point (duplicate) -->
            <view class="control-point bottom-left duplicate-point" @touchstart.stop="duplicateTextLayer(index)">
              <text class="control-icon">+</text>
            </view>

            <!-- Bottom-center control point (edit) -->
            <view class="control-point bottom-center edit-point" @touchstart.stop="editTextLayer(index)">
              <text class="control-icon">✎</text>
            </view>

            <!-- Bottom-right control point (resize) -->
            <view class="control-point bottom-right resize-point" @touchstart.stop="onTextResizeStart($event, index)">
              <text class="control-icon">⤡</text>
            </view>
          </view>
        </template>
      </view>
    </view>

    <!-- Material selection popup -->
    <view v-if="activeToolType == 'material' || activeToolType == 'template'" class="material-panel">
      <view class="material-search">
        <view class="search-categories">
          <scroll-view scroll-x="true" class="category-scroll">
            <view v-for="(category, idx) in categories" :key="idx" class="category-item"
              :class="{ active: currentCategory === idx }" @click="selectCategory(idx)">
              {{ category.name }}
            </view>
          </scroll-view>
        </view>
      </view>
      <view style="display: flex;">
        <view class="subcategories">
          <view v-for="(subcategory, idx) in currentSubcategories" :key="idx" class="subcategory-item"
            :class="{ active: currentSubcategory === idx }" @click="selectSubcategory(idx)">
            {{ subcategory.name }}
          </view>
        </view>

        <view class="material-grid">
          <view class="material-item" v-for="(item, index) in currentMaterials" :key="index"
            @click="selectMaterial(item)">
            <image :src="item.image" mode="aspectFill" class="material-image"></image>
            <view class="material-name">{{ item.name }}</view>
            <view v-if="item.premium" class="premium-icon">⭐</view>
          </view>
        </view>
      </view>

      <view class="material-panel-close" @click="closeMaterialPanel">
        <text class="close-icon">×</text>
      </view>
    </view>

    <!-- Layer panel (shows when toggled) -->
    <view v-if="showLayerPanel" class="layer-panel">
      <view class="layer-panel-header">
        <view class="layer-tabs">
          <view class="layer-tab active">图层管理</view>
        </view>
        <text class="close-panel" @click="toggleLayerPanel">×</text>
      </view>
      <view class="layer-list">
        <!-- Background layer (always present) -->
        <view class="layer-item">
          <view class="layer-preview">
            <image :src="imageSrc || defaultImage" mode="aspectFit"></image>
          </view>
          <view class="layer-info">
            <text class="layer-name">背景图层</text>
          </view>
          <view class="layer-visibility">
            <text class="visibility-icon">👁️</text>
          </view>
        </view>

        <!-- Text layers -->
        <view class="layer-item" v-for="(layer, index) in textLayers" :key="'text-' + index"
          :class="{ 'active-layer': layer.isSelected, 'hidden-layer': !layer.visible }"
          @tap="selectLayerFromPanel(index, 'text')">
          <view class="layer-preview text-preview" :style="{ color: layer.color, fontFamily: layer.font }">
            <text>T</text>
          </view>
          <view class="layer-info">
            <text class="layer-name">{{ layer.text.substring(0, 8) || '文字图层' + (index + 1) }}</text>
          </view>
          <view class="layer-visibility" @tap.stop="toggleTextLayerVisibility(index)">
            <text class="visibility-icon" :data-hidden="!layer.visible">{{ layer.visible ? '👁️' : '👁️‍🗨️' }}</text>
          </view>
          <view class="layer-delete" @tap.stop="deleteTextLayer(index)">
            <text class="delete-icon">×</text>
          </view>
        </view>

        <!-- Material layers -->
        <view class="layer-item" v-for="(layer, index) in materialLayers" :key="'material-' + index"
          :class="{ 'active-layer': layer.isSelected }" @tap="selectLayerFromPanel(index)">
          <view class="layer-preview">
            <image :src="layer.image" mode="aspectFit"></image>
          </view>
          <view class="layer-info">
            <text class="layer-name">{{ layer.name || '素材图层' + (index + 1) }}</text>
          </view>
          <view class="layer-visibility" @tap.stop="toggleMaterialLayerVisibility(index)">
            <text class="visibility-icon">{{ layer.visible ? '👁️' : '👁️‍🗨️' }}</text>
          </view>
          <view class="layer-delete" @tap.stop="deleteMaterialLayer(index)">
            <text class="delete-icon">×</text>
          </view>
        </view>
      </view>
      <view class="layer-panel-footer">
        <view class="layer-action">
          <text class="action-icon">⊞</text>
          <text class="action-text" @click="showLayerPanel = false">收起图层</text>
        </view>
      </view>
    </view>

    <!-- Text editing popup -->
    <view v-if="activeToolType == 'text'" class="text-panel">
      <view class="text-panel-header">
        <text class="apply-panel" @click="closeMaterialPanel">×</text>
        <text class="apply-panel" @click="applyTextChanges">√</text>
      </view>

      <view class="text-input-area">
        <textarea class="text-input" v-model="currentText" placeholder="示例文字" auto-height></textarea>
      </view>

      <view class="text-font-options">
        <scroll-view scroll-x="true" class="font-scroll">
          <view v-for="(font, idx) in fontOptions" :key="idx" class="font-item"
            :class="{ active: currentFont === font.value }" :style="{ fontFamily: font.value }"
            @click="selectFont(font.value)">
            {{ font.name }}
          </view>
        </scroll-view>
      </view>

      <view class="text-size-control">
        <text class="size-label">大小</text>
        <slider class="size-slider" min="12" max="26" step="1" show-value :value="textSize" @change="changeTextSize"
          activeColor="#ffffff" backgroundColor="#666666" block-color="#ffffff" block-size="24"></slider>
        <!-- <text class="size-value">{{ textSize }}</text> -->
      </view>

      <view class="text-color-options">
        <text class="color-label">颜色</text>
        <view class="color-grid">
          <view v-for="(color, idx) in colorOptions" :key="idx" class="color-item"
            :class="{ active: currentColor === color }" :style="{ backgroundColor: color }" @click="selectColor(color)">
          </view>
        </view>
      </view>
    </view>

    <!-- Bottom toolbar -->
    <view class="bottom-toolbar">
      <view class="toolbar-controls">
        <view class="control-btn prev" @click="prevStep">
          <text class="icon">←</text>
        </view>
        <view class="control-btn next" @click="nextStep">
          <text class="icon">→</text>
        </view>
        <view class="control-btn zoom-in" @click="zoomIn">
          <text class="icon">+</text>
        </view>
        <view class="control-btn zoom-out" @click="zoomOut">
          <text class="icon">-</text>
        </view>
        <view class="control-btn reset" @click="resetView">
          <text class="icon">⊡</text>
        </view>
        <view class="control-btn fullscreen" @click="toggleFullscreen">
          <text class="icon">⤢</text>
        </view>
      </view>

      <view class="layer-btn" @click="toggleLayerPanel">
        <text class="layer-text">图层</text>
      </view>
    </view>

    <!-- Bottom tools panel -->
    <view class="tools-panel">
      <view class="tool-item" @click="selectTool('material')">
        <view class="tool-icon material-icon"></view>
        <text class="tool-text">素材</text>
      </view>
      <view class="tool-item" @click="selectTool('template')">
        <view class="tool-icon template-icon"></view>
        <text class="tool-text">模板</text>
      </view>
      <view class="tool-item" @click="selectTool('text')">
        <view class="tool-icon text-icon"></view>
        <text class="tool-text">文字</text>
      </view>
      <view class="tool-item" @click="selectTool('mark')">
        <view class="tool-icon mark-icon"></view>
        <text class="tool-text">标记</text>
      </view>
      <view class="tool-item" @click="selectTool('brush')">
        <view class="tool-icon brush-icon"></view>
        <text class="tool-text">画笔</text>
      </view>
      <view class="tool-item" @click="selectTool('addImage')">
        <view class="tool-icon add-image-icon"></view>
        <text class="tool-text">加图</text>
      </view>
    </view>

    <!-- Brush settings popup -->
    <view v-if="activeToolType == 'brush'" class="brush-panel">
      <view class="text-panel-header">
        <text class="apply-panel" @click="closeMaterialPanel">×</text>
      </view>
      <view class="brush-size-control">
        <text class="size-label">粗细</text>
        <slider class="size-slider" min="1" max="30" step="1" show-value :value="brushSize" @change="changeBrushSize"
          activeColor="#ffffff" backgroundColor="#666666" block-color="#ffffff" block-size="24"></slider>
        <!-- <text class="size-value">{{brushSize}}</text> -->
      </view>

      <view class="brush-color-options">
        <text class="color-label">颜色</text>
        <scroll-view scroll-x="true" class="color-scroll">
          <view class="color-grid">
            <view v-for="(color, idx) in colorOptions" :key="idx" class="color-item"
              :class="{ active: brushColor === color }" :style="{ backgroundColor: color }"
              @click="selectBrushColor(color)"></view>
          </view>
        </scroll-view>
      </view>

      <view class="brush-style-options">
        <text class="style-label">笔状</text>
        <view class="style-grid">
          <view class="style-item" :class="{ active: brushStyle === 'solid' }" @click="selectBrushStyle('solid')">
            <view class="style-line solid-line"></view>
            <text class="style-text">实线</text>
          </view>
          <view class="style-item" :class="{ active: brushStyle === 'dashed' }" @click="selectBrushStyle('dashed')">
            <view class="style-line dashed-line"></view>
            <text class="style-text">虚线</text>
          </view>
        </view>
      </view>
    </view>

    <!-- Canvas for drawing -->
    <canvas v-if="activeToolType === 'brush'" canvas-id="brushCanvas" class="brush-canvas" @touchstart="onBrushStart"
      @touchmove="onBrushMove" @touchend="onBrushEnd"></canvas>
  </view>
</template>

<script>
export default {
  data() {
    return {
      imageSrc: '',
      defaultImage: '',
      activeToolType: '',
      scale: 1,
      brushContext: null,
      blurValue: 2,
      isDrawing: false,
      lastX: 0,
      lastY: 0,
      showLayerPanel: false,
      showMaterialPanel: false,
      currentCategory: 0,
      currentSubcategory: 2, // Default to "艺术盆栽"
      layers: [
        {
          name: '背景图层',
          thumbnail: '',
          visible: true
        },
        {
          name: '文字图层',
          thumbnail: '',
          visible: true
        }
      ],
      categories: [
        {
          name: '室内植物',
          subcategories: [
            { name: '收藏', materials: [] },
            { name: '我的素材', materials: [] },
            {
              name: '艺术盆栽',
              materials: [
                { name: '宝莲灯盆栽', image: 'https://b0.bdstatic.com/ugc/img/2024-12-28/42e36b01cb8ddae367b10ca4b0e4b919.png', premium: true },
                { name: '宝莲灯盆栽', image: 'https://example.com/plant2.jpg', premium: true },
                { name: '宝莲灯盆栽', image: 'https://example.com/plant3.jpg', premium: true },
                { name: '红梅盆栽', image: 'https://example.com/plant4.jpg', premium: true },
                { name: '红梅盆栽', image: 'https://example.com/plant5.jpg', premium: true },
                { name: '红梅盆栽', image: 'https://example.com/plant6.jpg', premium: true }
              ]
            },
            { name: '花卉盆栽', materials: [] },
            { name: '造型植物', materials: [] },
            { name: '大型植物', materials: [] }
          ]
        },
        {
          name: '庭院植物',
          subcategories: [
            { name: '收藏', materials: [] },
            { name: '我的素材', materials: [] },
            { name: '庭院树木', materials: [] },
            { name: '观赏草本', materials: [] },
            { name: '地被植物', materials: [] }
          ]
        },
        {
          name: '组合造景',
          subcategories: [
            { name: '收藏', materials: [] },
            { name: '我的素材', materials: [] },
            { name: '现代组合', materials: [] },
            { name: '中式组合', materials: [] },
            { name: '欧式组合', materials: [] }
          ]
        },
        {
          name: '树木模块',
          subcategories: [
            { name: '收藏', materials: [] },
            { name: '我的素材', materials: [] },
            { name: '常绿乔木', materials: [] },
            { name: '落叶乔木', materials: [] },
            { name: '观花乔木', materials: [] }
          ]
        },
        {
          name: '容器&架构',
          subcategories: [
            { name: '收藏', materials: [] },
            { name: '我的素材', materials: [] },
            { name: '花盆花器', materials: [] },
            { name: '花架支架', materials: [] },
            { name: '装饰构件', materials: [] }
          ]
        }
      ],
      showTextPanel: false,
      currentText: '示例文字',
      currentFont: 'sans-serif',
      textSize: 18,
      currentColor: '#ff0000',
      fontOptions: [
        { name: '思源黑体', value: 'sans-serif' },
        { name: '三极字体', value: 'serif' },
        { name: '汉鼎行书', value: 'cursive' },
        { name: '思源宋体', value: 'SimSun' },
        { name: '思源圆体', value: 'Arial' }
      ],
      colorOptions: [
        '#ffffff', '#cccccc', '#999999', '#666666', '#333333', '#000000',
        '#ffff00', '#ffa500', '#ff8c00', '#ff4500', '#ff0000'
      ],
      showBrushPanel: false,
      brushSize: 10,
      brushColor: '#ff0000',
      brushStyle: 'solid',
      materialLayers: [], // Array to store all material layers
      activeLayerIndex: -1, // Index of currently selected layer
      isDragging: false, // Flag for drag operation
      isResizing: false, // Flag for resize operation
      isRotating: false, // Flag for rotation operation
      startX: 0, // Starting X position for touch operations
      startY: 0, // Starting Y position for touch operations
      startWidth: 0, // Starting width for resize operation
      startHeight: 0, // Starting height for resize operation
      startRotation: 0, // Starting rotation angle for rotation operation
      lastTouchTime: 0, // For detecting double taps
      canvasWidth: 0, // Canvas width for boundary checking
      canvasHeight: 0, // Canvas height for boundary checking
      textLayers: [], // Array to store text layers
      activeTextLayerIndex: -1, // Index of currently selected text layer
      editingTextLayer: false, // Flag to indicate we're editing an existing text layer
    }
  },
  onLoad() {
    this.initCanvas();
  },
  computed: {
    currentSubcategories() {
      if (this.categories.length === 0) return [];
      return this.categories[this.currentCategory].subcategories;
    },
    currentMaterials() {
      if (this.currentSubcategories.length === 0) return [];
      return this.currentSubcategories[this.currentSubcategory].materials;
    }
  },
  mounted() {
    // Get canvas dimensions for boundary checking
    setTimeout(() => {
      const query = uni.createSelectorQuery().in(this);
      query.select('.image-area').boundingClientRect(data => {
        if (data) {
          this.canvasWidth = data.width;
          this.canvasHeight = data.height;
        }
      }).exec();
    }, 300);
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    saveImage() {
      uni.showLoading({ title: '保存中...' });
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({ title: '保存成功', icon: 'success' });
      }, 1000);
    },
    prevStep() {
      // Previous step logic
    },
    nextStep() {
      // Next step logic
    },
    zoomIn() {
      this.scale = Math.min(this.scale + 0.1, 3);
    },
    zoomOut() {
      this.scale = Math.max(this.scale - 0.1, 0.5);
    },
    resetView() {
      this.scale = 1;
    },
    toggleFullscreen() {
      // Fullscreen toggle logic
    },
    toggleLayerPanel() {
      this.showLayerPanel = !this.showLayerPanel;
    },
    toggleLayerVisibility(index) {
      this.layers[index].visible = !this.layers[index].visible;
    },
    selectTool(tool) {
      this.activeToolType = tool;
      if (tool == 'material') {
        this.initCanvas()
      }
      if (tool == 'text') {
        this.textLayers.map(layer => {
          if (layer.isSelected) {
            this.currentText = layer.text
            this.currentFont = layer.font
            this.textSize = layer.size
            this.currentColor = layer.color
          }
        })
      }
    },
    initCanvas() {
      try {
        this.brushContext = uni.createCanvasContext('brushCanvas', this);
        if (this.brushContext) {
          this.brushContext.setLineCap('round');
          this.brushContext.setLineJoin('round');
          this.updateBrushSettings();
        }
      } catch (error) {
        console.error('Error initializing canvas:', error);
      }
    },
    onBrushStart(e) {
      if (!this.brushContext) return;

      const touch = e.touches[0];
      this.isDrawing = true;
      this.lastX = touch.x;
      this.lastY = touch.y;

      this.brushContext.beginPath();
      this.brushContext.moveTo(this.lastX, this.lastY);
    },
    onBrushMove(e) {
      if (!this.isDrawing || !this.brushContext) return;

      const touch = e.touches[0];
      const currentX = touch.x;
      const currentY = touch.y;

      this.brushContext.beginPath();

      if (this.brushStyle === 'dashed' && !this.brushContext.setLineDash) {
        // Manual implementation of dashed lines if setLineDash is not supported
        this.drawDashedLine(this.lastX, this.lastY, currentX, currentY);
      } else {
        this.brushContext.moveTo(this.lastX, this.lastY);
        this.brushContext.lineTo(currentX, currentY);
      }

      this.brushContext.stroke();
      this.brushContext.draw(true);

      this.lastX = currentX;
      this.lastY = currentY;
    },
    onBrushEnd() {
      this.isDrawing = false;
    },
    closeMaterialPanel() {
      // this.showMaterialPanel = false;
      this.activeToolType = ''
    },
    selectMaterial(material) {
      // Get canvas dimensions if not already set
      if (!this.canvasWidth || !this.canvasHeight) {
        const query = uni.createSelectorQuery().in(this);
        query.select('.image-area').boundingClientRect(data => {
          if (data) {
            this.canvasWidth = data.width;
            this.canvasHeight = data.height;
          }
        }).exec();
      }

      // Default dimensions if canvas size is not available
      const defaultWidth = 200;
      const defaultHeight = 200;

      // Create a new layer with the selected material
      const newLayer = {
        image: material.image,
        name: material.name,
        x: (this.canvasWidth ? (this.canvasWidth / 2 - defaultWidth / 2) : 100),
        y: (this.canvasHeight ? (this.canvasHeight / 2 - defaultHeight / 2) : 100),
        width: defaultWidth,
        height: defaultHeight,
        rotation: 0,
        isSelected: true,
        visible: true,
        premium: material.premium || false
      };

      // Deselect all other layers
      this.materialLayers.forEach(layer => {
        layer.isSelected = false;
      });

      // Add the new layer
      this.materialLayers.push(newLayer);
      this.activeLayerIndex = this.materialLayers.length - 1;

      // Show toast and close panel
      uni.showToast({
        title: '已添加素材',
        icon: 'success'
      });
      this.closeMaterialPanel();
    },
    selectCategory(index) {
      this.currentCategory = index;
      this.currentSubcategory = 0; // Reset subcategory when changing category
    },
    selectSubcategory(index) {
      this.currentSubcategory = index;
    },
    closeTextPanel() {
      this.showTextPanel = false
    },
    applyTextChanges() {
      if (this.editingTextLayer && this.activeTextLayerIndex >= 0) {
        // Update existing text layer
        const layer = this.textLayers[this.activeTextLayerIndex];
        layer.text = this.currentText;
        layer.font = this.currentFont;
        layer.size = this.textSize;
        layer.color = this.currentColor;

        // Adjust layer size based on content if needed
        this.adjustTextLayerSize(layer);

        // Reset editing flag but keep the layer selected
        this.editingTextLayer = false;
      } else {
        // Calculate appropriate size based on text content
        const textLength = this.currentText.length;
        const estimatedWidth = Math.max(200, Math.min(textLength * this.textSize * 0.8, this.canvasWidth * 0.8));
        const estimatedHeight = Math.max(80, Math.min(this.textSize * 3, this.canvasHeight * 0.3));

        // Create a new text layer with proper sizing
        const newTextLayer = {
          type: 'text',
          text: this.currentText,
          font: this.currentFont,
          size: this.textSize,
          color: this.currentColor,
          x: this.canvasWidth ? (this.canvasWidth / 2 - estimatedWidth / 2) : 100,
          y: this.canvasHeight ? (this.canvasHeight / 2 - estimatedHeight / 2) : 100,
          width: estimatedWidth,
          height: estimatedHeight,
          rotation: 0,
          isSelected: true,
          visible: true
        };

        // Deselect all other layers
        this.textLayers.forEach(layer => {
          layer.isSelected = false;
        });
        this.materialLayers.forEach(layer => {
          layer.isSelected = false;
        });

        // Add the new text layer
        this.textLayers.push(newTextLayer);
        this.activeTextLayerIndex = this.textLayers.length - 1;
      }

      // Close the text panel and show success message
      this.activeToolType = '';
      uni.showToast({
        title: this.editingTextLayer ? '已更新文字' : '已添加文字',
        icon: 'success'
      });
    },
    selectFont(font) {
      this.currentFont = font;
    },
    changeTextSize(e) {
      this.textSize = e.detail.value;
      console.log(this.textLayers, 'layer.isSelected');

      this.textLayers.map(layer => {
        if (layer.isSelected) {
          layer.size = e.detail.value
        }
      })
    },
    selectColor(color) {
      this.currentColor = color;

      
      this.textLayers.map(layer => {
        if (layer.isSelected) {
          layer.color = color
        }
      })
    },
    changeBrushSize(e) {
      this.brushSize = e.detail.value;
      this.updateBrushSettings();
    },
    selectBrushColor(color) {
      this.brushColor = color;
      this.updateBrushSettings();
    },
    selectBrushStyle(style) {
      this.brushStyle = style;
      this.updateBrushSettings();
    },
    updateBrushSettings() {
      if (!this.brushContext) return;

      this.brushContext.setStrokeStyle(this.brushColor);
      this.brushContext.setLineWidth(this.brushSize);

      // Note: setLineDash may not be supported in all platforms
      // You may need to implement dashed lines manually if needed
      try {
        if (this.brushStyle === 'dashed') {
          this.brushContext.setLineDash([this.brushSize, this.brushSize * 2]);
        } else {
          this.brushContext.setLineDash([]);
        }
      } catch (error) {
        console.warn('setLineDash not supported, using solid lines only');
      }
    },
    drawDashedLine(x1, y1, x2, y2) {
      const dashLen = this.brushSize * 2;
      const gapLen = this.brushSize * 2;
      const deltaX = x2 - x1;
      const deltaY = y2 - y1;
      const numDashes = Math.floor(Math.sqrt(deltaX * deltaX + deltaY * deltaY) / (dashLen + gapLen));
      const dashX = deltaX / (numDashes * 2);
      const dashY = deltaY / (numDashes * 2);

      let q = 0;
      this.brushContext.moveTo(x1, y1);

      while (q++ < numDashes) {
        x1 += dashX;
        y1 += dashY;
        this.brushContext.lineTo(x1, y1);

        x1 += dashX;
        y1 += dashY;
        this.brushContext.moveTo(x1, y1);
      }

      this.brushContext.lineTo(x2, y2);
    },
    onLayerTouchStart(e, index) {
      console.log('Material layer touch start:', index);
      e.stopPropagation();
      
      // 选中当前图层，取消选中其他图层
      this.materialLayers.forEach((layer, i) => {
        layer.isSelected = (i === index);
      });
      this.textLayers.forEach(layer => {
        layer.isSelected = false;
      });
      
      this.activeLayerIndex = index;
      this.activeTextLayerIndex = -1;
      
      // 设置拖动状态
      this.isDragging = true;
      this.onLayerDirectDrag = true;
      
      // 记录起始触摸位置
      const touch = e.touches[0];
      this.startX = touch.clientX;
      this.startY = touch.clientY;
      
      // 记录图层初始位置和属性
      const layer = this.materialLayers[index];
      this.startLayerX = layer.x;
      this.startLayerY = layer.y;
      this.startWidth = layer.width;
      this.startHeight = layer.height;
      this.startRotation = layer.rotation;
      
      console.log('Touch start position:', this.startX, this.startY);
      console.log('Layer initial position:', this.startLayerX, this.startLayerY);
    },
    
    // 直接在图层上处理移动
    onLayerTouchMove(e, index) {
      if (!this.isDragging || this.activeLayerIndex !== index) return;
      if (this.isResizing || this.isRotating) return;
      
      e.stopPropagation();
      
      const touch = e.touches[0];
      const deltaX = touch.clientX - this.startX;
      const deltaY = touch.clientY - this.startY;
      
      const layer = this.materialLayers[index];
      
      // 计算新位置
      let newX = this.startLayerX + deltaX;
      let newY = this.startLayerY + deltaY;
      
      // 边界检查
      newX = Math.max(0, Math.min(newX, this.canvasWidth - layer.width));
      newY = Math.max(0, Math.min(newY, this.canvasHeight - layer.height));
      
      // 更新图层位置
      layer.x = newX;
      layer.y = newY;
      
      console.log('Moving layer to:', newX, newY);
    },
    
    // 结束拖动
    onLayerTouchEnd(e) {
      console.log('Layer touch end');
      e.stopPropagation();
      this.isDragging = false;
      this.isResizing = false;
      this.isRotating = false;
      this.onLayerDirectDrag = false;
    },
    
    // 旋转处理
    onRotationStart(e, index) {
      console.log('Rotation start for layer:', index);
      e.stopPropagation();
      
      this.isRotating = true;
      this.isDragging = false;
      this.isResizing = false;
      this.activeLayerIndex = index;
      
      const touch = e.touches[0];
      const layer = this.materialLayers[index];
      
      // 计算图层中心
      const centerX = layer.x + layer.width / 2;
      const centerY = layer.y + layer.height / 2;
      
      // 计算初始角度
      const angleRadians = Math.atan2(touch.clientY - centerY, touch.clientX - centerX);
      this.startRotation = layer.rotation;
      this.startAngle = angleRadians * (180 / Math.PI);
      
      console.log('Rotation start - center:', centerX, centerY);
      console.log('Start rotation:', this.startRotation, 'Start angle:', this.startAngle);
    },
    
    // 调整大小处理
    onResizeStart(e, index) {
      console.log('Resize start for layer:', index);
      e.stopPropagation();
      
      this.isResizing = true;
      this.isDragging = false;
      this.isRotating = false;
      this.activeLayerIndex = index;
      
      const touch = e.touches[0];
      this.startX = touch.clientX;
      this.startY = touch.clientY;
      
      const layer = this.materialLayers[index];
      this.startWidth = layer.width;
      this.startHeight = layer.height;
      
      console.log('Resize start - initial size:', this.startWidth, this.startHeight);
    },
    
    // Canvas触摸移动处理所有操作
    onCanvasTouchMove(e) {
      // 处理图层旋转
      if (this.isRotating && this.activeLayerIndex !== -1) {
        const touch = e.touches[0];
        const layer = this.materialLayers[this.activeLayerIndex];
        
        // 计算图层中心
        const centerX = layer.x + layer.width / 2;
        const centerY = layer.y + layer.height / 2;
        
        // 计算当前角度
        const angleRadians = Math.atan2(touch.clientY - centerY, touch.clientX - centerX);
        const currentAngle = angleRadians * (180 / Math.PI);
        
        // 计算角度差并更新旋转
        const angleDiff = currentAngle - this.startAngle;
        layer.rotation = (this.startRotation + angleDiff) % 360;
        
        console.log('Rotating layer to:', layer.rotation);
      }
      
      // 处理图层调整大小
      else if (this.isResizing && this.activeLayerIndex !== -1) {
        const touch = e.touches[0];
        const deltaX = touch.clientX - this.startX;
        const deltaY = touch.clientY - this.startY;
        
        const layer = this.materialLayers[this.activeLayerIndex];
        
        // 计算新尺寸（保持宽高比）
        const aspectRatio = this.startWidth / this.startHeight;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        const direction = deltaX > 0 || deltaY > 0 ? 1 : -1;
        const scaleFactor = 1 + (direction * distance * 0.01);
        
        let newWidth = this.startWidth * scaleFactor;
        let newHeight = this.startHeight * scaleFactor;
        
        // 最小尺寸限制
        newWidth = Math.max(50, newWidth);
        newHeight = Math.max(50, newHeight);
        
        // 更新图层尺寸
        layer.width = newWidth;
        layer.height = newHeight;
        
        console.log('Resizing layer to:', newWidth, newHeight);
      }
      
      // 处理图层拖动 - 这部分已经由onLayerTouchMove处理
      else if (this.isDragging && this.activeLayerIndex !== -1 && !this.onLayerDirectDrag) {
        const touch = e.touches[0];
        const deltaX = touch.clientX - this.startX;
        const deltaY = touch.clientY - this.startY;
        
        const layer = this.materialLayers[this.activeLayerIndex];
        
        // 计算新位置
        let newX = this.startLayerX + deltaX;
        let newY = this.startLayerY + deltaY;
        
        // 边界检查
        newX = Math.max(0, Math.min(newX, this.canvasWidth - layer.width));
        newY = Math.max(0, Math.min(newY, this.canvasHeight - layer.height));
        
        // 更新图层位置
        layer.x = newX;
        layer.y = newY;
      }
      
      // 处理文字图层...
      else if (this.activeTextLayerIndex !== -1) {
        // 现有的文字图层处理代码...
      }
    },
    
    onCanvasTouchEnd() {
      console.log('Canvas touch end - operations ended');
      this.isDragging = false;
      this.isResizing = false;
      this.isRotating = false;
      this.onLayerDirectDrag = false;
    },
    onResizeStart(e, index) {
      e.stopPropagation();

      this.isResizing = true;
      this.isDragging = false;
      this.activeLayerIndex = index;

      const touch = e.touches[0];
      this.startX = touch.clientX;
      this.startY = touch.clientY;

      const layer = this.materialLayers[index];
      this.startWidth = layer.width;
      this.startHeight = layer.height;
    },
    onRotationStart(e, index) {
      console.log(123123);
      
      e.stopPropagation();

      this.isRotating = true;
      this.isDragging = false;
      this.activeLayerIndex = index;

      const touch = e.touches[0];
      const layer = this.materialLayers[index];

      // Calculate center of the layer
      const centerX = layer.x + layer.width / 2;
      const centerY = layer.y + layer.height / 2;

      // Calculate initial angle
      const angleRadians = Math.atan2(touch.clientY - centerY, touch.clientX - centerX);
      this.startRotation = angleRadians * (180 / Math.PI) - layer.rotation;
    },
    deleteMaterialLayer(index) {
      // Remove the layer from materialLayers array
      this.materialLayers.splice(index, 1);

      // Update active layer index
      if (this.activeLayerIndex === index) {
        this.activeLayerIndex = -1;
      } else if (this.activeLayerIndex > index) {
        this.activeLayerIndex--;
      }

      uni.showToast({
        title: '已删除图层',
        icon: 'success'
      });
    },
    duplicateLayer(index) {
      const originalLayer = this.materialLayers[index];

      // Create a deep copy of the layer
      const duplicatedLayer = JSON.parse(JSON.stringify(originalLayer));

      // Modify position slightly to make it visible
      duplicatedLayer.x += 20;
      duplicatedLayer.y += 20;

      // Ensure the duplicated layer is selected
      this.materialLayers.forEach(layer => {
        layer.isSelected = false;
      });
      duplicatedLayer.isSelected = true;

      // Add the duplicated layer
      this.materialLayers.push(duplicatedLayer);
      this.activeLayerIndex = this.materialLayers.length - 1;

      uni.showToast({
        title: '已复制图层',
        icon: 'success'
      });
    },
    // Toggle material layer visibility
    toggleMaterialLayerVisibility(index) {
      if (index >= 0 && index < this.materialLayers.length) {
        // Toggle the visibility state
        this.materialLayers[index].visible = !this.materialLayers[index].visible;

        // Show appropriate toast message
        uni.showToast({
          title: this.materialLayers[index].visible ? '已显示图层' : '已隐藏图层',
          icon: 'none',
          duration: 1500
        });

        // Optional: If hiding the selected layer, you might want to keep it selected
        // but provide visual feedback that it's hidden
        if (this.materialLayers[index].isSelected && !this.materialLayers[index].visible) {
          // You could add special handling here if needed
          // For example, show a hint that the selected layer is hidden
        }
      }
    },
    // Text layer touch handlers - updated to sync text data to panel
    onTextLayerTouchStart(e, index) {
      console.log(e,index,'???');
      
      // Prevent default behavior
      e.stopPropagation();

      // Select this layer and deselect others
      this.textLayers.forEach((layer, i) => {
        layer.isSelected = (i === index);
      });
      this.materialLayers.forEach(layer => {
        layer.isSelected = false;
      });

      this.activeTextLayerIndex = index;
      this.isDragging = true;

      const touch = e.touches[0];
      this.startX = touch.clientX;
      this.startY = touch.clientY;

      // Store initial layer properties for potential operations
      const layer = this.textLayers[index];
      this.startWidth = layer.width;
      this.startHeight = layer.height;
      this.startRotation = layer.rotation;

      // Sync text data to editing panel
      this.syncTextDataToPanel(index);

      // Double tap detection for opening text editor
      const now = new Date().getTime();
      const timeSinceLastTouch = now - this.lastTouchTime;

      if (timeSinceLastTouch < 300) { // Double tap detected
        this.editTextLayer(index);
      }

      this.lastTouchTime = now;
    },

    // Sync text data to editing panel
    syncTextDataToPanel(index) {
      const layer = this.textLayers[index];

      // Update current text properties from the layer
      this.currentText = layer.text;
      this.currentFont = layer.font;
      this.textSize = layer.size;
      this.currentColor = layer.color;

      // Set editing flags but don't open panel yet
      this.editingTextLayer = true;
      this.activeTextLayerIndex = index;
    },

    // Handle canvas touch move for text layers
    onCanvasTouchMove(e) {
      // Handle material layer operations
      if (this.activeLayerIndex !== -1) {
        // Existing material layer code...
      }

      // Handle text layer operations
      if (this.activeTextLayerIndex !== -1) {
        // Handle text layer dragging
        if (this.isDragging && !this.isResizing && !this.isRotating) {
          const touch = e.touches[0];
          const deltaX = touch.clientX - this.startX;
          const deltaY = touch.clientY - this.startY;

          const layer = this.textLayers[this.activeTextLayerIndex];

          // Calculate new position
          let newX = layer.x + deltaX;
          let newY = layer.y + deltaY;

          // Boundary checking
          newX = Math.max(0, Math.min(newX, this.canvasWidth - layer.width));
          newY = Math.max(0, Math.min(newY, this.canvasHeight - layer.height));

          // Update layer position
          layer.x = newX;
          layer.y = newY;

          // Update start position for next move
          this.startX = touch.clientX;
          this.startY = touch.clientY;
        }

        // Handle text layer resizing
        if (this.isResizing) {
          const touch = e.touches[0];
          const deltaX = touch.clientX - this.startX;
          const deltaY = touch.clientY - this.startY;

          const layer = this.textLayers[this.activeTextLayerIndex];

          // Calculate new dimensions
          let newWidth = this.startWidth + deltaX;
          let newHeight = this.startHeight + deltaY;

          // Minimum size constraints
          newWidth = Math.max(50, newWidth);
          newHeight = Math.max(30, newHeight);

          // Maximum size constraints
          newWidth = Math.min(newWidth, this.canvasWidth - layer.x);
          newHeight = Math.min(newHeight, this.canvasHeight - layer.y);

          // Update layer dimensions
          layer.width = newWidth;
          layer.height = newHeight;
        }

        // Handle text layer rotation
        if (this.isRotating) {
          const touch = e.touches[0];
          const layer = this.textLayers[this.activeTextLayerIndex];

          // Calculate center of the layer
          const centerX = layer.x + layer.width / 2;
          const centerY = layer.y + layer.height / 2;

          // Calculate angle between center and touch point
          const angleRadians = Math.atan2(touch.clientY - centerY, touch.clientX - centerX);
          let angleDegrees = angleRadians * (180 / Math.PI);

          // Adjust angle based on starting rotation
          let newRotation = angleDegrees - this.startRotation;

          // Normalize rotation to 0-360 degrees
          newRotation = (newRotation + 360) % 360;

          // Update layer rotation
          layer.rotation = newRotation;
        }
      }
    },

    // Text layer resize start
    onTextResizeStart(e, index) {
      e.stopPropagation();

      this.isResizing = true;
      this.isDragging = false;
      this.activeTextLayerIndex = index;

      const touch = e.touches[0];
      this.startX = touch.clientX;
      this.startY = touch.clientY;

      const layer = this.textLayers[index];
      this.startWidth = layer.width;
      this.startHeight = layer.height;
    },

    // Text layer rotation start
    onTextRotationStart(e, index) {
      e.stopPropagation();

      this.isRotating = true;
      this.isDragging = false;
      this.activeTextLayerIndex = index;

      const touch = e.touches[0];
      const layer = this.textLayers[index];

      // Calculate center of the layer
      const centerX = layer.x + layer.width / 2;
      const centerY = layer.y + layer.height / 2;

      // Calculate initial angle
      const angleRadians = Math.atan2(touch.clientY - centerY, touch.clientX - centerX);
      this.startRotation = angleRadians * (180 / Math.PI) - layer.rotation;
    },

    // Delete text layer
    deleteTextLayer(index) {
      this.textLayers.splice(index, 1);

      if (this.activeTextLayerIndex === index) {
        this.activeTextLayerIndex = -1;
      } else if (this.activeTextLayerIndex > index) {
        this.activeTextLayerIndex--;
      }

      uni.showToast({
        title: '已删除文字图层',
        icon: 'success'
      });
    },

    // Edit text layer - open text panel with current text properties
    editTextLayer(index) {
      // Sync text data to panel
      this.syncTextDataToPanel(index);

      // Open text panel
      this.activeToolType = 'text';
    },

    // Duplicate text layer
    duplicateTextLayer(index) {
      const originalLayer = this.textLayers[index];

      // Create a deep copy of the layer
      const duplicatedLayer = JSON.parse(JSON.stringify(originalLayer));

      // Modify position slightly to make it visible
      duplicatedLayer.x += 20;
      duplicatedLayer.y += 20;

      // Ensure the duplicated layer is selected
      this.textLayers.forEach(layer => {
        layer.isSelected = false;
      });
      this.materialLayers.forEach(layer => {
        layer.isSelected = false;
      });

      duplicatedLayer.isSelected = true;

      // Add the duplicated layer
      this.textLayers.push(duplicatedLayer);
      this.activeTextLayerIndex = this.textLayers.length - 1;

      uni.showToast({
        title: '已复制文字图层',
        icon: 'success'
      });
    },

    // Toggle text layer visibility
    toggleTextLayerVisibility(index) {
      if (index >= 0 && index < this.textLayers.length) {
        this.textLayers[index].visible = !this.textLayers[index].visible;

        uni.showToast({
          title: this.textLayers[index].visible ? '已显示文字图层' : '已隐藏文字图层',
          icon: 'none',
          duration: 1500
        });
      }
    },

    // Select layer from panel (updated to handle text layers and sync data)
    selectLayerFromPanel(index, type = 'material') {
      if (type === 'material') {
        // Deselect all layers first
        this.materialLayers.forEach((layer, i) => {
          layer.isSelected = (i === index);
        });
        this.textLayers.forEach(layer => {
          layer.isSelected = false;
        });

        this.activeLayerIndex = index;
        this.activeTextLayerIndex = -1;
      } else if (type === 'text') {
        // Deselect all layers first
        this.textLayers.forEach((layer, i) => {
          layer.isSelected = (i === index);
        });
        this.materialLayers.forEach(layer => {
          layer.isSelected = false;
        });

        this.activeTextLayerIndex = index;
        this.activeLayerIndex = -1;

        // Sync text data to editing panel
        this.syncTextDataToPanel(index);
      }
    },

    // Adjust text layer size based on content
    adjustTextLayerSize(layer) {
      // Calculate appropriate size based on text content
      const textLength = layer.text.length;
      const lineCount = (layer.text.match(/\n/g) || []).length + 1;

      // Adjust width based on text length
      if (textLength > 0) {
        // Estimate width based on text length and font size
        const estimatedWidth = Math.max(200, Math.min(textLength * layer.size * 0.8, this.canvasWidth * 0.8));
        layer.width = estimatedWidth;
      }

      // Adjust height based on line count and font size
      if (lineCount > 1) {
        const estimatedHeight = Math.max(80, Math.min(lineCount * layer.size * 1.5, this.canvasHeight * 0.3));
        layer.height = estimatedHeight;
      }
    },

    // Select tool with proper initialization
    selectTool(tool) {
      this.activeToolType = tool;

      if (tool === 'text') {
        // Reset text properties when selecting text tool
        if (!this.editingTextLayer) {
          this.currentText = "示例文字";
          this.currentFont = "sans-serif";
          this.textSize = 40;
          this.currentColor = "#ff0000"; // Default to red for better visibility
        }

        // Make sure canvas dimensions are initialized
        if (!this.canvasWidth || !this.canvasHeight) {
          this.initCanvas();
        }
      }
    },

    // Duplicate text layer with proper sizing
    duplicateTextLayer(index) {
      const originalLayer = this.textLayers[index];

      // Create a deep copy of the layer
      const duplicatedLayer = JSON.parse(JSON.stringify(originalLayer));

      // Modify position slightly to make it visible
      duplicatedLayer.x += 20;
      duplicatedLayer.y += 20;

      // Ensure the duplicated layer is selected
      this.textLayers.forEach(layer => {
        layer.isSelected = false;
      });
      this.materialLayers.forEach(layer => {
        layer.isSelected = false;
      });

      duplicatedLayer.isSelected = true;

      // Add the duplicated layer
      this.textLayers.push(duplicatedLayer);
      this.activeTextLayerIndex = this.textLayers.length - 1;

      uni.showToast({
        title: '已复制文字图层',
        icon: 'success'
      });
    }
  }
}
</script>

<style>
.editor-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background-color: #1a1a1a;
  display: flex;
  flex-direction: column;
}

.top-bar {
  display: flex;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #1a1a1a;
}

.back-btn,
.save-btn {
  color: #b6ff00;
  font-size: 32rpx;
  padding: 10rpx 20rpx;
}

.save-btn {
  border: 1px solid #b6ff00;
  border-radius: 8rpx;
}

.image-area {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background-color: #1a1a1a;
}

.transparent-bg {
  width: 100%;
  height: 55%;
  background-image:
    linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
    linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
    linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
  background-size: 10px 10px;
  background-position: 0 0, 0 5px, 5px -5px, -5px 0px;
  background-color: #ffffff;
}

.bottom-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 20rpx;
  background-color: #1a1a1a;
}

.toolbar-controls {
  display: flex;
}

.control-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10rpx;
}

.icon {
  color: #ffffff;
  font-size: 40rpx;
}

.layer-btn {
  background-color: #333333;
  padding: 10rpx 30rpx;
  border-radius: 8rpx;
}

.layer-text {
  color: #ffffff;
  font-size: 28rpx;
}

.tools-panel {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  background-color: #000000;
}

.tool-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 16.66%;
}

.tool-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.material-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
}

.template-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14h-2v-4H8v-2h4V7h2v4h4v2h-4v4z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
}

.text-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M5 4v3h5.5v12h3V7H19V4z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
}

.mark-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
}

.brush-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M7 14c-1.66 0-3 1.34-3 3 0 1.31-1.16 2-2 2 .92 1.22 2.49 2 4 2 2.21 0 4-1.79 4-4 0-1.66-1.34-3-3-3zm13.71-9.37l-1.34-1.34c-.39-.39-1.02-.39-1.41 0L9 12.25 11.75 15l8.96-8.96c.39-.39.39-1.02 0-1.41z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
}

.add-image-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19 7v2.99s-1.99.01-2 0V7h-3s.01-1.99 0-2h3V2h2v3h3v2h-3zm-3 4V8h-3V5H5c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2v-8h-3zM5 19l3-4 2 3 3-4 4 5H5z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
}

.tool-text {
  color: #ffffff;
  font-size: 24rpx;
}

.brush-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  pointer-events: auto;
}

/* Layer panel styles */
.layer-panel {
  position: absolute;
  right: 0;
  top: 80px;
  /* bottom: 0; */
  width: 300rpx;
  background-color: #222222;
  z-index: 100;
  display: flex;
  flex-direction: column;
  border-left: 1px solid #333333;
}

.apply-panel {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
}

.layer-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1px solid #333333;
}

.layer-tabs {
  display: flex;
  flex: 1;
}

.layer-tab {
  padding: 10rpx 20rpx;
  color: #999999;
  font-size: 24rpx;
}

.layer-tab.active {
  color: #ffffff;
  border-bottom: 2px solid #b6ff00;
}

.close-panel {
  color: #ffffff;
  font-size: 40rpx;
  padding: 0 10rpx;
}

.layer-list {
  flex: 1;
  overflow-y: auto;
}

.layer-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1px solid #333333;
}

.layer-preview {
  width: 60rpx;
  height: 60rpx;
  background-color: #333333;
  margin-right: 10rpx;
  border: 1px solid #444444;
}

.layer-preview image {
  width: 100%;
  height: 100%;
}

.layer-info {
  flex: 1;
}

.layer-name {
  color: #ffffff;
  font-size: 24rpx;
}

.layer-visibility {
  width: 40rpx;
  text-align: center;
}

.visibility-icon {
  font-size: 24rpx;
}

.layer-panel-footer {
  padding: 20rpx;
  border-top: 1px solid #333333;
}

.layer-action {
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  color: #b6ff00;
  font-size: 28rpx;
  margin-right: 10rpx;
}

.action-text {
  color: #b6ff00;
  font-size: 24rpx;
}

/* Material panel styles */
.material-panel {
  position: absolute;
  /* top: 0; */
  left: 0;
  /* right: 0; */
  bottom: 120rpx;
  background-color: #000000;
  z-index: 200;
  display: flex;
  flex-direction: column;
}

.material-search {
  padding: 20rpx;
}

.search-categories {
  margin-bottom: 20rpx;
  max-width: 750rpx;
}

.category-scroll {
  white-space: nowrap;
  padding: 10rpx 0;
}

.category-item {
  display: inline-block;
  padding: 10rpx 30rpx;
  color: #ffffff;
  font-size: 28rpx;
  transition: all 0.3s;
}

.category-item.active {
  color: #b6ff00;
  border-bottom: 4rpx solid #b6ff00;
}

.subcategories {
  display: flex;
  flex-direction: column;
  background-color: #222222;
  border-radius: 10rpx;
}

.subcategory-item {
  padding: 20rpx;
  color: #ffffff;
  font-size: 26rpx;
  border-bottom: 1px solid #333333;
  transition: all 0.3s;
}

.subcategory-item.active {
  color: #b6ff00;
  background-color: #333333;
}

.material-grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10rpx;
  padding: 10rpx;
  overflow-y: auto;
  max-width: 630rpx;
}

.material-item {
  position: relative;
  background-color: #222222;
  border-radius: 10rpx;
  overflow: hidden;
  aspect-ratio: 1;
}

.material-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.material-name {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10rpx;
  background-color: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  font-size: 22rpx;
  text-align: center;
}

.premium-icon {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  color: #ffcc00;
  font-size: 30rpx;
}

.material-panel-close {
  position: absolute;
  bottom: -80rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 80rpx;
  background-color: #333333;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.close-icon {
  color: #ffffff;
  font-size: 50rpx;
}

/* Text panel styles */
.text-panel {
  position: absolute;
  /* top: 0; */
  left: 0;
  /* right: 0; */
  bottom: 120rpx;
  background-color: #333333;
  z-index: 200;
  display: flex;
  flex-direction: column;
  padding: 0;
}

.text-panel-header {
  display: flex;
  width: 77%;
  /* margin: auto; */
  /* justify-content: flex-end; */
  align-items: center;
  justify-content: space-between;
  height: 40px;
  /* padding: 20rpx; */
}

.close-panel {
  color: #ffffff;
  font-size: 40rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.text-input-area {
  background-color: #222222;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.text-input {
  width: 100%;
  min-height: 80rpx;
  color: #ffffff;
  font-size: 32rpx;
}

.text-font-options {
  padding: 0 20rpx;
  margin-bottom: 30rpx;
}

.font-scroll {
  white-space: nowrap;
}

.font-item {
  display: inline-block;
  padding: 15rpx 30rpx;
  margin-right: 10rpx;
  background-color: #444444;
  color: #ffffff;
  font-size: 28rpx;
  border-radius: 8rpx;
}

.font-item.active {
  background-color: #b6ff00;
  color: #000000;
}

.text-size-control {
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  margin-bottom: 30rpx;
}

.size-label {
  color: #ffffff;
  font-size: 28rpx;
  margin-right: 20rpx;
  width: 80rpx;
}

.size-slider {
  /* flex: 1; */
  width: 50%;
}

.size-value {
  color: #ffffff;
  font-size: 28rpx;
  margin-left: 20rpx;
  width: 60rpx;
  text-align: right;
}

.text-color-options {
  padding: 0 20rpx;
  margin-bottom: 20rpx;
}

.color-label {
  color: #ffffff;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  display: block;
}

.color-grid {
  display: flex;
  flex-wrap: nowrap;
}

/* Brush panel styles */
.brush-panel {
  position: absolute;
  /* top: 0; */
  left: 0;
  /* right: 0; */
  bottom: 120rpx;
  background-color: #333333;
  z-index: 200;
  display: flex;
  flex-direction: column;
  padding: 20rpx;
}

.brush-size-control {
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  margin-bottom: 30rpx;
}

.size-label {
  color: #ffffff;
  font-size: 28rpx;
  margin-right: 20rpx;
  width: 80rpx;
}

/* .size-slider {
  flex: 1;
} */

.size-value {
  color: #ffffff;
  font-size: 28rpx;
  margin-left: 20rpx;
  width: 60rpx;
  text-align: right;
}

.brush-color-options {
  padding: 0 20rpx;
  margin-bottom: 30rpx;
}

.color-label {
  color: #ffffff;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  display: block;
}

.color-scroll {
  width: 100%;
  white-space: nowrap;
}

.color-grid {
  display: inline-flex;
  padding: 10rpx 0;
}

.color-item {
  flex-shrink: 0;
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  /* border: 2rpx solid transparent; */
}

.color-item.active {
  border: 10rpx solid #ffffff;
  width: 30rpx;
  height: 30rpx;
}

.brush-style-options {
  padding: 0 20rpx;
  margin-bottom: 30rpx;
}

.style-label {
  color: #ffffff;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  display: block;
}

.style-grid {
  display: flex;
}

.style-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 40rpx;
}

.style-line {
  width: 100rpx;
  height: 6rpx;
  margin-bottom: 10rpx;
}

.solid-line {
  background-color: #b6ff00;
}

.dashed-line {
  background: repeating-linear-gradient(to right,
      #ffffff 0%,
      #ffffff 40%,
      transparent 40%,
      transparent 100%);
}

.style-text {
  color: #ffffff;
  font-size: 24rpx;
}

.style-item.active .style-text {
  color: #b6ff00;
}

/* Material layer styles */
.material-layer {
  position: absolute;
  box-sizing: border-box;
  transform-origin: center center;
  user-select: none;
  touch-action: none;
  cursor: move;
  overflow: visible;
  transition: opacity 0.2s ease;
}

.material-layer.selected {
  z-index: 100 !important;
}

.layer-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  pointer-events: none;
  user-drag: none;
  -webkit-user-drag: none;
}

/* Text layer styles */
.text-layer {
  box-sizing: border-box;
  transform-origin: center center;
  user-select: none;
  touch-action: none;
  cursor: move;
  overflow: visible;
  transition: opacity 0.2s ease;
  background-color: transparent;
}

.text-layer.selected {
  border: none;
  /* Border is handled by the layer-border element */
}

.text-content-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  padding: 4px;
  box-sizing: border-box;
}

.text-content {
  width: 100%;
  text-align: center;
  pointer-events: none;
  user-select: none;
  word-break: break-word;
  white-space: pre-wrap;
  display: block;
}

/* Layer border and control points */
.layer-border {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px dashed #00ff00;
  pointer-events: none;
}

.control-point {
  position: absolute;
  width: 30px;
  height: 30px;
  background-color: #ffffff;
  border: 2px solid #00ff00;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto;
  z-index: 2;
  transform: translate(-50%, -50%);
}

.control-icon {
  font-size: 18px;
  color: #333;
  pointer-events: none;
}

/* Control point positions */
.top-left {
  top: 0;
  left: 0;
}

.top-center {
  top: 0;
  left: 50%;
}

.top-right {
  top: 0;
  right: 0;
  transform: translate(50%, -50%);
}

.middle-left {
  top: 50%;
  left: 0;
}

.middle-right {
  top: 50%;
  right: 0;
  transform: translate(50%, -50%);
}

.bottom-left {
  bottom: 0;
  left: 0;
  transform: translate(-50%, 50%);
}

.bottom-center {
  bottom: 0;
  left: 50%;
  transform: translate(-50%, 50%);
}

.bottom-right {
  bottom: 0;
  right: 0;
  transform: translate(50%, 50%);
}

/* Special control points */
.rotate-point {
  background-color: #ffcc00;
}

.resize-point {
  background-color: #00ccff;
}

.delete-point {
  background-color: #ff3333;
}

.duplicate-point {
  background-color: #33cc33;
}

/* Animation for layer selection */
@keyframes pulse-border {
  0% {
    box-shadow: 0 0 0 0 rgba(126, 211, 33, 0.4);
  }

  70% {
    box-shadow: 0 0 0 5px rgba(126, 211, 33, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(126, 211, 33, 0);
  }
}

.material-layer.selected .layer-border {
  animation: pulse-border 2s infinite;
}

/* Hover effects for control points */
.control-point:hover {
  transform: translate(-50%, -50%) scale(1.1);
}

/* Transparent background for the image area */
.image-area {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: transparent;
  overflow: hidden;
}

.transparent-bg {
  background-image:
    linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
    linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
    linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  background-color: #ffffff;
}

/* Ensure the layer is above the background */
.material-layer {
  z-index: 10;
}

.material-layer.selected {
  z-index: 100;
}
</style>



