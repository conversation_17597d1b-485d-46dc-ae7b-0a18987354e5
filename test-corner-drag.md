# 四角拖动裁剪功能实现说明

## 功能概述
已成功实现四个角的拖动裁剪功能，主要特点：

1. **四个角拖动点**: 在 `editLine == 1` 模式下，图片四个角会显示蓝色拖动点
2. **裁剪而非缩放**: 拖动角点时，图片本身大小不变，只是改变显示的裁剪区域
3. **最大限制**: 裁剪区域最大不能超过图片原本大小
4. **最小限制**: 裁剪区域最小为 20px

## 实现细节

### 模板修改
- 在 `editLine == 1` 模式下添加了四个角的拖动点
- 每个角点都有独立的事件处理：`touchstart`、`touchmove`、`touchend`

### 方法实现

#### `handleCornerStart(e, it, corner)`
- 初始化拖拽数据
- 记录原始图片尺寸和当前裁剪状态
- 保存触摸起始位置

#### `handleTouchs(e, it, direction)` (修改后)
- 处理四个角的拖动逻辑
- 根据拖拽方向调整裁剪区域
- 实现边界限制和尺寸约束

#### `handleCornerEnd(e, it)`
- 清理拖拽临时数据
- 防止内存泄漏

### CSS样式
- 四个角的拖动点使用蓝色背景 (`#007aff`)
- 尺寸为 14x14px，比普通圆点稍大
- 添加阴影效果和按压反馈

## 使用方法

1. 确保图片处于 `editLine == 1` 模式
2. 图片四个角会显示蓝色拖动点
3. 拖动任意角点即可调整裁剪区域
4. 裁剪区域会受到以下限制：
   - 最小尺寸：20px
   - 最大尺寸：图片原始尺寸
   - 不能超出容器边界

## 技术要点

- 使用 `_cropDrag` 临时对象存储拖拽状态
- 通过 `originalWidth` 和 `originalHeight` 记录图片原始尺寸
- 实现了四个角的不同拖拽逻辑
- 保持了与原有边框拖动功能的兼容性
