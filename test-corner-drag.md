# 四角拖动裁剪功能实现说明 (已修复左下角问题)

## 功能概述
已成功实现四个角的拖动裁剪功能，主要特点：

1. **四个角拖动点**: 在 `editLine == 1` 模式下，图片四个角会显示蓝色拖动点
2. **裁剪而非缩放**: 拖动角点时，图片本身大小不变，只是改变显示的裁剪区域
3. **最大限制**: 裁剪区域最大不能超过图片原本大小
4. **最小限制**: 裁剪区域最小为 20px
5. **锚点固定**: 每个角的拖动都以对角作为固定锚点

## 修复的问题

### 左下角拖动修复
- **问题**: 之前左下角拖动时Y坐标处理不正确
- **修复**: 重新设计了锚点逻辑，确保每个角都有正确的固定点
- **效果**: 现在左下角拖动时，右上角保持固定，只调整左边和下边

## 四个角的拖动逻辑

### 1. 左上角 (top-left)
- **固定点**: 右下角
- **调整**: 左边界和上边界
- **效果**: 向内拖动缩小裁剪区域，向外拖动扩大裁剪区域

### 2. 右上角 (top-right)
- **固定点**: 左下角
- **调整**: 右边界和上边界
- **效果**: 水平向右、垂直向上调整裁剪区域

### 3. 左下角 (bottom-left) ✅ 已修复
- **固定点**: 右上角
- **调整**: 左边界和下边界
- **效果**: 水平向左、垂直向下调整裁剪区域

### 4. 右下角 (bottom-right)
- **固定点**: 左上角
- **调整**: 右边界和下边界
- **效果**: 向外拖动扩大裁剪区域

## 技术实现

### 核心算法
```javascript
// 以左下角为例
case 'bottom-left':
    const anchorRightX = startPosX + startWidth;  // 右边固定
    const anchorTopY = startPosY;                 // 上边固定

    newX = Math.max(0, startPosX + deltaX);       // 左边可调整
    newY = anchorTopY;                            // 上边保持不变
    newWidth = anchorRightX - newX;               // 宽度根据左边调整
    newHeight = Math.min(maxHeight, startHeight + deltaY); // 高度可调整
```

### 边界处理
- 最小尺寸限制：20px
- 最大尺寸限制：原图尺寸
- 容器边界限制：不超出外层容器
- 锚点重新计算：确保固定点始终正确

## 测试建议

1. **左下角测试**: 拖动左下角，确认右上角保持固定
2. **边界测试**: 拖动到容器边界，确认不会超出
3. **最小尺寸测试**: 拖动到很小，确认不会小于20px
4. **最大尺寸测试**: 拖动到很大，确认不会超过原图尺寸
