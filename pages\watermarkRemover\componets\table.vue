<template>
    <view class="containerff">

        <!-- 文字工具弹出面板 -->
        <view class="text-panel" v-if="currentTool === 'text'" @click.stop>
            <!-- 面板头部 -->
            <view class="panel-header">
                <text class="panel-title">示例文字</text>
                <view style="display: flex;">
                    <view class="panel-close" style="margin-right: 10px;" @click="selectScaleItem('text')">
                        <text class="close-icon">√</text>
                    </view>
                    <view class="panel-close" @click="closeTextPanel">
                        <text class="close-icon">×</text>
                    </view>
                </view>
            </view>
            <view class="text-input-area font-section">
                <input type="text" class="textInput" v-model="currentText" placeholder="示例文字" />
                <view class="fontWeights" @click="Weights = !Weights" :class="{ backgroundColors: Weights }">I</view>
            </view>
            <!-- 字体选择区域 -->
            <view class="font-section">
                <view class="font-buttons">

                    <view class="color-pos">
                        <view class="font-btn" v-for="(it, index) in selectedFontOries" :key="index"
                            :class="{ active: selectedFont === index }" @click="selectFont(index)">
                            <text class="font-text">{{ it.name }}</text>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 字体大小调节 -->
            <view class="size-section">
                <!-- <text class="section-label">大小</text> -->
                <view class="size-control">
                    <slider class="size-slider" :value="fontSize" min="12" max="34" @change="onFontSizeChange" />
                    <text class="size-value">{{ fontSize }}</text>
                </view>
            </view>

            <!-- 颜色选择区域 -->
            <view class="color-section">
                <text class="section-label">颜色</text>
                <view class="color-palette">
                    <view class="color-pos">
                        <view class="color-item" v-for="(color, index) in colorPalette" :key="index"
                            :style="{ backgroundColor: color }" :class="{ active: selectedColor === color }"
                            @click="selectColor(color)">
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- <view class="text-panel" v-if="showTextPanel" @click.stop>

        </view> -->

        <!-- 模板面板 -->
        <view class="scale-panel" v-if="currentTool === 'scale'" @click.stop>
            <view class="panel-header">
                <text class="panel-title">模板</text>
                <view class="panel-close" @click="closeTextPanel">
                    <text class="close-icon">×</text>
                </view>
            </view>
            <!-- 搜索栏 -->
            <view class="search-section">
                <view class="search-bar">
                    <text class="search-icon">🔍</text>
                    <input class="search-input" type="text" v-model="searchText" placeholder="搜索" />
                </view>
                <view class="filter-tabs">
                    <view class="color-pos">
                        <view class="filter-tab" :class="{ active: activeFilter === 'all' }" @click="setFilter('all')">
                            <text class="filter-text">全部</text>
                        </view>
                        <view class="filter-tab" :class="{ active: activeFilter === 'indoor' }"
                            @click="setFilter('indoor')">
                            <text class="filter-text">室内装修</text>
                        </view>
                        <view class="filter-tab" :class="{ active: activeFilter === 'combine' }"
                            @click="setFilter('combine')">
                            <text class="filter-text">组合</text>
                        </view>
                        <view class="filter-tab" :class="{ active: activeFilter === 'furniture' }"
                            @click="setFilter('furniture')">
                            <text class="filter-text">家具装饰</text>
                        </view>
                        <view class="filter-tab" :class="{ active: activeFilter === 'garden' }"
                            @click="setFilter('garden')">
                            <text class="filter-text">花园景观</text>
                        </view>
                        <view class="filter-tab" :class="{ active: activeFilter === 'other' }"
                            @click="setFilter('other')">
                            <text class="filter-text">其他装饰</text>
                        </view>
                        <view class="filter-tab" :class="{ active: activeFilter === 'other' }"
                            @click="setFilter('other')">
                            <text class="filter-text">其他装饰</text>
                        </view>
                        <view class="filter-tab" :class="{ active: activeFilter === 'other' }"
                            @click="setFilter('other')">
                            <text class="filter-text">其他装饰</text>
                        </view>
                        <view class="filter-tab" :class="{ active: activeFilter === 'other' }"
                            @click="setFilter('other')">
                            <text class="filter-text">其他装饰</text>
                        </view>
                        <view class="filter-tab" :class="{ active: activeFilter === 'other' }"
                            @click="setFilter('other')">
                            <text class="filter-text">其他装饰</text>
                        </view>
                        <view class="filter-tab" :class="{ active: activeFilter === 'other' }"
                            @click="setFilter('other')">
                            <text class="filter-text">其他装饰</text>
                        </view>
                        <view class="filter-tab" :class="{ active: activeFilter === 'other' }"
                            @click="setFilter('other')">
                            <text class="filter-text">其他装饰</text>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 内容卡片区域 -->
            <view class="content-gridss">
                <view class="content-card" v-for="(item, index) in filteredItems" :key="index"
                    @click="selectScaleItem(item)">
                    <image class="card-image" :src="item.image" mode="aspectFit"></image>
                    <view class="card-overlay">
                        <text class="card-title">{{ item.title }}</text>
                        <view class="card-stats">
                            <text class="stats-icon">👁</text>
                            <text class="stats-text">{{ item.views }}</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 画笔面板 -->
        <view class="brush-panel" v-if="currentTool === 'brush'" @click.stop>

            <view class="panel-header">
                <text class="panel-title">画笔</text>
                <view class="panel-close" @click="closeTextPanel">
                    <text class="close-icon">×</text>
                </view>
            </view>
            <!-- 粗细调节 -->
            <view class="brush-section">
                <view class="slider-container">
                    <slider class="brush-slider" :value="brushSize" min="1" max="20" @change="onBrushSizeChange"
                        activeColor="#4CAF50" backgroundColor="#555" block-color="#fff" block-size="20" />

                    <text class="section-value">{{ brushSize }}</text>
                </view>
            </view>

            <!-- 颜色选择 -->
            <view class="brush-section">
                <text class="section-title">颜色</text>
                <view class="color-palette">

                    <view class="color-pos">
                        <view class="color-item" v-for="(color, index) in brushColors" :key="index"
                            :style="{ backgroundColor: color }" :class="{ active: selectedBrushColor === color }"
                            @click="selectBrushColor(color)">
                            <text class="color-check" v-if="selectedBrushColor === color">✓</text>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 笔状选择 -->
            <view class="brush-section">
                <text class="section-title">笔状</text>
                <view class="brush-type-container">
                    <view class="brush-type-item" :class="{ active: selectedBrushType === 'solid' }"
                        @click="selectBrushType('solid')">
                        <view class="brush-type-line solid-line"></view>
                        <!-- <text class="brush-type-label">实线</text> -->
                    </view>
                    <view class="brush-type-item" :class="{ active: selectedBrushType === 'dashed' }"
                        @click="selectBrushType('dashed')">
                        <view class="brush-type-line dashed-line"></view>
                        <!-- <text class="brush-type-label">虚线</text> -->
                    </view>
                </view>
            </view>
        </view>

        <view class="brush-panel" v-if="currentTool === 'edit'" @click.stop>
            <view class="panel-header">
                <text class="panel-title">编辑</text>
                <view class="panel-close" @click="closeTextPanel">
                    <text class="close-icon">×</text>
                </view>
            </view>

            <view class="edits">
                <view class="tool-btn" v-for="(it, index) in fuls" :key="index"
                    :class="{ active: currenEinet === it.id }" @click="enitsd(it)">
                    <view class="tool-icon-box">
                        <text class="tool-icon">{{ it.icon }}</text>
                    </view>
                    <text class="tool-label">{{ it.name }}</text>
                </view>
            </view>
        </view>

        <!-- 素材面板 -->
        <view class="copy-panel" v-if="currentTool == 'copy'" @click.stop>

            <view class="panel-header">
                <text class="panel-title">素材</text>
                <view class="panel-close" @click="closeTextPanel">
                    <text class="close-icon">×</text>
                </view>
            </view>
            <!-- 搜索栏 -->
            <view class="copy-search-section">
                <view class="copy-search-bar">
                    <text class="copy-search-icon">🔍</text>
                    <input class="copy-search-input" type="text" v-model="copySearchText" placeholder="搜索" />
                </view>
            </view>

            <!-- 分类标签 -->
            <view class="filter-tabs">
                <view class="color-pos">
                    <view class="copy-category-tab" :class="{ active: activeCopyCategory === category.key }"
                        v-for="category in copyCategories" :key="category.key" @click="setCopyCategory(category.key)">
                        <text class="filter-text">{{ category.name }}</text>
                    </view>
                </view>
            </view>

            <!-- 内容网格 -->
            <view class="content-box">
                <view class="content-left">
                    <view v-for="(item, index) in copyCategories" :key="index"
                        :style="activeCopyOff == index ? 'color: #4CAF50' : ''" @click="activeCopyOff = index"
                        class="content-color">
                        {{ item.name }}
                    </view>
                </view>
                <view class="content-grids">
                    <view class="content-card" v-for="(item, index) in filteredCopyItems" :key="index"
                        @click="selectScaleItem(item)">
                        <image class="card-image" :src="item.image" mode="aspectFit"></image>
                        <view class="card-overlay">
                            <text class="card-title">{{ item.title }}</text>
                            <view class="card-stats">
                                <text class="stats-icon">👁</text>
                                <text class="stats-text">{{ item.views }}</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 遮罩层 -->
        <!-- <view class="panel-mask" v-if="showTextPanel || showScalePanel || showBrushPanel || showCopyPanel"
            @click="closePanels"></view> -->

        <!-- 底部工具栏 -->
        <view class="toolbars">
            <view class="tool-btn" :class="{ active: currentTool === 'copy' }" @click="selectTool('copy')">
                <view class="tool-icon-box">
                    <text class="tool-icon">📋</text>
                </view>
                <text class="tool-label">素材</text>
            </view>

            <view class="tool-btn" :class="{ active: currentTool === 'scale' }" @click="selectTool('scale')">
                <view class="tool-icon-box">
                    <text class="tool-icon">🔍</text>
                </view>
                <text class="tool-label">模板</text>
            </view>

            <view class="tool-btn" :class="{ active: currentTool === 'text' }" @click="selectTool('text')">
                <view class="tool-icon-box">
                    <text class="tool-text-icon">文</text>
                </view>
                <text class="tool-label">文字</text>
            </view>

            <view class="tool-btn" :class="{ active: currentTool === 'edit' }" @click="selectTool('edit')">
                <view class="tool-icon-box">
                    <text class="tool-text-icon">编</text>
                </view>
                <text class="tool-label">编辑</text>
            </view>

            <view class="tool-btn" :class="{ active: currentTool === 'brush' }" @click="selectTool('brush')">
                <view class="tool-icon-box">
                    <text class="tool-icon">🖌</text>
                </view>
                <text class="tool-label">画笔</text>
            </view>

            <view class="tool-btn" :class="{ active: currentTool === 'add' }" @click="selectTool('add')">
                <view class="tool-icon-box">
                    <text class="tool-icon">➕</text>
                </view>
                <text class="tool-label">上传</text>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            Weights: false,
            currentTool: '',
            activeCopyOff: 0,
            selectedFont: 0,
            fontSize: 14,
            selectedColor: '#8BC34A',
            currentText: '',
            searchText: '',
            activeFilter: 'all',
            // 画笔相关数据
            brushSize: 2,
            selectedBrushColor: '#4CAF50',
            selectedBrushType: 'solid',
            brushColors: [
                '#4CAF50', // 绿色 (默认选中)
                '#9E9E9E', // 灰色
                '#757575', // 深灰色
                '#424242', // 更深灰色
                '#212121', // 黑色
                '#FFEB3B', // 黄色
                '#FF9800', // 橙色
                '#FF5722', // 深橙色
                '#F44336'  // 红色
            ],
            // 复制面板相关数据
            copySearchText: '',
            activeCopyCategory: 'favorite',
            copyCategories: [
                { key: 'favorite', name: '收藏' },
                { key: 'myMaterial', name: '我的素材' },
                { key: 'artPot', name: '艺术盆栽' },
                { key: 'flowerPot', name: '花卉盆栽' },
                { key: 'shapePlant', name: '造型植物' },
                { key: 'craftDecor', name: '工艺装饰' }
            ],
            selectedFontOries: [
                { name: "宋体", key: '"SimSun", serif' },
                { name: "微软雅黑", key: '"Microsoft YaHei", sans-serif' },
                { name: "黑体", key: '"SimHei", sans-serif' },
                { name: "楷体", key: '"KaiTi", serif' },
                { name: "仿宋", key: '"FangSong", serif' },
                { name: "Arial", key: '"Arial", sans-serif' },
                { name: "Helvetica", key: '"Helvetica", sans-serif' },
                { name: "Times New Roman", key: '"Times New Roman", serif' },
                { name: "Georgia", key: '"Georgia", serif' },
                { name: "Courier New", key: '"Courier New", monospace' },
                { name: "Verdana", key: '"Verdana", sans-serif' },
                { name: "Tahoma", key: '"Tahoma", sans-serif' },
                { name: "华文宋体", key: '"STSong", serif' },
                { name: "华文黑体", key: '"STHeiti", sans-serif' },
                { name: "华文楷体", key: '"STKaiti", serif' },
                { name: "Consolas", key: '"Consolas", monospace' },
                { name: "苹方", key: '"PingFang SC", sans-serif' },
                { name: "通用无衬线", key: 'sans-serif' }
            ],
            fuls: [
                {
                    name: '裁剪',
                    icon: '裁',
                    id: 1,
                },
                {
                    name: '变形',
                    icon: '形',
                    id: 2,
                },
            ],
            currenEinet: 1,
            copyItems: [
                // 艺术盆栽
                {
                    id: 1,
                    title: '宝莲灯盆栽',
                    image: 'https://c-ssl.duitang.com/uploads/blog/202201/07/20220107201202_cc681.jpg',
                    category: 'artPot'
                },
                {
                    id: 2,
                    title: '玉兰灯盆栽',
                    image: 'https://ts3.tc.mm.bing.net/th/id/OIP-C.3vN88r7hCrYG33J2aTPPmAHaNK?rs=1&pid=ImgDetMain&o=7&rm=3',
                    category: 'artPot'
                },
                {
                    id: 3,
                    title: '宝莲灯盆栽',
                    image: 'https://tse3-mm.cn.bing.net/th/id/OIP-C.3qI2PjjJqsRdXaPWF90DsgHaNK?o=7rm=3&rs=1&pid=ImgDetMain&o=7&rm=3',
                    category: 'artPot'
                },
                // 花卉盆栽
                {
                    id: 4,
                    title: '北美冬青盆栽',
                    image: 'https://bpic.588ku.com/photo_water_img/24/03/20/995cf0b8d89b5eda394b76e0f4f0578a.jpg!/fw/351/quality/100/unsharp/true/compress/true',
                    category: 'flowerPot'
                },
                {
                    id: 5,
                    title: '北美冬青盆栽',
                    image: 'https://img.shetu66.com/zt/1661509292582_be304c32.jpg',
                    category: 'flowerPot'
                }
            ],
            colorPalette: [
                '#8BC34A', // 绿色 (选中状态)
                '#9E9E9E', // 灰色
                '#757575', // 深灰色
                '#424242', // 更深灰色
                '#212121', // 黑色
                '#FFEB3B', // 黄色
                '#FF9800', // 橙色
                '#FF5722', // 深橙色
                '#F44336'  // 红色
            ],
            scaleItems: [
                {
                    id: 1,
                    title: '新 庭院造景',
                    image: 'https://c-ssl.duitang.com/uploads/blog/202201/07/20220107201202_cc681.jpg',
                    views: 448,
                    category: 'garden'
                },
                {
                    id: 2,
                    title: '组合造景',
                    image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRkY5ODAwIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iNjAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0Ij7nu4TlkojpgKDmma88L3RleHQ+Cjwvc3ZnPg==',
                    views: 325,
                    category: 'combine'
                },
                {
                    id: 3,
                    title: '室内装修',
                    image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjMjE5NkYzIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iNjAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0Ij7lrqTlhoXoo4Xkv6w8L3RleHQ+Cjwvc3ZnPg==',
                    views: 567,
                    category: 'indoor'
                },
                {
                    id: 4,
                    title: '家具装饰',
                    image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjOUMyN0IwIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iNjAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0Ij7lrrblhbfoo4Xpo7w8L3RleHQ+Cjwvc3ZnPg==',
                    views: 234,
                    category: 'furniture'
                },
                {
                    id: 5,
                    title: '花园景观',
                    image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRkY1NzIyIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iNjAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0Ij7oirHlm63mma/op4I8L3RleHQ+Cjwvc3ZnPg==',
                    views: 189,
                    category: 'garden'
                },
                {
                    id: 6,
                    title: '其他装饰',
                    image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjNjA3RDhCIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iNjAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0Ij7lhbbku5boo4Xpo7w8L3RleHQ+Cjwvc3ZnPg==',
                    views: 156,
                    category: 'other'
                }
            ]
        }
    },
    props: {
        width: {
            type: String,
            default: ''
        },
        height: {
            type: String,
            default: ''
        }
    },
    computed: {
        filteredItems() {
            let items = this.scaleItems;

            // 按分类过滤
            if (this.activeFilter !== 'all') {
                items = items.filter(item => item.category === this.activeFilter);
            }

            // 按搜索文本过滤
            if (this.searchText) {
                items = items.filter(item =>
                    item.title.toLowerCase().includes(this.searchText.toLowerCase())
                );
            }

            return items;
        },

        filteredCopyItems() {
            let items = this.copyItems;

            // 按分类过滤
            if (this.activeCopyCategory !== 'favorite' && this.activeCopyCategory !== 'myMaterial') {
                items = items.filter(item => item.category === this.activeCopyCategory);
            } else if (this.activeCopyCategory === 'favorite') {
                // 收藏分类显示所有项目（实际应用中可以添加收藏标记）
                items = this.copyItems;
            } else if (this.activeCopyCategory === 'myMaterial') {
                // 我的素材分类（实际应用中可以过滤用户上传的素材）
                items = this.copyItems;
            }

            // 按搜索文本过滤
            if (this.copySearchText) {
                items = items.filter(item =>
                    item.title.toLowerCase().includes(this.copySearchText.toLowerCase())
                );
            }

            return items;
        }
    },
    methods: {
        enitsd(it) {
            this.currenEinet = it.id
            uni.$emit('edit', it)
        },
        selectTool(tool) {
            this.currentTool = tool;
            console.log('选择工具:', tool);

            if (tool === 'add') {
                var that = this
                uni.chooseImage({
                    count: 1,
                    sourceType: ['album'],
                    success: function (res) {
                        console.log(res, '==');

                        uni.getImageInfo({
                            src: res.tempFilePaths[0],
                            success: function (image) {
                                console.log(image, 'M==');
                                const whuse = image.width > that.width || image.height > that.height ? image.width / 6 : image.width / 3
                                const huse = image.width > that.width || image.height > that.height ? image.height / 6 : image.height / 3
                                uni.$emit('selectScaleItem', {
                                    name: 'add',
                                    x: 100,
                                    y: 100,
                                    w: whuse,
                                    h: huse,
                                    type: 'add',
                                    size: 0,
                                    url: image.path,
                                    text: '',
                                    rotate: 0,
                                    display: false,
                                    color: '',
                                    closes: true,
                                    path: '',
                                })
                            }
                        });
                    }
                });
            } else if (tool === 'edit') {
                uni.$emit('edit', this.fuls.find(e => e.id == this.currenEinet))
            }
            this.colorLine();
        },

        handleScale() {
            this.showScalePanel = true;
        },

        handleAdd() {
            uni.showToast({
                title: '加载功能',
                icon: 'none'
            });
        },

        getToolName(tool) {
            const toolNames = {
                'copy': '复制',
                'scale': '缩放',
                'text': '文字',
                'brush': '画笔',
                'add': '加载'
            };
            return toolNames[tool] || '未知';
        },

        getToolDescription(tool) {
            const descriptions = {
                'copy': '点击复制按钮来复制选中的内容',
                'scale': '使用缩放工具来调整内容大小',
                'text': '选择文字工具来添加或编辑文本',
                'brush': '使用画笔工具来进行绘制操作',
                'add': '点击加载按钮来添加新的内容'
            };
            return descriptions[tool] || '请选择一个工具开始操作';
        },

        // 面板相关方法
        closeTextPanel() {
            this.currentTool = '';
            this.colorLine();
            uni.$emit('edit', null);
        },

        closePanels() {
            // this.showTextPanel = false;
            // this.showScalePanel = false;
            // this.showBrushPanel = false;
            // this.showCopyPanel = false;
        },

        closeBrushPanel() {
            this.showBrushPanel = false;
        },

        selectFont(font) {
            this.selectedFont = font;
            console.log('选择字体:', font);
        },

        onFontSizeChange(e) {
            this.fontSize = e.detail.value;
            console.log('字体大小:', this.fontSize);
        },

        selectColor(color) {
            this.selectedColor = color;
            console.log('选择颜色:', color);
        },

        // 缩放面板相关方法
        setFilter(filter) {
            this.activeFilter = filter;
            console.log('设置过滤器:', filter);
        },
        getTextDimensions(text, fontSize) {
            // 每行最多18个字符
            const maxCharsPerLine = 18;
            // 计算行数
            const lineCount = Math.ceil(text.length / maxCharsPerLine);

            // 单个字符宽度估算（中文1×字号，英文0.5×字号）
            let maxLineWidth = 0;
            for (let i = 0; i < lineCount; i++) {
                // 获取当前行的字符
                const start = i * maxCharsPerLine;
                const end = Math.min(start + maxCharsPerLine, text.length);
                const lineText = text.substring(start, end);

                // 计算当前行宽度
                let lineWidth = 0;
                for (let j = 0; j < lineText.length; j++) {
                    lineWidth += /[\u4e00-\u9fa5]/.test(lineText[j]) ? fontSize : fontSize * 0.5;
                }

                // 记录最宽行的宽度
                maxLineWidth = Math.max(maxLineWidth, lineWidth);
            }

            // 高度 = 行数 × 字号（简单估算，实际可能需要加上行间距）
            const height = lineCount * fontSize;

            return {
                width: Math.round(maxLineWidth),
                height: Math.round(height)
            };
        },
        selectScaleItem(item) {
            console.log('选择缩放项目:', item);
            uni.showToast({
                title: `选择了: ${item.title || item}`,
                icon: 'none'
            });
            var that = this
            if (item == 'text') {
                const { width, height } = that.getTextDimensions(that.currentText, that.fontSize)
                uni.$emit('selectScaleItem', {
                    name: item.title || '文字',
                    x: 100,
                    y: 100,
                    w: width + 5,
                    h: height + 7,
                    type: 'text',
                    textTpey: that.selectedFontOries[that.selectedFont].key,
                    size: that.fontSize || 0,
                    url: '',
                    text: that.currentText,
                    rotate: 0,
                    display: false,
                    color: that.selectedColor,
                    closes: true,
                    path: '',
                    Weights: that.Weights,
                })
                that.currentText = ''
            } else {
                uni.getImageInfo({
                    src: item.image,
                    success: function (image) {
                        console.log(image);
                        console.log(image.height, that.height);
                        const whuse = image.width > that.width || image.height > that.height ? image.width / 6 : image.width / 3
                        const huse = image.width > that.width || image.height > that.height ? image.height / 6 : image.height / 3
                        uni.$emit('selectScaleItem', {
                            name: item.title,
                            x: 100,
                            y: 100,
                            w: whuse,
                            h: huse,
                            type: '',
                            size: 0,
                            url: image.path,
                            text: '',
                            rotate: 0,
                            display: false,
                            color: '',
                            closes: true,
                            path: item.image,
                        })
                    }
                });
            }
        },

        // 画笔面板相关方法
        onBrushSizeChange(e) {
            this.brushSize = e.detail.value;
            console.log('画笔大小改变:', this.brushSize);
            this.colorLine();
        },

        selectBrushColor(color) {
            this.selectedBrushColor = color;
            console.log('选择画笔颜色:', color);
            this.colorLine();
        },

        selectBrushType(type) {
            this.selectedBrushType = type;
            console.log('选择画笔类型:', type);
            this.colorLine();
        },
        colorLine() {
            uni.$emit('colorLine', {
                tool: this.currentTool,
                size: this.brushSize,
                color: this.selectedBrushColor,
                type: this.selectedBrushType,
            });
        },

        // 复制面板相关方法
        setCopyCategory(category) {
            this.activeCopyCategory = category;
            console.log('设置复制分类:', category);
        },

        selectCopyItem(item) {
            console.log('选择复制项目:', item);
            uni.showToast({
                title: `选择了: ${item.title}`,
                icon: 'success'
            });
            this.showCopyPanel = false;
        },

        closeCopyPanel() {
            this.showCopyPanel = false;
        }

    }
}
</script>

<style scoped>
.containerff {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
    position: relative;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.content-area {
    width: 100%;
    height: 100%;
    background-color: #fff;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.placeholder-text {
    color: #999;
    font-size: 16px;
}

/* 缩放面板样式 */
.scale-panel {
    position: fixed;
    left: 0;
    bottom: 70px;
    background-color: #3c3c3c;
    z-index: 1000;
    /* animation: slideDown 0.3s ease-out; */
    overflow: auto;
    max-height: 56vh;
    width: 100%;
}

/* 
@keyframes slideDown {
    from {
        transform: translateY(-100%);
    }

    to {
        transform: translateY(0);
    }
} */

/* 搜索区域样式 */
.search-section {
    background-color: #2c2c2c;
    padding: 15px;
}

.search-bar {
    display: flex;
    align-items: center;
    background-color: #3c3c3c;
    border-radius: 20px;
    padding: 8px 15px;
    margin-bottom: 15px;
}

.search-icon {
    color: #999;
    font-size: 16px;
    margin-right: 10px;
}

.search-input {
    flex: 1;
    background: transparent;
    border: none;
    color: #fff;
    font-size: 14px;
    outline: none;
}

.search-input::placeholder {
    color: #999;
}

/* 过滤标签样式 */
.filter-tabs {
    display: flex;
    align-items: center;
    /* justify-content: center; */
    overflow-x: auto;
    width: 100%;
    height: 40px;
}

.filter-tab {
    padding: 6px 12px;
    background-color: #555;
    border-radius: 15px;
    border: 1px solid transparent;
    transition: all 0.2s ease;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.filter-tab.active {
    background-color: #4CAF50;
    border-color: #4CAF50;
}

.active.active {
    background-color: #4CAF50;
    border-color: #4CAF50;
}

.filter-text {
    color: #fff;
    font-size: 12px;
    white-space: nowrap;
}

/* 内容网格样式 */
.content-grid {
    width: 100%;
    /* padding: 15px; */
    grid-template-columns: repeat(2, 1fr);
    /* gap: 15px; */
    display: flex;
    align-items: center;
    justify-content: space-around;
    flex-wrap: wrap;
    overflow: auto;
}

.content-box {
    width: 100%;
    display: flex;
    /* align-items: center; */
}

.content-left {
    width: 20%;
    height: 100%;
    color: #fff;

    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    font-size: 14px;
}

.content-color {
    margin-top: 10px;
}

.content-right {
    width: 80%;
    height: 100%;
}

.content-grids {
    width: 80%;

    height: 220px;
    /* padding: 15px; */
    grid-template-columns: repeat(2, 1fr);
    /* gap: 15px; */
    display: flex;
    align-items: center;
    justify-content: space-around;
    flex-wrap: wrap;
    overflow: auto;
    background-color: none;
}

.content-gridss {
    width: 100%;
    height: 220px;
    /* padding: 15px; */
    grid-template-columns: repeat(2, 1fr);
    /* gap: 15px; */
    display: flex;
    align-items: center;
    justify-content: space-around;
    flex-wrap: wrap;

    overflow: auto;
    background-color: none;
}

.content-card {
    width: 48%;
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    /* background-color: #fff; */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
    margin-top: 10px;
}

.content-card:active {
    transform: scale(0.98);
}

.card-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
}

.card-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    padding: 15px 10px 10px;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
}

.card-title {
    color: #fff;
    font-size: 13px;
    font-weight: 500;
    flex: 1;
}

.card-stats {
    display: flex;
    align-items: center;
    gap: 4px;
}

.stats-icon {
    color: #fff;
    font-size: 12px;
}

.stats-text {
    color: #fff;
    font-size: 11px;
}

/* 画笔面板样式 */
.brush-panel {
    position: fixed;
    bottom: 65px;
    left: 0;
    right: 0;
    background-color: #3c3c3c;
    border-radius: 12px 12px 0 0;
    padding: 10px 20px;
    z-index: 1000;
    animation: slideUp 0.3s ease-out;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
    max-height: 60vh;
    overflow-y: auto;
}

.brush-section {
    margin-bottom: 10px;
}

.brush-section:last-child {
    margin-bottom: 0;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.section-title {
    color: #fff;
    font-size: 16px;
    font-weight: 500;
}

.section-value {
    color: #4CAF50;
    font-size: 16px;
    font-weight: bold;
}

/* 滑块样式 */
.slider-container {
    padding: 0 5px;
    display: flex;
    align-items: center;
}

.brush-slider {
    width: 100%;
    height: 15px;
}

/* 画笔颜色选择样式 */
.brush-color-palette {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    margin-top: 15px;
}

.brush-color-item {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: 3px solid transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    cursor: pointer;
}

.brush-color-item.active {
    border-color: #fff;
    transform: scale(1.1);
}

.color-check {
    color: #fff;
    font-size: 13px;
    font-weight: bold;
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
}

/* 笔状选择样式 */
.brush-type-container {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.brush-type-item {
    /* flex: 1; */
    display: flex;
    flex-direction: column;
    align-items: center;
    /* padding: 10px; */
    background-color: #555;
    border-radius: 8px;
    border: 2px solid transparent;
    transition: all 0.2s ease;
    cursor: pointer;
}

.brush-type-item.active {
    border-color: #4CAF50;
    background-color: #4CAF50;
}

.brush-type-line {
    width: 60px;
    height: 4px;
    background-color: #fff;
    /* margin-bottom: 10px; */
    border-radius: 2px;
}

.solid-line {
    background-color: #fff;
}

.dashed-line {
    background-image: repeating-linear-gradient(to right,
            #fff 0px,
            #fff 8px,
            transparent 8px,
            transparent 16px);
    background-color: transparent;
}

.brush-type-label {
    color: #fff;
    font-size: 14px;
    text-align: center;
}

/* 复制面板样式 */
.copy-panel {
    position: fixed;
    left: 0;
    bottom: 70px;
    background-color: #2c2c2c;
    z-index: 1000;
    animation: slideDown 0.3s ease-out;
    overflow-y: auto;
    height: 56vh;
    width: 100%;
}

/* 复制面板搜索区域 */
.copy-search-section {
    background-color: #2c2c2c;
    padding: 15px;
    border-bottom: 1px solid #444;
}

.copy-search-bar {
    display: flex;
    align-items: center;
    background-color: #3c3c3c;
    border-radius: 20px;
    padding: 8px 15px;
}

.copy-search-icon {
    color: #999;
    font-size: 16px;
    margin-right: 10px;
}

.copy-search-input {
    flex: 1;
    background: transparent;
    border: none;
    color: #fff;
    font-size: 14px;
    outline: none;
}

.copy-search-input::placeholder {
    color: #999;
}

/* 复制面板分类标签 */
.copy-category-tabs {
    display: flex;
    background-color: #2c2c2c;
    padding: 0 15px 15px;
    gap: 8px;
    flex-wrap: wrap;
}

.copy-category-tab {
    padding: 8px 0px;
    background-color: #444;
    border-radius: 20px;
    border: 1px solid transparent;
    transition: all 0.2s ease;
    cursor: pointer;
    margin: 0px 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.copy-category-tab.active {
    background-color: #4CAF50;
    border-color: #4CAF50;
}

.copy-category-text {
    color: #fff;
    font-size: 13px;
    font-weight: 500;
}

/* 复制面板内容网格 */
.copy-content-grid {
    /* padding: 15px; */
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    background-color: #f5f5f5;
    min-height: calc(100vh - 200px);
    width: 100%;
}

.copy-content-item {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
    cursor: pointer;
    width: 49%;
    height: 40px;
}

.copy-content-item:active {
    transform: scale(0.98);
}

.copy-item-image {
    width: 100%;
    height: 100px;
    object-fit: cover;
}

.copy-item-title {
    padding: 8px;
    font-size: 12px;
    color: #333;
    text-align: center;
    background-color: #fff;
}

/* 文字面板样式 */
.text-panel {
    position: fixed;
    bottom: 80px;
    left: 0;
    right: 0;
    background-color: #3c3c3c;
    border-radius: 12px 12px 0 0;
    padding: 0;
    z-index: 1000;
    /* animation: slideUp 0.3s ease-out; */
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
    max-height: 70vh;
    overflow-y: auto;
}


.panel-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2;
}

/* 面板头部 */
.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #555;
}

.panel-title {
    color: #fff;
    font-size: 16px;
    font-weight: 500;
}

.panel-close {
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #4CAF50;
    border-radius: 4px;
}

.close-icon {
    color: #fff;
    font-size: 16px;
    font-weight: bold;
}

/* 字体选择区域 */
.font-section {
    padding: 15px 20px;
    border-bottom: 1px solid #555;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.font-buttons {
    display: flex;
    gap: 8px;
    width: 100%;
    height: 50px;
    align-items: center;
    flex-wrap: nowrap;
    overflow: auto;
}

.font-btn {
    padding: 8px 12px;
    background-color: #555;
    border-radius: 6px;
    border: 1px solid transparent;
    transition: all 0.2s ease;
    margin: 0px 10px;
}

.font-btn.active {
    background-color: #4CAF50;
    border-color: #4CAF50;
}

.font-text {
    color: #fff;
    font-size: 13px;
    white-space: nowrap;
}

/* 字体大小调节区域 */
.size-section {
    padding: 15px 20px;
    border-bottom: 1px solid #555;
}

.section-label {
    color: #fff;
    font-size: 14px;
    margin-bottom: 10px;
    display: block;
}

.size-control {
    display: flex;
    align-items: center;
    gap: 15px;
}

.size-slider {
    flex: 1;
    height: 15px;
}

.size-value {
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    min-width: 35px;
    text-align: right;
}

/* 颜色选择区域 */
.color-section {
    padding: 15px 20px;
}

.color-palette {
    gap: 12px;
    margin-top: 10px;
    height: 6vh;
    overflow: auto;
    display: flex;
    align-items: center;
    /* border: 1px solid red; */
}

.text-input-area {
    height: 25px;
}

.textInput {
    width: 90%;
    height: 25px;
    border: 1px solid rgb(221, 221, 221);
    border-radius: 10rpx;
    background-color: #fff;
    color: #000;
}

.fontWeights {
    width: 25px;
    height: 25px;
    border-radius: 10rpx;
    text-align: center;
    line-height: 25px;
    font-size: 13px;
    background-color: #9E9E9E;
    color: #fff;
}

.backgroundColors {
    background-color: #4CAF50 !important;
}

.color-item {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    border: 3px solid transparent;
    transition: all 0.2s ease;
    cursor: pointer;
    margin-left: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.color-pos {
    display: flex;
    align-items: center;
}

.color-item.active {
    border-color: #fff;
    transform: scale(1.1);
}

.color-item:active {
    transform: scale(0.95);
}

/* 应用按钮区域 */
.apply-section {
    padding: 20px;
    border-top: 1px solid #555;
}

.apply-btn {
    width: 100%;
    height: 44px;
    background-color: #4CAF50;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: background-color 0.2s ease;
}

.apply-btn:active {
    background-color: #45a049;
}

.apply-text {
    color: #fff;
    font-size: 16px;
    font-weight: 500;
}

/* 顶部标题栏样式 */
.header {
    width: 100%;
    height: 100%;
    /* background-color: #fff; */
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #e9ecef;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-title {
    color: #212529;
    font-size: 16px;
    font-weight: 600;
}

/* 主要内容区域样式 */
.main-content {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.content-area {
    width: 100%;
    height: 100%;
    /* background-color: #fff; */
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.tool-status {
    width: 100%;
    height: 50px;
    /* background-color: #f8f9fa; */
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    padding: 0 20px;
    box-sizing: border-box;
    transition: background-color 0.3s ease;
}

.status-text {
    color: #495057;
    font-size: 14px;
    font-weight: 500;
    transition: color 0.3s ease;
}

.work-area {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.placeholder {
    text-align: center;
    max-width: 300px;
}

.placeholder-text {
    color: #6c757d;
    font-size: 15px;
    line-height: 1.5;
}

/* 底部工具栏样式 */
.toolbars {
    width: 100%;
    height: 80px;
    background-color: #1a1a1a;
    display: flex;
    position: relative;
    z-index: 100;
    align-items: center;
    justify-content: space-evenly;
    padding: 8px 0px;
    box-sizing: border-box;
    /* border-top: 1px solid #333; */
}

.edits {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
}

.tool-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 54px;
    border-radius: 4px;
    background-color: transparent;
    transition: all 0.2s ease;
    position: relative;
}

.tool-icon-box {
    width: 22px;
    height: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 3px;
}

.tool-icon {
    color: #ffffff;
    font-size: 15px;
    opacity: 0.9;
}

.tool-text-icon {
    color: #ffffff;
    font-size: 13px;
    font-weight: 500;
    opacity: 0.9;
}

.tool-label {
    color: #ffffff;
    font-size: 9px;
    text-align: center;
    line-height: 1;
    opacity: 0.8;
    font-weight: 400;
}

.tool-btn:active {
    background-color: rgba(255, 255, 255, 0.1);
    transform: scale(0.95);
}

/* 选中状态 */
.tool-btn.active {
    background-color: rgba(255, 255, 255, 0.08);
}

.tool-btn.active .tool-icon,
.tool-btn.active .tool-text-icon {
    opacity: 1;
    color: #4CAF50;
}

.tool-btn.active .tool-label {
    opacity: 1;
    color: #4CAF50;
}

/* 响应式设计 */
@media screen and (max-width: 480px) {
    .toolbar {
        padding: 6px 10px;
    }

    .tool-btn {
        width: 44px;
        height: 50px;
    }

    .tool-icon {
        font-size: 14px;
    }

    .tool-text-icon {
        font-size: 12px;
    }

    .tool-label {
        font-size: 8px;
    }
}

@media screen and (min-width: 768px) {
    .toolbar {
        height: 85px;
        padding: 10px 30px;
    }

    .tool-btn {
        width: 55px;
        height: 65px;
    }

    .tool-icon {
        font-size: 18px;
    }

    .tool-text-icon {
        font-size: 15px;
    }

    .tool-label {
        font-size: 11px;
    }
}

/* 文字面板响应式设计 */
@media screen and (max-width: 480px) {
    .text-panel {
        bottom: 70px;
    }

    .font-buttons {
        gap: 6px;
    }

    .font-btn {
        padding: 6px 10px;
    }

    .font-text {
        font-size: 12px;
    }

    .color-item {
        width: 18px;
        height: 18px;
    }

    .color-palette {
        gap: 10px;
    }

    /* 缩放面板响应式 */
    .content-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        padding: 12px;
    }

    .card-image {
        height: 100px;
    }

    .filter-tabs {
        gap: 6px;
    }

    .filter-tab {
        padding: 5px 10px;
    }

    .filter-text {
        font-size: 11px;
    }

    /* 画笔面板小屏幕优化 */
    .brush-color-palette {
        gap: 10px;
    }

    .brush-color-item {
        width: 32px;
        height: 32px;
    }

    .brush-type-container {
        gap: 15px;
    }

    .brush-type-item {
        padding: 8px 5px;
    }

    .brush-type-line {
        width: 50px;
        height: 3px;
    }

    /* 复制面板小屏幕优化 */
    .copy-content-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
        padding: 10px;
    }

    .copy-item-image {
        height: 80px;
    }

    .copy-item-title {
        font-size: 11px;
        padding: 6px;
    }

    .copy-category-tabs {
        gap: 6px;
        padding: 0 10px 10px;
    }

    .copy-category-tab {
        padding: 6px 12px;
        font-size: 12px;
    }
}

@media screen and (min-width: 768px) {
    .text-panel {
        left: 50%;
        right: auto;
        transform: translateX(-50%);
        width: 400px;
        border-radius: 12px;
        bottom: 90px;
    }

    /* 缩放面板大屏幕优化 */
    .content-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
        padding: 20px;
    }

    .card-image {
        height: 140px;
    }

    .search-section {
        padding: 20px;
    }

    /* 画笔面板大屏幕优化 */
    .brush-panel {
        left: 50%;
        right: auto;
        transform: translateX(-50%);
        width: 400px;
        border-radius: 12px;
        bottom: 90px;
    }

    .brush-color-palette {
        gap: 15px;
    }

    .brush-color-item {
        width: 40px;
        height: 40px;
    }

    .brush-type-container {
        gap: 25px;
    }

    .brush-type-item {
        padding: 18px;
    }

    .brush-type-line {
        width: 70px;
        height: 5px;
    }

    /* 复制面板大屏幕优化 */
    .copy-content-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
        padding: 20px;
    }

    .copy-item-image {
        height: 120px;
    }

    .copy-item-title {
        font-size: 13px;
        padding: 10px;
    }

    .copy-category-tabs {
        gap: 12px;
        padding: 0 20px 20px;
    }

    .copy-category-tab {
        padding: 10px 20px;
        font-size: 14px;
    }
}
</style>