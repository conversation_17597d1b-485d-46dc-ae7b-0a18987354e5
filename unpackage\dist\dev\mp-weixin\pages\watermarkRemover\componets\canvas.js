"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  props: {
    width: {
      type: String,
      default: ""
    },
    height: {
      type: String,
      default: ""
    },
    info: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      ctx: null,
      data: null,
      dataTrans: []
    };
  },
  watch: {
    dataTrans: {
      handler(newVal, oldVal) {
        if (this.dataTrans.length == this.data.length) {
          this.draws();
        }
      },
      deep: true
    }
  },
  mounted() {
    this.ctx = common_vendor.index.createCanvasContext("firstCanvas", this);
    common_vendor.index.$on("draws", async (e) => {
      this.data = e;
      this.imageOpen();
    });
  },
  methods: {
    imageOpen() {
      const that = this;
      this.data.forEach((item, index) => {
        if (item.type != "text") {
          common_vendor.index.getImageInfo({
            src: item.url,
            success: (res) => {
              if (item.type == "background") {
                item.w = res.width;
                item.h = res.height;
                item.url = res.path;
                that.dataTrans.push(true);
              } else {
                item.url = res.path;
                that.dataTrans.push(true);
              }
            }
          });
        } else {
          that.dataTrans.push(true);
        }
      });
    },
    draws() {
      const { ctx, data, width, height, info } = this;
      ctx.clearRect(0, 0, info.width, info.height);
      ctx.draw();
      console.log(ctx, data[0], "ctx");
      ctx.clearRect(0, 0, info.width, info.height);
      ctx.fillRect(0, 0, info.width, info.height);
      const i = data.filter((v) => v.type == "background");
      console.log(!i.length, "i");
      if (!i.length) {
        ctx.setFillStyle("rgba(245, 245, 245, 0.2)");
      }
      data.forEach((item) => {
        console.log(item, "item.url");
        ctx.save();
        if (item.type == "background") {
          const canvasW = info.width;
          const canvasH = info.height;
          const scale = canvasH / item.h;
          const drawW = item.w * scale;
          const drawH = canvasH;
          const offsetX = (canvasW - drawW) / 2;
          const offsetY = 0;
          ctx.drawImage(item.url, offsetX, offsetY, drawW, drawH);
        } else {
          ctx.translate(item.x + item.w / 2, item.y + item.h / 2);
          ctx.rotate(item.rotate);
          ctx.translate(-(item.x + item.w / 2), -(item.y + item.h / 2));
          if (item.type === "text") {
            const t = item.size + "px " + item.textTpey;
            ctx.setFillStyle(item.color || "#000000");
            ctx.setTextAlign("left");
            ctx.setTextBaseline("top");
            ctx.font = t;
            ctx.fillText(item.text, item.x, item.y);
          } else {
            console.log("item:", item);
            ctx.drawImage(item.url, item.x, item.y, item.w, item.h);
          }
        }
        ctx.restore();
      });
      setTimeout(() => {
        ctx.draw(false, () => {
          console.log("画布绘制完成");
          common_vendor.index.canvasToTempFilePath({
            canvasId: "firstCanvas",
            fileType: "png",
            quality: 1,
            success: (res) => {
              console.log("生成图片成功:", res.tempFilePath);
              common_vendor.index.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: () => {
                  common_vendor.index.showToast({
                    title: "保存成功",
                    icon: "none"
                  });
                },
                fail: (err) => {
                  console.error("保存失败:", err);
                  common_vendor.index.showToast({
                    title: "保存失败",
                    icon: "none"
                  });
                }
              });
            },
            fail: (err) => {
              console.error("生成图片失败:", err);
            }
          }, this);
        });
      }, 1e3);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $props.width + "px",
    b: $props.height + "px"
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-1efa5b3a"]]);
wx.createComponent(Component);
