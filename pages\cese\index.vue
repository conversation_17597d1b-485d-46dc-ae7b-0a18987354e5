<template>
    <view style="width: 100%;height: 100vh; position: relative; overflow: hidden;">

        <view class="container">
            <!-- 头部按钮 -->
            <view class="header">
                <button class="header-btn" @click="goHome">返回</button>
                <button class="header-btn save" @click="save">保存</button>
                <!-- <button class="header-btn" @click="">上传图片</button> -->
            </view>
            <!-- 画布 -->
            <view class="main-area">
                <div style="position: relative; 
                    display: flex;
                    align-items: center;
                    justify-content: center;" :style="{ width: canvasStyle.width, height: canvasStyle.height, }">
                    <!-- 上传图片按钮 -->
                    <!-- canvas 画布 -->
                    <image :src="imgesMuns" alt="" class="main-img" mode="aspectFit" />

                    <!-- 主画布 -->
                    <canvas canvas-id="canvasId" id="canvasId" class="main-img draggable-img"
                        :style="{ width: canvasStyle.width, height: canvasStyle.height, }" @touchstart="onTouchStart"
                        @touchmove="onTouchMove" @touchend="onTouchEnd" width="400" :class="imgesMuns ? '' : 'autos'"
                        height="400"></canvas>

                    <!-- 处理线条数据标签 -->
                    <canvas canvas-id="canvasIdPen" id="canvasIdPen" class="main-img draggable-img"
                        style="position: absolute;top: -999px;z-index: 3;"></canvas>
                    <!-- 选中图片时的边框和操作按钮（删除、复制、旋转、缩放） -->
                    <view v-for="(img, idx) in images" :key="idx">
                        <div v-if="img.selected" :key="'border-' + idx" class="img-border-ops" :style="{
                            left: (img.x) + 'px',
                            top: (img.y) + 'px',
                            width: (img.w) + 'px',
                            height: (img.h) + 'px',
                            transform: img.rotate ? `rotate(${img.rotate}rad)` : '',
                            transformOrigin: `${(img.w + 5) / 2}px ${(img.h + 5) / 2}px`,
                            zIndex: 2,
                            padding: '10px'
                        }">
                            <!-- 边框 -->
                            <div class="border-rect"></div>
                            <!-- 左上角 删除 -->
                            <div class="corner-btn corner-del" @touchstart="cornerDelete">
                                ×
                            </div>

                            <!-- <div class="corner-border"></div> -->
                            <!-- 左下角 复制 -->
                            <div class="corner-btn corner-copy" @touchstart="cornerCopy">
                                +
                            </div>

                            <div class="corner-border"></div>
                            <!-- 右上角 旋转 -->
                            <div class="corner-btn corner-rotate" @touchstart="touchStartRotate"
                                @touchmove="touchmoveRotate" @touchend="touchEndRotate">
                                ↻
                            </div>
                            <div class="corner-border"></div>
                            <!-- 右下角 缩放 -->
                            <div class="corner-btn corner-scale" @touchstart="touchStartScale" @touchmove="rotateScale"
                                @touchend="touchEndScale">
                                <span v-if="!_scaling">⇄</span>
                                <span v-else-if="_scaleType === 'horizontal'" style="color: #ff5722;">↔</span>
                                <span v-else-if="_scaleType === 'vertical'" style="color: #2196f3;">↕</span>
                                <span v-else style="color: #4caf50;">⇄</span>
                            </div>
                            <div class="corner-border"></div>
                        </div>
                    </view>
                </div>
            </view>
            <!-- 底部工具栏 -->
            <view class="toolbar">
                <view class="toolbar-item" @click="materialVisible = 'material'">
                    <image src="./images/material.png" class="toolbar-icon"></image>
                    <text>素材</text>
                </view>
                <view class="toolbar-item" @click="materialVisible = 'template'">
                    <image src="./images/template.png" class="toolbar-icon"></image>
                    <text>模板</text>
                </view>
                <view class="toolbar-item" @click="materialVisible = 'text'">
                    <image src="./images/text.png" class="toolbar-icon"></image>
                    <text>文字</text>
                </view>
                <!-- <view class="toolbar-item">
                <image src="/static/icon-mark.png" class="toolbar-icon" @click="materialVisible = 'mark'"></image>
                <text>标记</text>
            </view> -->
                <view class="toolbar-item" @click="materialVisible = 'pen'">
                    <image src="./images/paintbrush.png" class="toolbar-icon"></image>
                    <text>画笔</text>
                </view>
                <view class="toolbar-item" @click="chooseImage">
                    <image src="./images/image.png" class="toolbar-icon"></image>
                    <text>加图</text>
                </view>
            </view>
            <!-- 图层按钮 -->
            <view class="layer-box">
                <view style="width: 40%;display: flex;align-items: center;">
                    <view style="width: 50%;text-align: center;" @click="leftClick">
                        ←
                    </view>
                    <view style="width: 50%;text-align: center;" @click="ringhtClick">
                        →
                    </view>

                    <view style="width: 50%;text-align: center;" @click="clear">
                        清空
                    </view>
                </view>

                <view style="width: 25%;margin-right: 20px">
                    <button class="layer-btn" @click="layerBtn = !layerBtn">{{ !layerBtn ? '图层' : '收起图层' }}</button>
                </view>
            </view>

            <!-- 素材侧边栏 -->
            <view class="sidebarBox" v-if="materialVisible == 'material'">
                <view class="end-btn">
                    <span @click="downs">×</span>
                    <span @click="sidebar('material')">√</span>
                </view>
                <view class="sidebar">
                    <view class="sidebar-section">
                        <view class="sidebar-title" v-for="(item, idex) in materialList" :key="idex"
                            :class="sectionIndex == idex ? 'active' : ''" @click="sectionIndex = idex">{{ item.name }}
                        </view>
                    </view>
                    <view class="material-content">
                        <view class="sidebar-menu">
                            <view class="sidebar-menu-item" v-if="materialList[sectionIndex].child"
                                :class="menuItemIndex == index ? 'active' : ''"
                                v-for="(item, index) in materialList[sectionIndex].child" :key="index"
                                @click="menuItemIndex = index">{{ item.name }}</view>
                        </view>
                        <view class="material-row">
                            <view class="material-card" :class="{ 'material-circle': materialidx == idx }"
                                v-for="(item, idx) in materialList[sectionIndex].child[menuItemIndex].order" :key="idx"
                                @click="materialidx = idx">
                                <image class="material-img" :src="item.image">
                                </image>
                                <view class="material-title">{{ item.name }}</view>
                                <view class="material-tag" v-if="idx === 0">￥</view>
                                <view class="material-vip" v-if="idx !== 0">VIP</view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 文字工具栏 -->
            <view class="text-toolbar" v-if="materialVisible == 'text'">
                <view class="end-btn">
                    <span @click="downs">×</span>
                    <span @click="sidebar('text')">√</span>
                </view>
                <view class="text-toolbar-row">
                    <input class="text-input" v-model="textInput" placeholder="示例文字" />
                </view>
                <view class="text-toolbar-row font-row">
                    <button v-for="(font, idx) in fonts" :key="idx"
                        :class="['font-btn', { active: textFont === font.name }]" @click="textFont = font.name"
                        :style="{ fontFamily: font.name }">
                        {{ font.label }}
                    </button>
                </view>
                <view class="text-toolbar-row size-row">
                    <text class="label">大小</text>
                    <slider class="size-slider" min="10" max="200" :value="textSize" show-value style="width: 60%;" />
                    <text class="size-value">{{ textSize }}</text>
                </view>
                <view class="text-toolbar-row color-row">
                    <text class="label">颜色</text>
                    <view class="color-list">
                        <view v-for="(color, idx) in textColors" :key="idx"
                            :class="['color-circle', { selected: textColor === color }]"
                            :style="{ background: color, border: textColor === color ? '2px solid #b6ff3b' : '2px solid #888' }"
                            @click="textColor = color">
                            <view v-if="textColor === color" class="color-selected"></view>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 划线选择 -->
            <view class="pen-toolbar" v-if="materialVisible == 'pen'">
                <view class="end-btn">
                    <span @click="downs">×</span>
                    <span @click="sidebar('pen')">√</span>
                </view>
                <view class="pen-row">
                    <text class="label">粗细</text>
                    <slider min="1" max="30" :value="penSize" show-value style="width: 60%;" />
                    <!-- <text class="size-value">{{ penSize }}</text> -->
                </view>
                <view class="pen-row">
                    <text class="label">颜色</text>
                    <view class="pen-color-list">
                        <view v-for="(color, idx) in penColors" :key="color"
                            :class="['pen-color-circle', { selected: penColor === color }]"
                            :style="{ background: color, border: penColor === color ? '2px solid #b6ff3b' : '2px solid #888' }"
                            @click="penColor = color">
                            <view v-if="penColor === color" class="pen-color-selected"></view>
                        </view>
                    </view>
                </view>
                <view class="pen-row">
                    <text class="label">笔状</text>
                    <view class="pen-style-list">
                        <view :class="['pen-style-item', { active: penStyle === 'solid' }]" @click="penStyle = 'solid'">
                            <image src="/static/pen-solid.png" style="width:24px;height:24px;" />
                            <text style="color:#8dd800;margin-left:4px;">实线</text>
                        </view>
                        <view :class="['pen-style-item', { active: penStyle === 'dashed' }]"
                            @click="penStyle = 'dashed'">
                            <image src="/static/pen-dashed.png" style="width:24px;height:24px;" />
                            <text style="color:#bbb;margin-left:4px;">虚线</text>
                        </view>
                    </view>
                </view>

                <!-- 画笔状态显示 -->
                <view class="pen-status" v-if="isDrawing && currentStroke">
                    <text class="pen-status-text">正在画线: </text>
                    <view class="pen-status-color" :style="{ background: currentStroke.color }"></view>
                    <text class="pen-status-size">{{ currentStroke.size }}px</text>
                    <text class="pen-status-style">{{ currentStroke.style === 'dashed' ? '虚线' : '实线' }}</text>
                </view>
            </view>

            <!-- 模板 -->
            <view class="template-bar" v-if="materialVisible == 'template'">
                <view class="end-btn">
                    <span @click="downs">×</span>
                </view>
                <view class="template-tabs">
                    <view :class="['template-tab', { active: true }]">推荐</view>
                    <view class="template-tab">室内组景</view>
                    <view class="template-tab">组合盆</view>
                    <view class="template-tab">组景架构</view>
                    <view class="template-tab">花箱景观</view>
                    <view class="template-tab">积木造型</view>
                </view>
                <scroll-view scroll-x class="template-list">
                    <view class="template-card" v-for="(item, idx) in templateList" :key="idx"
                        @click="sidebar('template')">
                        <image class="template-img" :src="item.image">
                        </image>
                        <view class="template-info">
                            <view class="template-title">{{ item.name }}</view>
                            <view class="template-meta">
                                <image
                                    src="https://b0.bdstatic.com/ugc/img/2024-12-28/42e36b01cb8ddae367b10ca4b0e4b919.png"
                                    class="template-vip-icon" />
                                <text class="template-view">448</text>
                            </view>
                        </view>
                    </view>
                </scroll-view>
            </view>

            <!-- 图层 -->
            <view class="layer-panel" v-if="layerBtn">
                <view class="layer-tabs">
                    <view :class="['layer-tab', { active: true }]">单选</view>
                    <!-- <view class="layer-tab">多选</view> -->
                </view>
                <scroll-view class="layer-list" scroll-y>
                    <view v-for="(img, idx) in images" :key="idx" class="layer-item">
                        <view style="display: flex;align-items: center;">
                            <image v-if="img.switch" src="./images/display.png" class="layer-eye" @click="hideIf(img)"
                                mode="aspectFit" />
                            <image v-else @click="hideIf(img)" src="./images/hide.png" class="layer-eye"
                                mode="scaleToFill" />
                            <view class="layer-thumb">
                                <image v-if="img.url" :src="img.url" class="layer-img" mode="aspectFit" />
                                <text v-else class="layer-text" :style="{ color: '#ff5722' }">{{ img.type == 'pen' ?
                                    '线条' :
                                    img.type == 'backgorund' ? '背景' : '文字' }}</text>
                            </view>
                            <view @click="up(img)" style="margin-left: 10px;color: #fff;">
                                ↑
                            </view>
                        </view>
                        <view style="color: #fff;text-align: right;" @click="leftClickOff(idx)">
                            ×
                        </view>
                    </view>
                </scroll-view>
                <view class="layer-footer">
                    <image src="/static/layer-icon.png" class="layer-footer-icon" />
                    <text class="layer-footer-text" @click="layerBtn = false">收起图层</text>
                </view>
            </view>
        </view>
    </view>
</template>


<script>
/*
   materialLists: {
        name: '艺术盆栽',
        child: [
            {
                name: ,
                order: [
                    {
                        name: ,
                        image: 
                    }
                ]
            }
        ]
    }

    @save(e) //保存 返回数据
*/


export default {
    props: {
        // 背景图
        imgesData: {
            type: String,
            default: ''
        },
        // 素材列表
        materialLists: {
            type: Array,
            default: [
                {
                    name: '室内植物',
                    child: [
                        {
                            name: '收藏',
                            order: [
                                {
                                    name: '宝莲灯盆栽',
                                    image: 'https://img.shetu66.com/2023/07/18/1689659210837955.png'
                                },
                                {
                                    name: '哈哈哈',
                                    image: 'https://tse1-mm.cn.bing.net/th/id/OIP-C.uuMRp41SjL9ukaBDDBWz5wHaNK?rs=1&pid=ImgDetMain'
                                }
                            ]
                        },
                        {
                            name: '我的素材',
                            order: [
                                {
                                    name: '宝莲灯盆栽',
                                    image: 'https://tse1-mm.cn.bing.net/th/id/OIP-C.uuMRp41SjL9ukaBDDBWz5wHaNK?rs=1&pid=ImgDetMain'
                                }
                            ]
                        },
                        {
                            name: '艺术盆栽',
                            order: [
                                {
                                    name: '宝莲灯盆栽',
                                    image: 'https://tse4-mm.cn.bing.net/th/id/OIP-C.vyXDY_jejkSCTPCB-wyh5AHaEK?rs=1&pid=ImgDetMain'
                                }
                            ]
                        },

                    ]
                },
                {
                    name: '我的素材',
                    child: [
                        {
                            name: '我的',
                            order: [
                                {
                                    name: '宝莲灯盆栽',
                                    image: 'https://img.shetu66.com/2023/07/18/1689659210837955.png'
                                }
                            ]
                        }
                    ]
                },
            ]
        },
        // 模板列表
        templateLists: {
            type: Array,
            default: [{ name: '宝莲灯盆栽', image: 'https://img.shetu66.com/2023/07/18/1689659210837955.png' }]
        },
        // 是否返回图片下载
        returnImage: {
            type: Boolean,
            default: true
        },
        // 是否下载图片
        download: {
            type: Boolean,
            default: true
        },
        // 画布样式宽高
        canvasStyle: {
            type: Object,
            default: {
                width: '100%',
                height: '950rpx'
            }
        },
        // 回显传入数据就行
        imageData: {
            type: Array,
            default: [{
                "createBy": null,
                "createTime": null,
                "updateBy": null,
                "updateTime": null,
                "remark": null,
                "id": 36,
                "createCode": "20250627162531A003",
                "x": null,
                "y": null,
                "w": "1280",
                "h": "960",
                "rotate": null,
                "selected": null,
                "switchStatue": null,
                "type": "backgorund",
                "text": null,
                "font": null,
                "isText": null,
                "size": null,
                "style": null,
                "path": null,
                "url": 'https://tse1-mm.cn.bing.net/th/id/OIP-C.uuMRp41SjL9ukaBDDBWz5wHaNK?rs=1&pid=ImgDetMain',
                "scaleStart": null,
                switch: true
            }]
        },
        // 需要返回的地址 page: true 为 tabBar页面  false 为普通页面
        returnUrl: {
            type: Object,
            default: {
                page: false,
                url: ''
            }
        }
    },
    data() {
        return {
            images: [], // {url, x, y, w, h, selected}
            LayerList: [],

            sectionIndex: 0,
            menuItemIndex: 0,

            imgesMuns: null,
            imgesMunsData: {
                w: '',
                h: ''
            },

            imagesOnes: [],
            dragging: false,
            startTouch: { x: 0, y: 0 },
            startPos: { x: 0, y: 0 },
            ctx: null,
            materialVisible: '',
            textInput: '',
            // 文字相关
            textFont: 'Arial',
            textSize: 36,
            textColor: '#fff',
            fonts: [
                { name: 'Arial', label: 'Arial' },
                { name: 'PingFang SC', label: '苹方' },
                { name: 'SimHei', label: '黑体' },
                { name: 'FZShuTi', label: '舒体' }
            ],
            textColors: [
                '#fff', '#222', '#b6ff3b', '#e53935', '#ffd700', '#00bcd4', '#ff9800', '#8dd800'
            ],
            materialList: [],
            templateList: [],
            materialidx: 0,
            // 画笔相关
            penSize: 6,
            penColor: '#8dd800',
            penColors: [
                '#8dd800', '#fff', '#222', '#e53935', '#ffd700', '#00bcd4', '#ff9800'
            ],
            penStyle: 'solid',
            _rotating: false,

            // 旋转
            _rotateStart: null,
            _rotateStartStart: false,
            // 图层
            layerBtn: false,
            // 缩放
            _scaling: false,
            _scaleStart: null,
            _scaleType: 'both', // 缩放类型：'horizontal', 'vertical', 'both'

            arring: [],
            arringTure: false,
            finish: false,


            isDrawing: false,
            penPath: [],
            penLastPoint: null,
            currentStroke: null,
            currentPenColor: null,
            currentPenSize: null,
            currentPenStyle: null,
            // 清空
        }
    },
    async onLoad() {
        const listYOPu = this.imageData
        if (listYOPu) {
            try {
                const app = listYOPu
                if (app && app.length > 0) {
                    if (app[0].type == 'backgorund') {
                        this.background(app[0].url)
                        if (app.slice(1).length) {
                            this.images = app.slice(1) // 使用slice而不是shift
                        }
                        this.drawImgs()
                    } else {
                        this.images = app
                        this.drawImgs()
                    }
                }
            } catch (e) {
                console.error('解析存储数据失败:', e);
            }
        }
        this.canvasV()
    },
    watch: {
        arring: {
            handler() {
                console.log(this.arring, this.images, 'this.arring.length == this.images.length');
                if (this.arring.length == this.images.length && this.finish) {

                    if (this.download) {
                        uni.canvasToTempFilePath({
                            canvasId: 'canvasId',
                            success: (res) => {
                                uni.saveImageToPhotosAlbum({
                                    filePath: res.tempFilePath,
                                    success: () => {
                                        uni.showToast({ title: '保存成功', icon: 'success' });
                                        this.finish = false
                                        // this.images = [] // 清空
                                        // this.drawImgs() // 清空
                                        this.arring = []
                                        uni.hideLoading();
                                    },
                                    fail: () => {
                                        uni.showToast({ title: '保存失败', icon: 'none' });
                                    }
                                });
                            },
                            fail: () => {
                                uni.showToast({ title: '生成图片失败', icon: 'none' });
                            }
                        }, this);
                    }

                    if (this.returnImage) {
                        uni.canvasToTempFilePath({
                            canvasId: 'canvasId',
                            success: (res) => {
                                console.log(res, 'resss');

                                uni.showToast({ title: '保存成功', icon: 'success' });
                                this.finish = false
                                uni.$emit('returnImage', res.tempFilePath)
                                // this.images = []  // 清空
                                // this.drawImgs()  // 清空
                                uni.hideLoading();
                                this.arring = []
                            },
                            fail: () => {
                                uni.showToast({ title: '生成图片失败', icon: 'none' });
                            }
                        }, this);
                    }
                }
            },
            deep: true
        }
    },
    computed: {
        // LayerListOne() {
        //     this.LayerList = this.images.reverse()

        //     return this.LayerList
        // }
    },
    methods: {
        background(e) {
            uni.getImageInfo({
                src: e,
                success: (img) => {
                    this.imgesMuns = img.path
                    this.imgesMunsData = {
                        w: img.width,
                        h: img.height,
                    }
                }
            });
        },
        canvasV() {
            console.log(this.arring, 'arring');

            this.ctx = uni.createCanvasContext('canvasId', this)
            this.materialList = this.materialLists
            this.templateList = this.templateLists
            if (this.imgesData && !this.imgesMuns) {
                this.background(this.imgesData)
            }
        },
        goHome() {
            if (this.returnUrl.url) {
                uni.showToast({
                    title: '未设置返回地址',
                    icon: 'none'
                });
            } else if (this.returnUrl.page) {
                uni.switchTab({
                    url: this.returnUrl.url
                });
            } else if (!this.returnUrl.page) {
                uni.navigateTo({
                    url: this.returnUrl.url
                });
            }
        },
        save() {
            if (this.imgesMuns) {
                this.images.unshift({
                    type: 'backgorund',
                    url: this.imgesMuns,
                    w: this.imgesMunsData.w,
                    h: this.imgesMunsData.h,
                    switch: true,
                });
                this.arringTure = true
                this.finish = true
                this.drawImgs()
            } else {
                this.finish = true
                this.drawImgs()
            }
            this.$emit('save', this.images)
        },
        sidebar(type) {
            this.materialVisible = '';
            var that = this
            if (type == 'material') {
                console.log(that.materialList[that.sectionIndex].child[that.menuItemIndex].order[that.materialidx].image, 'this.materialList[this.materialidx]');

                uni.getImageInfo({
                    src: that.materialList[that.sectionIndex].child[that.menuItemIndex].order[that.materialidx].image,// that.materialList[that.materialidx].image
                    success: function (img) {
                        console.log(img.width);
                        console.log(img.height);
                        console.log(img, '11111');


                        that.images.push({
                            type: 'material',
                            name: that.materialList[that.sectionIndex].child[that.menuItemIndex].order[that.materialidx].name,
                            url: img.path,
                            x: 100 + that.images.length * 20,
                            y: 100 + that.images.length * 20,
                            w: img.width / 4,
                            h: img.height / 4,
                            selected: true,
                            switch: true,
                            rotate: 0,
                            _scaleStart: { x: 0, y: 0, w: 0, h: 0 },
                        })

                        that.$nextTick(() => {
                            that.drawImgs()
                        })
                    }
                });
            } else if (type == 'text') {
                // 添加文字到画布
                if (this.textInput.trim()) {
                    // 文字对象加入 images 数组
                    this.images.push({
                        type: 'text',
                        text: this.textInput,
                        font: this.textFont,
                        size: this.textSize,
                        color: this.textColor,
                        x: 100 + this.images.length * 20,
                        y: 100 + this.images.length * 20,
                        w: null, // 宽高后面计算
                        h: null,
                        selected: true,
                        switch: true,
                        isText: true,
                        rotate: 0,
                        _scaleStart: { x: 0, y: 0, w: 0, h: 0 },
                    })
                    setTimeout(() => {
                        this.drawImgs()
                    })
                }
            } else if (type == 'pen') {
                // 画笔模式结束，先检查是否有线条需要保存
                console.log('点击确认按钮，检查是否有线条需要保存');
                console.log('当前状态 - isDrawing:', this.isDrawing, 'penPath长度:', this.penPath ? this.penPath.length : 0);

                if (this.penPath && this.penPath.length > 0) {
                    console.log('发现未保存的线条，正在保存...');
                    this.handlePenEnd();
                } else {
                    console.log('没有未保存的线条，直接重置状态');
                    this.resetPenState();
                }
                console.log('画笔模式结束');
                console.log('当前images数组:', this.images);
            } else if (type == 'template') {
                uni.getImageInfo({
                    src: that.materialList[this.materialidx].image,// that.materialList[that.materialidx].image
                    success: function (img) {
                        console.log(img.width);
                        console.log(img.height);
                        console.log(img, '11111');


                        that.images.push({
                            type: 'add',
                            name: that.materialList[this.materialidx].name,
                            url: img.path,
                            x: 0,
                            y: 0,
                            w: 400,
                            h: 500,
                            selected: true,
                            switch: true,
                            rotate: 0,
                            _scaleStart: { x: 0, y: 0, w: 0, h: 0 },
                        })

                        that.$nextTick(() => {
                            that.drawImgs()
                        })
                        // res.tempFilePaths.forEach(path => {
                        // })
                    }
                });
            } else if (type == 'add') {
            }
            // 只让最后一个元素为选中状态，其余全部取消选中
            this.images.forEach((item, idx) => {
                item.selected = idx === this.images.length - 1;
            });
        },
        chooseImage() {
            uni.chooseImage({
                count: 9,
                success: (res) => {
                    res.tempFilePaths.forEach(path => {
                        this.images.push({
                            type: 'add',
                            url: path,
                            x: 100 + this.images.length * 20,
                            y: 100 + this.images.length * 20,
                            w: 120,
                            h: 120,
                            selected: false,
                            rotate: 0
                        })
                    })
                    this.$nextTick(() => {
                        this.drawImgs()
                    })
                }
            })
        },
        // 图层向上一格
        up(item) {
            const idx = this.images.findIndex(img => img === item);
            if (idx > 0) {
                // 交换当前元素与上一个元素的位置
                const temp = this.images[idx - 1];
                this.images[idx - 1] = this.images[idx];
                this.images[idx] = temp;
                this.drawImgs();
            }
        },
        // 旋转
        touchStartRotate() {
            this._rotateStartStart = true
        },
        touchmoveRotate(event) {
            const touch = event.touches[0];

            // 旋转选中图片
            const img = this.images.find(item => item.selected);
            if (!img) return;

            // 兼容微信小程序 touch.clientY/touch.clientX
            const touchX = touch.x !== undefined ? touch.x : touch.clientX;
            const touchY = touch.y !== undefined ? touch.y : touch.clientY;

            // 计算图片中心点
            const centerX = img.x + img.w / 2;
            const centerY = img.y + img.h / 2;

            // 计算当前手指相对于中心点的角度
            const currentAngle = Math.atan2(touchY - centerY, touchX - centerX);

            if (!this._rotateStart) {
                // 第一次触摸，记录初始状态
                this._rotateStart = {
                    startAngle: currentAngle,
                    startRotate: img.rotate || 0,
                    lastAngle: currentAngle,
                    totalDelta: 0
                };
                console.log('开始旋转，初始角度:', (currentAngle * 180 / Math.PI).toFixed(1), '度');
            } else {
                // 计算与上一次的角度差值
                let deltaAngle = currentAngle - this._rotateStart.lastAngle;

                // 处理角度跨越边界的情况（-π 到 π 的跳跃）
                if (deltaAngle > Math.PI) {
                    deltaAngle -= 2 * Math.PI;
                } else if (deltaAngle < -Math.PI) {
                    deltaAngle += 2 * Math.PI;
                }

                // 累积角度变化
                this._rotateStart.totalDelta += deltaAngle;

                // 设置新的旋转角度
                img.rotate = (this._rotateStart.startRotate + this._rotateStart.totalDelta) * 1.5;

                // 更新上一次的角度
                this._rotateStart.lastAngle = currentAngle;

                console.log('旋转中 - 增量:', (deltaAngle * 180 / Math.PI).toFixed(1), '度, 总角度:', (img.rotate * 180 / Math.PI).toFixed(1), '度');
                this.drawImgs();
            }
        },
        touchEndRotate() {
            this._rotateStartStart = false;
            this._rotateStart = null; // 重置旋转起始状态
        },
        touchEndScale() {
            this._scaling = false;
            this._scaleStart = null; // 重置缩放起始状态
            this._scaleType = 'both'; // 重置缩放类型
            console.log('缩放结束');
        },

        // 删除功能
        cornerDelete() {
            // 找到当前选中的图片
            const selectedIndex = this.images.findIndex(item => item.selected);

            if (selectedIndex !== -1) {
                const deletedItem = this.images[selectedIndex];

                // 显示确认对话框
                uni.showModal({
                    title: '确认删除',
                    content: `确定要删除这个${this.getItemTypeName(deletedItem.type)}吗？`,
                    confirmText: '删除',
                    cancelText: '取消',
                    confirmColor: '#e53935',
                    success: (res) => {
                        if (res.confirm) {
                            this.imagesOnes.push(deletedItem);
                            // 用户确认删除
                            this.images.splice(selectedIndex, 1);
                            console.log('已删除元素:', deletedItem.type, '剩余元素数量:', this.images.length);
                            // 保存删除的元素到 imagesOnes
                            // 重新绘制画布
                            this.drawImgs();

                            // 显示删除成功提示
                            uni.showToast({
                                title: '删除成功',
                                icon: 'success',
                                duration: 1500
                            });
                        }
                    }
                });
            } else {
                // 没有选中的元素
                uni.showToast({
                    title: '请先选择要删除的元素',
                    icon: 'none',
                    duration: 2000
                });
            }
        },

        // 获取元素类型的中文名称
        getItemTypeName(type) {
            const typeNames = {
                'material': '素材',
                'add': '图片',
                'template': '模板',
                'text': '文字',
                'pen': '画笔'
            };
            return typeNames[type] || '元素';
        },

        // 复制功能
        cornerCopy() {
            console.log('<<<');

            // 找到当前选中的图片
            const selectedItem = this.images.find(item => item.selected);

            if (selectedItem) {
                // 创建副本，稍微偏移位置避免重叠
                const copiedItem = {
                    ...selectedItem,
                    x: selectedItem.x + 20,
                    y: selectedItem.y + 20,
                    selected: false // 新复制的元素不选中
                };

                // 如果是画笔类型，需要深拷贝路径数组
                if (selectedItem.type === 'pen' && selectedItem.path) {
                    copiedItem.path = [...selectedItem.path];
                }

                // 取消原来元素的选中状态
                selectedItem.selected = false;

                // 添加到数组
                this.images.push(copiedItem);

                console.log('已复制元素:', selectedItem.type, '总元素数量:', this.images.length);

                // 重新绘制画布
                this.drawImgs();

                // 显示复制成功提示
                uni.showToast({
                    title: '复制成功',
                    icon: 'none',
                    duration: 1500
                });
            } else {
                // 没有选中的元素
                uni.showToast({
                    title: '请先选择要复制的元素',
                    icon: 'none',
                    duration: 2000
                });
            }
        },
        // 缩放
        rotateScale(event) {
            if (!this._scaling || !this._scaleStart) return;

            const touch = event.touches[0];
            const img = this.images.find(item => item.selected);
            if (!img) return;

            // 兼容微信小程序 touch.clientY/touch.clientX
            const touchX = touch.x !== undefined ? touch.x : touch.clientX;
            const touchY = touch.y !== undefined ? touch.y : touch.clientY;

            // 计算当前触摸点相对于初始触摸点的偏移
            const deltaX = touchX - this._scaleStart.x;
            const deltaY = touchY - this._scaleStart.y;

            // 判断缩放方向
            const absDeltaX = Math.abs(deltaX);
            const absDeltaY = Math.abs(deltaY);

            let scaleType = 'both';
            let newW = this._scaleStart.w;
            let newH = this._scaleStart.h;

            // 根据主要移动方向决定缩放类型
            if (absDeltaX > absDeltaY && absDeltaX > 10) {
                // 水平缩放
                scaleType = 'horizontal';
                newW = Math.max(20, this._scaleStart.w + deltaX);
                newH = this._scaleStart.h; // 高度不变
            } else if (absDeltaY > absDeltaX && absDeltaY > 10) {
                // 垂直缩放
                scaleType = 'vertical';
                newW = this._scaleStart.w; // 宽度不变
                newH = Math.max(20, this._scaleStart.h + deltaY);
            } else if (absDeltaX > 10 || absDeltaY > 10) {
                // 同时缩放（对角线方向）
                scaleType = 'both';
                const avgDelta = (deltaX + deltaY) / 2;
                newW = Math.max(20, this._scaleStart.w + avgDelta);
                newH = Math.max(20, this._scaleStart.h + avgDelta);
            }

            // 限制最大尺寸
            newW = Math.min(500, newW);
            newH = Math.min(500, newH);

            // 更新缩放类型状态
            this._scaleType = scaleType;

            // 更新图片尺寸，保持左上角位置不变
            img.x = this._scaleStart.originX;
            img.y = this._scaleStart.originY;
            img.w = newW;
            img.h = newH;

            console.log(`缩放类型: ${scaleType}, 偏移: (${deltaX.toFixed(0)}, ${deltaY.toFixed(0)}), 新尺寸: ${newW.toFixed(0)}x${newH.toFixed(0)}`);
            this.drawImgs();
        },
        touchStartScale(event) {
            const touch = event.touches[0];
            const img = this.images.find(item => item.selected);
            if (!img) return;

            // 兼容微信小程序 touch.clientY/touch.clientX
            const touchX = touch.x !== undefined ? touch.x : touch.clientX;
            const touchY = touch.y !== undefined ? touch.y : touch.clientY;

            this._scaling = true;

            // 记录缩放开始时的状态（以左上角为缩放中心点）
            this._scaleStart = {
                x: touchX,
                y: touchY,
                w: img.w,
                h: img.h,
                originX: img.x,  // 左上角X坐标
                originY: img.y   // 左上角Y坐标
            };

            console.log('开始缩放（左上角为中心），初始尺寸:', img.w, 'x', img.h);
        },

        async drawImgs() {
            if (!this.ctx) {
                this.canvasV()
                console.log('没有');
            }
            this.ctx.clearRect(0, 0, 400, 400)
            console.log(this.images, '没有');
            if (this.finish) {
                uni.showLoading({
                    title: '加载中...'
                });
            }
            if (this.finish && !this.images.length) {
                uni.showToast({ title: '请添加内容', icon: 'error' });
                uni.hideLoading();
                return
            }
            var v = false
            this.images.forEach(img => {
                if (img.switch) {
                    if (img.type == 'backgorund') {
                        console.log(img.url, 'img.url');
                        // 画布尺寸
                        const canvasW = 400;
                        const canvasH = 500;
                        // 以高度为基准铺满画布，高度撑满，宽度等比缩放，左右可能裁剪
                        const scale = canvasH / img.h;
                        const drawW = img.w * scale;
                        const drawH = canvasH;
                        const offsetX = (canvasW - drawW) / 2;
                        const offsetY = 0;
                        this.ctx.drawImage(img.url, offsetX, offsetY, drawW, drawH);
                        if (this.finish) {
                            console.log(this.finish, '====<');
                            this.arring.push(true)
                        }
                    } else if (img.type == 'material' || img.type == 'add' || img.type == 'template' || img.type == 'penIamge') {
                        console.log(img.rotate, '旋转角度');

                        // 保存当前上下文
                        this.ctx.save();
                        // 计算图片中心点
                        const cx = img.x + img.w / 2;
                        const cy = img.y + img.h / 2;
                        // 平移到图片中心点
                        this.ctx.translate(cx, cy);
                        // 旋转（即使角度为0也应用变换，保持一致性）
                        this.ctx.rotate(img.rotate || 0);

                        if (img.url.startsWith("https:")) {
                            v = true
                            console.log(img.url, 11111);
                            var that = this
                            uni.getImageInfo({
                                src: img.url,
                                success(e) {

                                    img.url = e.path
                                    console.log(that.ctx, 'e.path');

                                    // 绘制图片时需要相对于中心点偏移
                                    that.ctx.drawImage(img.url, -img.w / 2, -img.h / 2, img.w, img.h);
                                    // 恢复画布状态
                                    that.ctx.restore()
                                    that.ctx.draw()
                                    // 选中高亮
                                    if (that.finish) {
                                        that.arring.push(true)
                                    }
                                    v = false
                                }
                            })
                        } else {
                            console.log(2222);
                            // 绘制图片时需要相对于中心点偏移
                            this.ctx.drawImage(img.url, -img.w / 2, -img.h / 2, img.w, img.h);
                            // 恢复画布状态
                            this.ctx.restore()
                            // 选中高亮
                            if (this.finish) {
                                this.arring.push(true)
                            }
                        }

                    } else if (img.type == 'text') {
                        if (img.text) {
                            this.ctx.setFontSize(img.size || 36)
                            this.ctx.setFillStyle(img.color || '#fff')
                            this.ctx.setFontFamily && this.ctx.setFontFamily(img.font || 'Arial')
                            this.ctx.setTextAlign('left')
                            this.ctx.setTextBaseline('top')
                            // 计算宽高
                            const metrics = this.ctx.measureText(img.text)
                            img.w = metrics.width
                            img.h = img.size || 36

                            // 应用旋转变换（即使角度为0也应用，保持一致性）
                            this.ctx.save();
                            const cx = img.x + img.w / 2;
                            const cy = img.y + img.h / 2;
                            this.ctx.translate(cx, cy);
                            this.ctx.rotate(img.rotate || 0);
                            this.ctx.fillText(img.text, -img.w / 2, -img.h / 2);
                            this.ctx.restore();
                            if (this.finish) {
                                console.log('<<<<--3');
                                this.arring.push(true)
                            }
                        }
                    }
                    else if (img.type == 'pen') {
                        if (img.path && img.path.length > 1) {
                            this.ctx.beginPath();
                            this.ctx.setStrokeStyle(img.color || '#8dd800');
                            this.ctx.setLineWidth(img.size || 6);
                            if (img.style === 'dashed') {
                                this.ctx.setLineDash && this.ctx.setLineDash([10, 8], 0);
                            } else {
                                this.ctx.setLineDash && this.ctx.setLineDash([], 0);
                            }

                            // 应用旋转变换（即使角度为0也应用，保持一致性）
                            this.ctx.save();
                            // 计算画笔路径的边界框中心点
                            const cx = img.x + img.w / 2;
                            const cy = img.y + img.h / 2;
                            this.ctx.translate(cx, cy);
                            this.ctx.rotate(img.rotate || 0);

                            // 绘制相对于中心点的路径
                            const firstPoint = img.path[0];
                            this.ctx.moveTo(firstPoint.x - cx, firstPoint.y - cy);
                            for (let i = 1; i < img.path.length; i++) {
                                this.ctx.lineTo(img.path[i].x - cx, img.path[i].y - cy);
                            }
                            this.ctx.stroke();
                            this.ctx.restore();

                            this.ctx.setLineDash && this.ctx.setLineDash([], 0);
                            if (this.finish) {
                                console.log('<<<<--4');
                                this.arring.push(true)
                            }
                        }
                    }
                }
            })
            if (!v) {
                this.ctx.draw()
            }
        },
        onTouchStart(e) {
            const touch = e.touches[0]
            let found = false

            // 倒序遍历，优先选中上层图片
            for (let i = this.images.length - 1; i >= 0; i--) {
                const img = this.images[i]
                if (
                    touch.x >= img.x &&
                    touch.x <= img.x + img.w &&
                    touch.y >= img.y &&
                    touch.y <= img.y + img.h
                ) {
                    this.images.forEach(item => (item.selected = false))
                    img.selected = true
                    this.dragging = true
                    this.startTouch = { x: touch.x, y: touch.y }
                    this.startPos = { x: img.x, y: img.y }
                    found = true
                    // 将选中的元素移到数组最后（最上层）
                    this.images.splice(i, 1)
                    this.images.push(img)
                    console.log(img, 'image');
                    // this.selected(img)
                    break
                }
            }
            if (!found) {
                this.images.forEach(item => (item.selected = false))
                this.dragging = false
            }
        },
        onTouchMove(e) {
            console.log(this.materialVisible, this.dragging, '划线');
            if (!this.dragging && this.materialVisible != 'pen') return
            if (this.materialVisible == 'pen') {
                const touch = e.touches[0];
                if (!this.penLastPoint) {
                    // 开始新的线条
                    this.penLastPoint = { x: touch.x, y: touch.y };
                    if (!this.penPath) this.penPath = [];
                    this.penPath.push({ x: touch.x, y: touch.y });

                    // 记录当前线条的属性（锁定颜色、大小、样式）
                    this.currentPenColor = this.penColor;
                    this.currentPenSize = this.penSize;
                    this.currentPenStyle = this.penStyle;

                    console.log('开始新线条，颜色:', this.currentPenColor, '大小:', this.currentPenSize);
                } else {
                    // 继续当前线条
                    this.penPath.push({ x: touch.x, y: touch.y });
                    if (!this.ctx) {
                        this.ctx = uni.createCanvasContext('canvasId', this);
                    }
                    // 只画当前线段，使用锁定的属性
                    this.ctx.beginPath();
                    this.ctx.setStrokeStyle(this.currentPenColor);
                    this.ctx.setLineWidth(this.currentPenSize);
                    if (this.currentPenStyle === 'dashed') {
                        // 虚线
                        this.ctx.setLineDash && this.ctx.setLineDash([10, 8], 0);
                    } else {
                        this.ctx.setLineDash && this.ctx.setLineDash([], 0);
                    }
                    this.ctx.moveTo(this.penLastPoint.x, this.penLastPoint.y);
                    this.ctx.lineTo(touch.x, touch.y);
                    this.ctx.stroke();
                    this.ctx.draw(true); // 保留之前内容
                    this.penLastPoint = { x: touch.x, y: touch.y };
                }
            } else {
                const touch = e.touches[0];
                const r = 14;
                const img = this.images.find(item => item.selected);

                // 拖动
                if (img) {
                    // console.log('拖动');
                    const dx = touch.x - this.startTouch.x;
                    const dy = touch.y - this.startTouch.y;
                    img.x = this.startPos.x + dx;
                    img.y = this.startPos.y + dy;
                    this.drawImgs();
                }
            }
        },
        onTouchEnd() {
            this.dragging = false
            this._rotating = false
            this._scaling = false

            // 如果是画笔模式，结束当前线条
            if (this.materialVisible === 'pen') {
                this.handlePenEnd();
            }
        },
        ringhtClick() {
            if (!this.imagesOnes.length) return uni.showToast({
                title: '没有上一步',
                icon: 'none',
                duration: 1500
            });
            this.images.push(this.imagesOnes[this.imagesOnes.length - 1])
            this.imagesOnes.pop()
            this.drawImgs();
        },
        leftClick() {
            if (this.images.length) return uni.showToast({
                title: '没有下一步',
                icon: 'none',
                duration: 1500
            });
            if (this.images.length > 0) {
                // const removed = this.images.pop();
                this.imagesOnes.push(this.images[this.images.length - 1]);
                this.images.pop()
                this.drawImgs();
            }
        },
        leftClickOff(idx) {
            if (this.images.length > 0 && idx >= 0 && idx < this.images.length) {
                // 将要删除的元素保存到 imagesOnes
                this.imagesOnes.push(this.images[idx]);
                // 删除指定下标的元素
                this.images.splice(idx, 1);
                this.drawImgs();
            }
        },

        // 关闭
        downs() {
            this.materialVisible = ''
        },
        // 隐藏图层
        hideIf(img) {
            if (img.selected) {
                img.selected = false
            }
            img.switch = !img.switch
            if (img.switch) {

                this.images.forEach((item, idx) => {
                    item.selected = false
                })

                img.selected = true
            }
            this.drawImgs()
        },
        // 清空
        clear() {
            uni.showModal({
                title: '确认删除',
                content: `确定要把所有内容清空吗？`,
                confirmText: '删除',
                cancelText: '取消',
                confirmColor: '#e53935',
                success: (res) => {
                    if (res.confirm) {
                        this.imagesOnes = this.images
                        this.images = []
                        this.drawImgs()

                        // 显示删除成功提示
                        uni.showToast({
                            title: '删除成功',
                            icon: 'success',
                            duration: 1500
                        });
                    }
                }
            });
        },

        // ========== 新的画笔功能方法 ==========

        // 处理画笔移动
        handlePenMove(e) {
            const touch = e.touches[0];
            const currentPoint = { x: touch.x, y: touch.y };

            if (!this.isDrawing) {
                // 开始新的线条
                this.startNewStroke(currentPoint);
            } else {
                // 继续当前线条
                this.continueStroke(currentPoint);
            }
        },

        // 开始新的线条
        startNewStroke(point) {
            console.log('开始新线条，颜色:', this.penColor, '大小:', this.penSize);

            this.isDrawing = true;
            this.penPath = [point];
            this.penLastPoint = point;

            // 创建当前线条对象，锁定属性
            this.currentStroke = {
                color: this.penColor,
                size: this.penSize,
                style: this.penStyle,
                path: this.penPath
            };

            // 初始化canvas上下文
            if (!this.ctx) {
                this.ctx = uni.createCanvasContext('canvasId', this);
            }
        },

        // 继续当前线条
        continueStroke(point) {
            if (!this.isDrawing || !this.penLastPoint) return;

            // 添加点到路径
            this.penPath.push(point);

            // 绘制线段
            this.ctx.beginPath();
            this.ctx.setStrokeStyle(this.currentStroke.color);
            this.ctx.setLineWidth(this.currentStroke.size);

            if (this.currentStroke.style === 'dashed') {
                this.ctx.setLineDash && this.ctx.setLineDash([10, 8], 0);
            } else {
                this.ctx.setLineDash && this.ctx.setLineDash([], 0);
            }

            this.ctx.moveTo(this.penLastPoint.x, this.penLastPoint.y);
            this.ctx.lineTo(point.x, point.y);
            this.ctx.stroke();
            this.ctx.draw(true); // 保留之前内容

            this.penLastPoint = point;
        },

        // 结束画笔
        async handlePenEnd() {
            console.log('handlePenEnd 被调用');

            // 检查是否有有效的路径可以保存
            if (!this.penPath || this.penPath.length < 2) {
                console.log('没有有效的线条可保存，路径为空或点数不足');
                this.resetPenState();
                return;
            }

            // 检查是否有当前线条的属性信息
            if (!this.currentStroke) {
                console.log('没有当前线条属性，使用默认属性');
                this.currentStroke = {
                    color: this.penColor,
                    size: this.penSize,
                    style: this.penStyle
                };
            }

            // 计算边界框
            const xs = this.penPath.map(p => p.x);
            const ys = this.penPath.map(p => p.y);
            const minX = Math.min(...xs);
            const maxX = Math.max(...xs);
            const minY = Math.min(...ys);
            const maxY = Math.max(...ys);

            console.log(this.penPath, 'this.penPath');


            // 保存线条为图层
            const newPenItem = {
                type: 'pen',
                path: this.penPath,
                color: this.currentStroke.color,
                size: this.currentStroke.size,
                style: this.currentStroke.style,
                selected: false,
                switch: true,
                x: minX,
                y: minY,
                w: Math.max(1, maxX - minX),
                h: Math.max(1, maxY - minY),
                rotate: 0,
            };

            // 在添加新元素前，先保存当前状态用于撤销
            this.saveHistoryState();

            // 添加新元素
            this.images.push(newPenItem);
            console.log('✅ 新线条已成功添加到images数组!');

            // 重置状态
            this.resetPenState();

            // 重新绘制所有内容
            this.drawImgs();
            this.optimizePath(newPenItem);

            // 显示提示
            uni.showToast({
                title: '线条已保存',
                icon: 'success',
                duration: 1000
            });
        },

        // 重置画笔状态
        resetPenState() {
            this.isDrawing = false;
            this.currentStroke = null;
            this.penPath = [];
            this.penLastPoint = null;
        },

        // 优化路径数据，减少点的数量
        optimizePath(path) {
            // if (path.path.length <= 2) return path;

            // 创建临时画布上下文
            const tempCtx = uni.createCanvasContext('canvasIdPen', this);

            // 设置临时画布尺寸为路径实际大小
            this.width = path.w
            this.height = path.h
            console.log(path, 'path');


            tempCtx.beginPath();
            tempCtx.setStrokeStyle(path.color || '#8dd800');
            tempCtx.setLineWidth(path.size || 6);
            if (path.style === 'dashed') {
                tempCtx.setLineDash && tempCtx.setLineDash([10, 8], 0);
            } else {
                tempCtx.setLineDash && tempCtx.setLineDash([], 0);
            }

            // 应用旋转变换（即使角度为0也应用，保持一致性）
            tempCtx.save();
            // 计算画笔路径的边界框中心点
            const cx = path.x + path.w / 2;
            const cy = path.y + path.h / 2;
            tempCtx.translate(cx, cy);
            tempCtx.rotate(path.rotate || 0);

            // 绘制相对于中心点的路径
            const firstPoint = path.path[0];
            tempCtx.moveTo(firstPoint.x - cx, firstPoint.y - cy);
            for (let i = 1; i < path.path.length; i++) {
                tempCtx.lineTo(path.path[i].x - cx, path.path[i].y - cy);
            }
            tempCtx.stroke();
            tempCtx.restore();
            tempCtx.draw()

            uni.canvasToTempFilePath({
                canvasId: 'canvasIdPen',
                x: path.x,
                y: path.y,
                width: path.w + 20,
                height: path.h + 20,
                success: (res) => {
                    console.log(res, '✅ ttt!');
                    this.aff = res.tempFilePath
                    // 添加新元素
                    this.images[this.images.length - 1] = {
                        ...path,
                        path: null,
                        url: res.tempFilePath,
                        w: path.w + 15,
                        h: path.h + 15,
                        type: 'penIamge'
                    }
                    console.log(this.images, '✅ 新线条已成功添加到images数组!');
                    tempCtx.clearRect();
                    // 重新绘制所有内容
                    this.drawImgs();
                }
            }, this);
        },

        // 保存当前状态用于撤销
        saveHistoryState() {
            // 创建当前图层状态的深拷贝
            const currentState = JSON.parse(JSON.stringify(this.images));

            // 如果历史记录数组不存在，则创建
            if (!this.historyStates) {
                this.historyStates = [];
            }

            // 限制历史记录的数量，防止内存占用过多
            if (this.historyStates.length > 20) {
                this.historyStates.shift(); // 移除最旧的记录
            }

            // 保存当前状态
            this.historyStates.push(currentState);
            console.log('已保存历史状态，当前历史记录数:', this.historyStates.length);
        },

        // 撤销上一步操作
        undoLastAction() {
            // 检查是否有历史记录
            if (!this.historyStates || this.historyStates.length === 0) {
                uni.showToast({
                    title: '没有可撤销的操作',
                    icon: 'none',
                    duration: 1500
                });
                return;
            }

            // 保存当前状态到重做栈
            if (!this.redoStates) {
                this.redoStates = [];
            }
            this.redoStates.push(JSON.parse(JSON.stringify(this.images)));

            // 恢复到上一个状态
            const previousState = this.historyStates.pop();
            this.images = previousState;

            // 重新绘制
            this.drawImgs();

            uni.showToast({
                title: '已撤销上一步操作',
                icon: 'success',
                duration: 1000
            });
        },

        // 重做上一步被撤销的操作
        redoLastAction() {
            // 检查是否有重做记录
            if (!this.redoStates || this.redoStates.length === 0) {
                uni.showToast({
                    title: '没有可重做的操作',
                    icon: 'none',
                    duration: 1500
                });
                return;
            }

            // 保存当前状态到撤销栈
            this.saveHistoryState();

            // 恢复到下一个状态
            const nextState = this.redoStates.pop();
            this.images = nextState;

            // 重新绘制
            this.drawImgs();

            uni.showToast({
                title: '已重做操作',
                icon: 'success',
                duration: 1000
            });
        }
    }
}
</script>

<style scoped>
.container {
    background: #222;
    min-height: 85vh;
    height: 85vh;
    position: absolute;
    padding-bottom: 90rpx;
    overflow: hidden;
    width: 100%;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 24rpx 0 0rpx;
    gap: 20rpx;
    width: 100%;
}

.header-btn {
    background: transparent;
    border: 1rpx solid #b6ff3b;
    color: #b6ff3b;
    border-radius: 8rpx;
    /* padding: 8rpx 32rpx; */
    font-size: 28rpx;
}

.header-btn.save {
    /* margin-left: 16rpx; */
}

.main-area {
    margin: 40rpx auto 0 auto;
    background: #333;
    width: 100%;
    height: 81%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12rpx;
    position: absolute;
    overflow: hidden;
    /* border: 1px solid red; */
}

.upload-btn {
    position: absolute;
    left: 20rpx;
    top: 20rpx;
    z-index: 2;
    background: #b6ff3b;
    color: #222;
    border-radius: 8rpx;
    padding: 8rpx 24rpx;
    font-size: 24rpx;
    border: none;
}

.main-img {

    width: 100%;
    height: 950rpx;
    object-fit: contain;
    /* border: 1rpx solid red; */
}

.draggable-img {
    position: absolute;
    z-index: 2;
    /* 可根据需要调整初始大小 */
    /*  */
    background-size: 20px 20px;
    position: absolute;
    background-position: 0 0, 10px 10px;
    /* border: 1px solid red; */
}

.autos {
    background-color: #f5f5f5;
    background-image: linear-gradient(45deg, #e0e0e0 25%, transparent 25%, transparent 75%, #e0e0e0 75%),
        linear-gradient(45deg, #e0e0e0 25%, transparent 25%, transparent 75%, #e0e0e0 75%);
}

.toolbar {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100vw;
    background: #111;
    display: flex;
    justify-content: space-around;
    align-items: center;
    height: 140rpx;
    border-top: 1rpx solid #333;
    z-index: 10;
}

.toolbar-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #fff;
    font-size: 22rpx;
}

.toolbar-icon {
    width: 40rpx;
    height: 40rpx;
    margin-bottom: 4rpx;
}

.layer-box {
    position: fixed;
    right: 0rpx;
    bottom: 140rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100rpx;
    /* border: 1px solid red; */
    color: #fff;
}

.layer-btn {
    background: #333;
    color: #fff;
    border-radius: 8rpx;
    padding: 12rpx 32rpx;
    font-size: 26rpx;
    border: none;
    z-index: 2;
    width: 100%;
}

.sidebarBox {
    position: absolute;
    left: 0;
    bottom: 0rpx;
    width: 100%;
    background: #191919;
    /* border: 1px solid blue; */
    z-index: 3;
}

.sidebar {
    width: 100%;
    height: 40vh;
    z-index: 5;
    display: flex;
    /* flex-direction: column; */
    padding-top: 20rpx;
}

.sidebar-section {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    gap: 10rpx;
    padding: 0 10rpx;
}

.sidebar-title {
    color: #bbb;
    font-size: 22rpx;
    margin-right: 16rpx;
    padding: 8rpx 0;
}

.sidebar-title.active {
    color: #b6ff3b;
    border-bottom: 2rpx solid #b6ff3b;
}

.sidebar-menu {
    /* margin-top: 30rpx; */
    display: flex;
    gap: 10rpx;
}

.sidebar-menu-item {
    color: #bbb;
    font-size: 22rpx;
    padding: 8rpx 16rpx;
    border-radius: 6rpx;
}

.sidebar-menu-item.active {
    background: #222;
    color: #b6ff3b;
}

.material-content {
    /* position: absolute;
    left: 180rpx;
    top: 120rpx;
    right: 0; */
    /* height: 72vh; */
    background: transparent;
    z-index: 4;
    /* padding: 24rpx 0 0 0; */
    overflow-y: auto;
}

.material-row {
    display: flex;
    flex-direction: row;
    gap: 24rpx;
    margin-bottom: 24rpx;
    justify-content: flex-start;
    padding-left: 24rpx;
}

.material-card {
    background: #222;
    border-radius: 12rpx;
    width: 160rpx;
    height: 200rpx;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    align-items: center;
    position: relative;
    box-shadow: 0 2rpx 8rpx #0006;
    padding: 12rpx 0;
    border: 2px solid #222;
}

.material-circle {
    border: 2px solid #fff;
}

.material-img {
    width: 120rpx;
    height: 120rpx;
    object-fit: contain;
    margin-bottom: 8rpx;
}

.material-title {
    color: #fff;
    font-size: 20rpx;
    margin-bottom: 4rpx;
    text-align: center;
}

.material-tag {
    position: absolute;
    top: 8rpx;
    right: 8rpx;
    background: #b6ff3b;
    color: #222;
    border-radius: 50%;
    width: 32rpx;
    height: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20rpx;
    font-weight: bold;
}

.material-vip {
    position: absolute;
    top: 8rpx;
    right: 8rpx;
    background: #ffd700;
    color: #222;
    border-radius: 8rpx;
    padding: 2rpx 8rpx;
    font-size: 16rpx;
    font-weight: bold;
}

.text-toolbar {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0rpx;
    background: #222;
    border-radius: 16rpx 16rpx 0 0;
    padding: 32rpx 24rpx 24rpx 24rpx;
    z-index: 3;
    box-shadow: 0 -4rpx 24rpx #000a;
}

.text-toolbar-row {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
}

.text-input {
    flex: 1;
    background: #333;
    color: #fff;
    border: 1rpx solid #444;
    border-radius: 8rpx;
    padding: 12rpx 20rpx;
    font-size: 28rpx;
    outline: none;
}

.font-row {
    flex-wrap: wrap;
    gap: 16rpx;
}

.font-btn {
    background: #333;
    color: #fff;
    border: 1rpx solid #444;
    border-radius: 8rpx;
    padding: 8rpx 24rpx;
    font-size: 26rpx;
    margin-right: 12rpx;
    margin-bottom: 8rpx;
    transition: border 0.2s, color 0.2s;
}

.font-btn.active {
    border: 2rpx solid #b6ff3b;
    color: #b6ff3b;
}

.size-row {
    gap: 16rpx;
}

.label {
    color: #bbb;
    font-size: 22rpx;
    margin-right: 12rpx;
}

.size-slider {
    flex: 1;
    margin: 0 12rpx;
}

.size-value {
    color: #b6ff3b;
    font-size: 24rpx;
    min-width: 48rpx;
    text-align: right;
}

.color-row {
    gap: 16rpx;
}

.color-list {
    display: flex;
    flex-direction: row;
    gap: 16rpx;
    margin-left: 12rpx;
}

.color-circle {
    width: 36rpx;
    height: 36rpx;
    border-radius: 50%;
    border: 2rpx solid #888;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    box-sizing: border-box;
}

.color-circle.selected {
    border: 2rpx solid #b6ff3b;
}

.color-selected {
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    background: #fff;
    position: absolute;
    top: 10rpx;
    left: 10rpx;
    box-shadow: 0 0 4rpx #b6ff3b;
}

.end-btn {
    width: 90%;
    height: 40rpx;
    text-align: right;
    color: #fff;
    margin: 10rpx auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}



/* 画线 */
.pen-toolbar {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    background: #222;
    border-radius: 16rpx 16rpx 0 0;
    padding: 32rpx 24rpx 24rpx 24rpx;
    z-index: 3;
    box-shadow: 0 -4rpx 24rpx #000a;
}

.pen-row {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
    gap: 16rpx;
}

.pen-color-list {
    display: flex;
    flex-direction: row;
    gap: 16rpx;
    margin-left: 12rpx;
}

.pen-color-circle {
    width: 36rpx;
    height: 36rpx;
    border-radius: 50%;
    border: 2rpx solid #888;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    box-sizing: border-box;
}

.pen-color-circle.selected {
    border: 2rpx solid #b6ff3b;
}

.pen-color-selected {
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    background: #fff;
    position: absolute;
    top: 10rpx;
    left: 10rpx;
    box-shadow: 0 0 4rpx #b6ff3b;
}

.pen-style-list {
    display: flex;
    flex-direction: row;
    gap: 24rpx;
    margin-left: 12rpx;
}

.pen-style-item {
    display: flex;
    align-items: center;
    background: #333;
    border: 1rpx solid #444;
    border-radius: 8rpx;
    padding: 8rpx 16rpx;
    font-size: 24rpx;
    color: #bbb;
    cursor: pointer;
    transition: border 0.2s, color 0.2s;
}

.pen-style-item.active {
    border: 2rpx solid #b6ff3b;
    color: #8dd800;
    background: #222;
}

/* 模板 */
/* template-bar 样式补全 */
.template-bar {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    background: #191919;
    border-radius: 16rpx 16rpx 0 0;
    z-index: 3;
    padding: 24rpx 0 0 0;
    box-shadow: 0 -4rpx 24rpx #000a;
}

.template-tabs {
    display: flex;
    flex-direction: row;
    gap: 24rpx;
    padding: 0 24rpx;
    margin-bottom: 16rpx;
}

.template-tab {
    color: #bbb;
    font-size: 24rpx;
    padding: 8rpx 16rpx;
    border-radius: 8rpx;
    min-width: 50px;
    text-align: center;
}

.template-tab.active {
    color: #b6ff3b;
    background: #222;
}

.template-list {
    display: flex;
    flex-direction: row;
    gap: 24rpx;
    padding: 0 24rpx 24rpx 24rpx;
    overflow-x: auto;
    white-space: nowrap;
}

.template-card {
    display: inline-block;
    background: #222;
    border-radius: 12rpx;
    width: 320rpx;
    margin-right: 24rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 8rpx #0006;
    vertical-align: top;
}

.template-img {
    width: 100%;
    height: 180rpx;
    object-fit: cover;
    display: block;
}

.template-info {
    padding: 12rpx 16rpx 8rpx 16rpx;
}

.template-title {
    color: #fff;
    font-size: 26rpx;
    margin-bottom: 8rpx;
}

.template-meta {
    display: flex;
    align-items: center;
    gap: 8rpx;
}

.template-vip-icon {
    width: 28rpx;
    height: 28rpx;
}

.template-view {
    color: #bbb;
    font-size: 22rpx;
}

/* 图层面板样式 */
.layer-panel {
    position: fixed;
    right: 15rpx;
    top: 200rpx;
    width: 320rpx;
    max-height: 60vh;
    background: #222;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 24rpx #000a;
    z-index: 20;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.layer-tabs {
    display: flex;
    flex-direction: row;
    border-bottom: 1rpx solid #333;
    background: #191919;
}

.layer-tab {
    flex: 1;
    text-align: center;
    color: #bbb;
    font-size: 22rpx;
    padding: 16rpx 0;
    cursor: pointer;
    transition: color 0.2s, background 0.2s;
}

.layer-tab.active {
    color: #b6ff3b;
    background: #222;
    border-bottom: 2rpx solid #b6ff3b;
}

.layer-list {
    flex: 1;
    overflow-y: auto;
    padding: 12rpx 0;
}

.layer-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10rpx 16rpx;
    border-bottom: 1rpx solid #333;
    gap: 16rpx;
}

.layer-eye {
    width: 32rpx;
    height: 32rpx;
    margin-right: 8rpx;
}

.layer-thumb {
    width: 48rpx;
    height: 48rpx;
    background: #333;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.layer-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.layer-text {
    font-size: 22rpx;
    color: #fff;
    text-align: center;
    width: 100%;
}

.layer-footer {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12rpx 0;
    background: #191919;
    border-top: 1rpx solid #333;
    gap: 8rpx;
}

.layer-footer-icon {
    width: 28rpx;
    height: 28rpx;
}

.layer-footer-text {
    color: #bbb;
    font-size: 22rpx;
}

.img-border-ops {
    position: absolute;
    border: 2px dashed #b6ff3b;
    border-radius: 12rpx;
    box-sizing: border-box;
    pointer-events: none;
}

.border-rect {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    border: 2px solid #1e01fc;
    border-radius: 12rpx;
    pointer-events: none;
}

.corner-btn {
    position: absolute;
    width: 42rpx;
    height: 42rpx;
    background: #8dd800;
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    font-weight: bold;
    z-index: 2;
    pointer-events: auto;
    box-shadow: 0 2rpx 8rpx #0006;
    cursor: pointer;
    user-select: none;
}

.corner-del {
    left: -16rpx;
    top: -16rpx;
    background: #e53935;
}

.corner-copy {
    left: -16rpx;
    bottom: -16rpx;
    background: #8dd800;
}

.corner-rotate {
    right: -16rpx;
    top: -16rpx;
    background: #8dd800;
}

.corner-scale {
    right: -16rpx;
    bottom: -16rpx;
    background: #8dd800;
}
</style>
