<template>
    <canvas :style="{
        width: width + 'px',
        height: height + 'px',
        left: info.left + 'px',
        top: info.top + 'px',
    }" @touchstart="handleTouchStart" @touchmove="handleTouchMove" @touchend="handleTouchEnd" canvas-id="drawLines"
        :class="Tress ? 'drawLines' : 'displayFalse'" id="drawLines">
    </canvas>
</template>

<script>
export default {
    props: {
        width: {
            type: String,
            default: ''
        },
        height: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            type: null,
            Tress: false,
            ctxs: null,
            penLastPoint: null,
            penPath: [],
            currentPenColor: '#000',
            currentPenSize: 2,
            currentPenStyle: '',

            info: {},
        }
    },
    watch: {
        info: {
            handler(newVal, oldVal) {
                console.log(this.info, 'colorLine');
            },
            deep: true
        }
    },
    mounted() {
        this.ctxs = uni.createCanvasContext('drawLines', this)

        uni.$on('colorLine', (e) => {
            this.type = e
            this.Tress = e.tool == 'brush' ? true : false
        })
    },
    onReady() {
    },

    methods: {
        handleTouchMove(e) {
            console.log(this.ctxs, '123123');

            if (!this.ctxs) return;
            const touch = e.touches[0];
            if (!this.penLastPoint) {
                // 开始新的线条
                this.penLastPoint = { x: touch.x, y: touch.y };
                if (!this.penPath) this.penPath = [];
                this.penPath.push({ x: touch.x, y: touch.y });

                // 记录当前线条的属性（锁定颜色、大小、样式）
                this.currentPenColor = this.type.color;
                this.currentPenSize = this.type.size;
                this.currentPenStyle = this.type.type;

                console.log('开始新线条，颜色:', this.currentPenColor, '大小:', this.currentPenSize);
            } else {
                // 继续当前线条
                this.penPath.push({ x: touch.x, y: touch.y });
                if (!this.ctxs) {
                    this.ctxs = uni.createCanvasContext('canvasId', this);
                }
                // 只画当前线段，使用锁定的属性
                this.ctxs.beginPath();
                this.ctxs.setStrokeStyle(this.currentPenColor);
                this.ctxs.setLineWidth(this.currentPenSize);
                if (this.currentPenStyle === 'dashed') {
                    // 虚线
                    this.ctxs.setLineDash && this.ctxs.setLineDash([10, 8], 0);
                } else {
                    this.ctxs.setLineDash && this.ctxs.setLineDash([], 0);
                }
                this.ctxs.moveTo(this.penLastPoint.x, this.penLastPoint.y);
                this.ctxs.lineTo(touch.x, touch.y);
                this.ctxs.stroke();
                this.ctxs.draw(true); // 保留之前内容
                this.penLastPoint = { x: touch.x, y: touch.y };
            }
        },
        handleTouchStart() { },
        handleTouchEnd(e) {
            // 检查是否有有效的路径可以保存
            if (!this.penPath || this.penPath.length < 2) {
                console.log('没有有效的线条可保存，路径为空或点数不足');
                // this.resetPenState();
                return;
            }

            const xs = this.penPath.map(p => p.x);
            const ys = this.penPath.map(p => p.y);
            const minX = Math.min(...xs);
            const maxX = Math.max(...xs);
            const minY = Math.min(...ys);
            const maxY = Math.max(...ys);

            console.log(this.penPath, 'this.penPath');


            // 保存线条为图层
            const newPenItem = {
                type: 'pen',
                path: this.penPath,
                color: this.currentPenColor,
                size: this.currentPenSize,
                style: this.currentPenStyle,
                selected: false,
                switch: true,
                x: minX,
                y: minY,
                w: Math.max(1, maxX - minX),
                h: Math.max(1, maxY - minY),
                rotate: 0,
            };

            this.optimizePath(newPenItem);
        },

        optimizePath(path) {
            // if (path.path.length <= 2) return path;

            // 创建临时画布上下文
            const tempCtx = this.ctxs

            // 设置临时画布尺寸为路径实际大小
            // this.width = path.w
            // this.height = path.h
            console.log(path, 'path');


            tempCtx.beginPath();
            tempCtx.setStrokeStyle(path.color || '#8dd800');
            tempCtx.setLineWidth(path.size || 6);
            if (path.style === 'dashed') {
                tempCtx.setLineDash && tempCtx.setLineDash([10, 8], 0);
            } else {
                tempCtx.setLineDash && tempCtx.setLineDash([], 0);
            }

            // 应用旋转变换（即使角度为0也应用，保持一致性）
            tempCtx.save();
            // 计算画笔路径的边界框中心点
            const cx = path.x + path.w / 2;
            const cy = path.y + path.h / 2;
            tempCtx.translate(cx, cy);
            tempCtx.rotate(path.rotate || 0);

            // 绘制相对于中心点的路径
            const firstPoint = path.path[0];
            tempCtx.moveTo(firstPoint.x - cx, firstPoint.y - cy);
            for (let i = 1; i < path.path.length; i++) {
                tempCtx.lineTo(path.path[i].x - cx, path.path[i].y - cy);
            }
            tempCtx.stroke();
            tempCtx.restore();
            tempCtx.draw()

            var that = this
            uni.canvasToTempFilePath({
                canvasId: 'drawLines',
                x: path.x - 10,
                y: path.y - 10,
                width: path.w + 20,
                height: path.h + 20,
                success: (res) => {
                    // console.log(res, '✅ ttt!');
                    // 添加新元素
                    uni.$emit('drawLines', {
                        ...path,
                        url: res.tempFilePath,
                        w: path.w + 15,
                        h: path.h + 15,
                        x: path.x - 10,
                        y: path.y - 10,
                        type: '',
                        path: null,
                        name: '图片',
                        size: 0,
                        rotate: 0,
                        display: false,
                        color: '',
                        closes: true,
                    })

                    // console.log('✅ 新线条已成功添加到images数组!');
                    that.clearRects()
                },
                fail: (err) => {
                    console.error(err, '❌ 保存失败');
                }
            }, this);
        },

        clearRects() {
            this.ctxs.clearRect(0, 0, this.width, this.height);
            this.ctxs.draw();


            this.isDrawing = false;
            this.currentStroke = null;
            this.penPath = [];
            this.penLastPoint = null;
        },
    }
}
</script>

<style scoped>
.drawLines {
    position: fixed;
    z-index: 10;
}

.displayFalse {
    position: fixed;
    top: -99999px;
    left: -99999px;
}
</style>