{"version": 3, "file": "logger.js", "sourceRoot": "", "sources": ["../../../src/core/logger.ts"], "names": [], "mappings": ";;;;;;;;AAKA;IAOI,gBAAY,EAA4B;YAA3B,EAAE,QAAA,EAAE,OAAO,aAAA;QACpB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC5B,CAAC;IAED,8DAA8D;IAC9D,sBAAK,GAAL;QAAM,cAAkB;aAAlB,UAAkB,EAAlB,qBAAkB,EAAlB,IAAkB;YAAlB,yBAAkB;;QACpB,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,sCAAsC;YACtC,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,OAAO,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,UAAU,EAAE;gBACxF,sCAAsC;gBACtC,OAAO,CAAC,KAAK,OAAb,OAAO,iBAAO,IAAI,CAAC,EAAE,EAAK,IAAI,CAAC,OAAO,EAAE,OAAI,GAAK,IAAI,GAAE;aAC1D;iBAAM;gBACH,IAAI,CAAC,IAAI,OAAT,IAAI,EAAS,IAAI,EAAE;aACtB;SACJ;IACL,CAAC;IAED,wBAAO,GAAP;QACI,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;IACnC,CAAC;IAED,8DAA8D;IAC9D,qBAAI,GAAJ;QAAK,cAAkB;aAAlB,UAAkB,EAAlB,qBAAkB,EAAlB,IAAkB;YAAlB,yBAAkB;;QACnB,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,sCAAsC;YACtC,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,OAAO,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,UAAU,EAAE;gBACvF,sCAAsC;gBACtC,OAAO,CAAC,IAAI,OAAZ,OAAO,iBAAM,IAAI,CAAC,EAAE,EAAK,IAAI,CAAC,OAAO,EAAE,OAAI,GAAK,IAAI,GAAE;aACzD;SACJ;IACL,CAAC;IAED,8DAA8D;IAC9D,qBAAI,GAAJ;QAAK,cAAkB;aAAlB,UAAkB,EAAlB,qBAAkB,EAAlB,IAAkB;YAAlB,yBAAkB;;QACnB,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,sCAAsC;YACtC,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,OAAO,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,UAAU,EAAE;gBACvF,sCAAsC;gBACtC,OAAO,CAAC,IAAI,OAAZ,OAAO,iBAAM,IAAI,CAAC,EAAE,EAAK,IAAI,CAAC,OAAO,EAAE,OAAI,GAAK,IAAI,GAAE;aACzD;iBAAM;gBACH,IAAI,CAAC,IAAI,OAAT,IAAI,EAAS,IAAI,EAAE;aACtB;SACJ;IACL,CAAC;IAED,8DAA8D;IAC9D,sBAAK,GAAL;QAAM,cAAkB;aAAlB,UAAkB,EAAlB,qBAAkB,EAAlB,IAAkB;YAAlB,yBAAkB;;QACpB,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,sCAAsC;YACtC,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,OAAO,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,UAAU,EAAE;gBACxF,sCAAsC;gBACtC,OAAO,CAAC,KAAK,OAAb,OAAO,iBAAO,IAAI,CAAC,EAAE,EAAK,IAAI,CAAC,OAAO,EAAE,OAAI,GAAK,IAAI,GAAE;aAC1D;iBAAM;gBACH,IAAI,CAAC,IAAI,OAAT,IAAI,EAAS,IAAI,EAAE;aACtB;SACJ;IACL,CAAC;IAhEM,gBAAS,GAA4B,EAAE,CAAC;IAiEnD,aAAC;CAAA,AAlED,IAkEC;AAlEY,wBAAM"}