
.editor-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background-color: #1a1a1a;
  display: flex;
  flex-direction: column;
}
.top-bar {
  display: flex;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #1a1a1a;
}
.back-btn,
.save-btn {
  color: #b6ff00;
  font-size: 32rpx;
  padding: 10rpx 20rpx;
}
.save-btn {
  border: 1px solid #b6ff00;
  border-radius: 8rpx;
}
.image-area {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background-color: #1a1a1a;
}
.transparent-bg {
  width: 100%;
  height: 55%;
  background-image:
    linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
    linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
    linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
  background-size: 10px 10px;
  background-position: 0 0, 0 5px, 5px -5px, -5px 0px;
  background-color: #ffffff;
}
.bottom-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 20rpx;
  background-color: #1a1a1a;
}
.toolbar-controls {
  display: flex;
}
.control-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10rpx;
}
.icon {
  color: #ffffff;
  font-size: 40rpx;
}
.layer-btn {
  background-color: #333333;
  padding: 10rpx 30rpx;
  border-radius: 8rpx;
}
.layer-text {
  color: #ffffff;
  font-size: 28rpx;
}
.tools-panel {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  background-color: #000000;
}
.tool-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 16.66%;
}
.tool-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}
.material-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
}
.template-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14h-2v-4H8v-2h4V7h2v4h4v2h-4v4z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
}
.text-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M5 4v3h5.5v12h3V7H19V4z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
}
.mark-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
}
.brush-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M7 14c-1.66 0-3 1.34-3 3 0 1.31-1.16 2-2 2 .92 1.22 2.49 2 4 2 2.21 0 4-1.79 4-4 0-1.66-1.34-3-3-3zm13.71-9.37l-1.34-1.34c-.39-.39-1.02-.39-1.41 0L9 12.25 11.75 15l8.96-8.96c.39-.39.39-1.02 0-1.41z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
}
.add-image-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M19 7v2.99s-1.99.01-2 0V7h-3s.01-1.99 0-2h3V2h2v3h3v2h-3zm-3 4V8h-3V5H5c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2v-8h-3zM5 19l3-4 2 3 3-4 4 5H5z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
}
.tool-text {
  color: #ffffff;
  font-size: 24rpx;
}
.brush-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  pointer-events: auto;
}

/* Layer panel styles */
.layer-panel {
  position: absolute;
  right: 0;
  top: 80px;
  /* bottom: 0; */
  width: 300rpx;
  background-color: #222222;
  z-index: 100;
  display: flex;
  flex-direction: column;
  border-left: 1px solid #333333;
}
.apply-panel {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
}
.layer-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1px solid #333333;
}
.layer-tabs {
  display: flex;
  flex: 1;
}
.layer-tab {
  padding: 10rpx 20rpx;
  color: #999999;
  font-size: 24rpx;
}
.layer-tab.active {
  color: #ffffff;
  border-bottom: 2px solid #b6ff00;
}
.close-panel {
  color: #ffffff;
  font-size: 40rpx;
  padding: 0 10rpx;
}
.layer-list {
  flex: 1;
  overflow-y: auto;
}
.layer-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1px solid #333333;
}
.layer-preview {
  width: 60rpx;
  height: 60rpx;
  background-color: #333333;
  margin-right: 10rpx;
  border: 1px solid #444444;
}
.layer-preview image {
  width: 100%;
  height: 100%;
}
.layer-info {
  flex: 1;
}
.layer-name {
  color: #ffffff;
  font-size: 24rpx;
}
.layer-visibility {
  width: 40rpx;
  text-align: center;
}
.visibility-icon {
  font-size: 24rpx;
}
.layer-panel-footer {
  padding: 20rpx;
  border-top: 1px solid #333333;
}
.layer-action {
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-icon {
  color: #b6ff00;
  font-size: 28rpx;
  margin-right: 10rpx;
}
.action-text {
  color: #b6ff00;
  font-size: 24rpx;
}

/* Material panel styles */
.material-panel {
  position: absolute;
  /* top: 0; */
  left: 0;
  /* right: 0; */
  bottom: 120rpx;
  background-color: #000000;
  z-index: 200;
  display: flex;
  flex-direction: column;
}
.material-search {
  padding: 20rpx;
}
.search-categories {
  margin-bottom: 20rpx;
  max-width: 750rpx;
}
.category-scroll {
  white-space: nowrap;
  padding: 10rpx 0;
}
.category-item {
  display: inline-block;
  padding: 10rpx 30rpx;
  color: #ffffff;
  font-size: 28rpx;
  transition: all 0.3s;
}
.category-item.active {
  color: #b6ff00;
  border-bottom: 4rpx solid #b6ff00;
}
.subcategories {
  display: flex;
  flex-direction: column;
  background-color: #222222;
  border-radius: 10rpx;
}
.subcategory-item {
  padding: 20rpx;
  color: #ffffff;
  font-size: 26rpx;
  border-bottom: 1px solid #333333;
  transition: all 0.3s;
}
.subcategory-item.active {
  color: #b6ff00;
  background-color: #333333;
}
.material-grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10rpx;
  padding: 10rpx;
  overflow-y: auto;
  max-width: 630rpx;
}
.material-item {
  position: relative;
  background-color: #222222;
  border-radius: 10rpx;
  overflow: hidden;
  aspect-ratio: 1;
}
.material-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.material-name {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10rpx;
  background-color: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  font-size: 22rpx;
  text-align: center;
}
.premium-icon {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  color: #ffcc00;
  font-size: 30rpx;
}
.material-panel-close {
  position: absolute;
  bottom: -80rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 80rpx;
  background-color: #333333;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.close-icon {
  color: #ffffff;
  font-size: 50rpx;
}

/* Text panel styles */
.text-panel {
  position: absolute;
  /* top: 0; */
  left: 0;
  /* right: 0; */
  bottom: 120rpx;
  background-color: #333333;
  z-index: 200;
  display: flex;
  flex-direction: column;
  padding: 0;
}
.text-panel-header {
  display: flex;
  width: 77%;
  /* margin: auto; */
  /* justify-content: flex-end; */
  align-items: center;
  justify-content: space-between;
  height: 40px;
  /* padding: 20rpx; */
}
.close-panel {
  color: #ffffff;
  font-size: 40rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.text-input-area {
  background-color: #222222;
  padding: 20rpx;
  margin-bottom: 20rpx;
}
.text-input {
  width: 100%;
  min-height: 80rpx;
  color: #ffffff;
  font-size: 32rpx;
}
.text-font-options {
  padding: 0 20rpx;
  margin-bottom: 30rpx;
}
.font-scroll {
  white-space: nowrap;
}
.font-item {
  display: inline-block;
  padding: 15rpx 30rpx;
  margin-right: 10rpx;
  background-color: #444444;
  color: #ffffff;
  font-size: 28rpx;
  border-radius: 8rpx;
}
.font-item.active {
  background-color: #b6ff00;
  color: #000000;
}
.text-size-control {
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  margin-bottom: 30rpx;
}
.size-label {
  color: #ffffff;
  font-size: 28rpx;
  margin-right: 20rpx;
  width: 80rpx;
}
.size-slider {
  /* flex: 1; */
  width: 50%;
}
.size-value {
  color: #ffffff;
  font-size: 28rpx;
  margin-left: 20rpx;
  width: 60rpx;
  text-align: right;
}
.text-color-options {
  padding: 0 20rpx;
  margin-bottom: 20rpx;
}
.color-label {
  color: #ffffff;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  display: block;
}
.color-grid {
  display: flex;
  flex-wrap: nowrap;
}

/* Brush panel styles */
.brush-panel {
  position: absolute;
  /* top: 0; */
  left: 0;
  /* right: 0; */
  bottom: 120rpx;
  background-color: #333333;
  z-index: 200;
  display: flex;
  flex-direction: column;
  padding: 20rpx;
}
.brush-size-control {
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  margin-bottom: 30rpx;
}
.size-label {
  color: #ffffff;
  font-size: 28rpx;
  margin-right: 20rpx;
  width: 80rpx;
}

/* .size-slider {
  flex: 1;
} */
.size-value {
  color: #ffffff;
  font-size: 28rpx;
  margin-left: 20rpx;
  width: 60rpx;
  text-align: right;
}
.brush-color-options {
  padding: 0 20rpx;
  margin-bottom: 30rpx;
}
.color-label {
  color: #ffffff;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  display: block;
}
.color-scroll {
  width: 100%;
  white-space: nowrap;
}
.color-grid {
  display: inline-flex;
  padding: 10rpx 0;
}
.color-item {
  flex-shrink: 0;
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  /* border: 2rpx solid transparent; */
}
.color-item.active {
  border: 10rpx solid #ffffff;
  width: 30rpx;
  height: 30rpx;
}
.brush-style-options {
  padding: 0 20rpx;
  margin-bottom: 30rpx;
}
.style-label {
  color: #ffffff;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  display: block;
}
.style-grid {
  display: flex;
}
.style-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 40rpx;
}
.style-line {
  width: 100rpx;
  height: 6rpx;
  margin-bottom: 10rpx;
}
.solid-line {
  background-color: #b6ff00;
}
.dashed-line {
  background: repeating-linear-gradient(to right,
      #ffffff 0%,
      #ffffff 40%,
      transparent 40%,
      transparent 100%);
}
.style-text {
  color: #ffffff;
  font-size: 24rpx;
}
.style-item.active .style-text {
  color: #b6ff00;
}

/* Material layer styles */
.material-layer {
  position: absolute;
  box-sizing: border-box;
  transform-origin: center center;
  -webkit-user-select: none;
          user-select: none;
  touch-action: none;
  cursor: move;
  overflow: visible;
  transition: opacity 0.2s ease;
}
.material-layer.selected {
  z-index: 100 !important;
}
.layer-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  pointer-events: none;
  user-drag: none;
  -webkit-user-drag: none;
}

/* Text layer styles */
.text-layer {
  box-sizing: border-box;
  transform-origin: center center;
  -webkit-user-select: none;
          user-select: none;
  touch-action: none;
  cursor: move;
  overflow: visible;
  transition: opacity 0.2s ease;
  background-color: transparent;
}
.text-layer.selected {
  border: none;
  /* Border is handled by the layer-border element */
}
.text-content-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  padding: 4px;
  box-sizing: border-box;
}
.text-content {
  width: 100%;
  text-align: center;
  pointer-events: none;
  -webkit-user-select: none;
          user-select: none;
  word-break: break-word;
  white-space: pre-wrap;
  display: block;
}

/* Layer border and control points */
.layer-border {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px dashed #00ff00;
  pointer-events: none;
}
.control-point {
  position: absolute;
  width: 30px;
  height: 30px;
  background-color: #ffffff;
  border: 2px solid #00ff00;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto;
  z-index: 2;
  transform: translate(-50%, -50%);
}
.control-icon {
  font-size: 18px;
  color: #333;
  pointer-events: none;
}

/* Control point positions */
.top-left {
  top: 0;
  left: 0;
}
.top-center {
  top: 0;
  left: 50%;
}
.top-right {
  top: 0;
  right: 0;
  transform: translate(50%, -50%);
}
.middle-left {
  top: 50%;
  left: 0;
}
.middle-right {
  top: 50%;
  right: 0;
  transform: translate(50%, -50%);
}
.bottom-left {
  bottom: 0;
  left: 0;
  transform: translate(-50%, 50%);
}
.bottom-center {
  bottom: 0;
  left: 50%;
  transform: translate(-50%, 50%);
}
.bottom-right {
  bottom: 0;
  right: 0;
  transform: translate(50%, 50%);
}

/* Special control points */
.rotate-point {
  background-color: #ffcc00;
}
.resize-point {
  background-color: #00ccff;
}
.delete-point {
  background-color: #ff3333;
}
.duplicate-point {
  background-color: #33cc33;
}

/* Animation for layer selection */
@keyframes pulse-border {
0% {
    box-shadow: 0 0 0 0 rgba(126, 211, 33, 0.4);
}
70% {
    box-shadow: 0 0 0 5px rgba(126, 211, 33, 0);
}
100% {
    box-shadow: 0 0 0 0 rgba(126, 211, 33, 0);
}
}
.material-layer.selected .layer-border {
  animation: pulse-border 2s infinite;
}

/* Hover effects for control points */
.control-point:hover {
  transform: translate(-50%, -50%) scale(1.1);
}

/* Transparent background for the image area */
.image-area {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: transparent;
  overflow: hidden;
}
.transparent-bg {
  background-image:
    linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
    linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
    linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  background-color: #ffffff;
}

/* Ensure the layer is above the background */
.material-layer {
  z-index: 10;
}
.material-layer.selected {
  z-index: 100;
}
