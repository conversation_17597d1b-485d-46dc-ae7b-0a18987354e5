<view class="editor-container"><view class="top-bar"><text class="back-btn" bindtap="{{a}}">回首页</text><text class="save-btn" bindtap="{{b}}">保存</text></view><view class="image-area" bindtouchstart="{{h}}" bindtouchmove="{{i}}" bindtouchend="{{j}}"><image class="transparent-bg" src="{{c}}" mode="aspectFit" style="{{'filter:' + d + ';' + ('transform:' + e) + ';' + ('transition:' + 'transform 0.3s ease')}}"></image><view wx:for="{{f}}" wx:for-item="layer" wx:key="g" class="{{['material-layer', layer.h && 'selected']}}" style="{{'position:' + 'absolute' + ';' + ('left:' + layer.i) + ';' + ('top:' + layer.j) + ';' + ('width:' + layer.k) + ';' + ('height:' + layer.l) + ';' + ('transform:' + layer.m) + ';' + ('transform-origin:' + 'center center') + ';' + ('z-index:' + layer.n) + ';' + ('opacity:' + layer.o)}}" catchtouchstart="{{layer.p}}" catchtouchmove="{{layer.q}}" catchtouchend="{{layer.r}}"><image src="{{layer.a}}" mode="aspectFill" class="layer-image"></image><block wx:if="{{layer.b}}"><view class="layer-border"><view class="control-point top-left"></view><view class="control-point top-center"></view><view class="control-point top-right delete-point" catchtouchstart="{{layer.c}}"><text class="control-icon">×</text></view><view class="control-point middle-left"></view><view class="control-point middle-right rotate-point" catchtouchstart="{{layer.d}}"><text class="control-icon">⟳</text></view><view class="control-point bottom-left duplicate-point" catchtouchstart="{{layer.e}}"><text class="control-icon">+</text></view><view class="control-point bottom-center"></view><view class="control-point bottom-right resize-point" catchtouchstart="{{layer.f}}"><text class="control-icon">⤡</text></view></view></block></view><view wx:for="{{g}}" wx:for-item="layer" wx:key="l" class="{{['text-layer', layer.m && 'selecteds']}}" style="{{'position:' + 'absolute' + ';' + ('left:' + layer.n) + ';' + ('top:' + layer.o) + ';' + ('width:' + layer.p) + ';' + ('height:' + layer.q) + ';' + ('transform:' + layer.r) + ';' + ('z-index:' + layer.s) + ';' + ('opacity:' + layer.t) + ';' + ('pointer-events:' + 'auto')}}" catchtouchstart="{{layer.v}}"><view class="text-content-wrapper"><text class="text-content" style="{{'color:' + layer.b + ';' + ('font-family:' + layer.c) + ';' + ('font-size:' + layer.d) + ';' + ('line-height:' + layer.e) + ';' + ('text-align:' + 'center')}}">{{layer.a}}</text></view><block wx:if="{{layer.f}}"><view class="layer-border"><view class="control-point top-left"></view><view class="control-point top-center"></view><view class="control-point top-right delete-point" catchtouchstart="{{layer.g}}"><text class="control-icon">×</text></view><view class="control-point middle-left"></view><view class="control-point middle-right rotate-point" catchtouchstart="{{layer.h}}"><text class="control-icon">⟳</text></view><view class="control-point bottom-left duplicate-point" catchtouchstart="{{layer.i}}"><text class="control-icon">+</text></view><view class="control-point bottom-center edit-point" catchtouchstart="{{layer.j}}"><text class="control-icon">✎</text></view><view class="control-point bottom-right resize-point" catchtouchstart="{{layer.k}}"><text class="control-icon">⤡</text></view></view></block></view></view><view wx:if="{{k}}" class="material-panel"><view class="material-search"><view class="search-categories"><scroll-view scroll-x="true" class="category-scroll"><view wx:for="{{l}}" wx:for-item="category" wx:key="b" class="{{['category-item', category.c && 'active']}}" bindtap="{{category.d}}">{{category.a}}</view></scroll-view></view></view><view style="display:flex"><view class="subcategories"><view wx:for="{{m}}" wx:for-item="subcategory" wx:key="b" class="{{['subcategory-item', subcategory.c && 'active']}}" bindtap="{{subcategory.d}}">{{subcategory.a}}</view></view><view class="material-grid"><view wx:for="{{n}}" wx:for-item="item" wx:key="d" class="material-item" bindtap="{{item.e}}"><image src="{{item.a}}" mode="aspectFill" class="material-image"></image><view class="material-name">{{item.b}}</view><view wx:if="{{item.c}}" class="premium-icon">⭐</view></view></view></view><view class="material-panel-close" bindtap="{{o}}"><text class="close-icon">×</text></view></view><view wx:if="{{p}}" class="layer-panel"><view class="layer-panel-header"><view class="layer-tabs"><view class="layer-tab active">图层管理</view></view><text class="close-panel" bindtap="{{q}}">×</text></view><view class="layer-list"><view class="layer-item"><view class="layer-preview"><image src="{{r}}" mode="aspectFit"></image></view><view class="layer-info"><text class="layer-name">背景图层</text></view><view class="layer-visibility"><text class="visibility-icon">👁️</text></view></view><view wx:for="{{s}}" wx:for-item="layer" wx:key="h" class="{{['layer-item', layer.i && 'active-layer', layer.j && 'hidden-layer']}}" bindtap="{{layer.k}}"><view class="layer-preview text-preview" style="{{'color:' + layer.a + ';' + ('font-family:' + layer.b)}}"><text>T</text></view><view class="layer-info"><text class="layer-name">{{layer.c}}</text></view><view class="layer-visibility" catchtap="{{layer.f}}"><text class="visibility-icon" data-hidden="{{layer.e}}">{{layer.d}}</text></view><view class="layer-delete" catchtap="{{layer.g}}"><text class="delete-icon">×</text></view></view><view wx:for="{{t}}" wx:for-item="layer" wx:key="f" class="{{['layer-item', layer.g && 'active-layer']}}" bindtap="{{layer.h}}"><view class="layer-preview"><image src="{{layer.a}}" mode="aspectFit"></image></view><view class="layer-info"><text class="layer-name">{{layer.b}}</text></view><view class="layer-visibility" catchtap="{{layer.d}}"><text class="visibility-icon">{{layer.c}}</text></view><view class="layer-delete" catchtap="{{layer.e}}"><text class="delete-icon">×</text></view></view></view><view class="layer-panel-footer"><view class="layer-action"><text class="action-icon">⊞</text><text class="action-text" bindtap="{{v}}">收起图层</text></view></view></view><view wx:if="{{w}}" class="text-panel"><view class="text-panel-header"><text class="apply-panel" bindtap="{{x}}">×</text><text class="apply-panel" bindtap="{{y}}">√</text></view><view class="text-input-area"><block wx:if="{{r0}}"><textarea class="text-input" placeholder="示例文字" auto-height value="{{z}}" bindinput="{{A}}"></textarea></block></view><view class="text-font-options"><scroll-view scroll-x="true" class="font-scroll"><view wx:for="{{B}}" wx:for-item="font" wx:key="b" class="{{['font-item', font.c && 'active']}}" style="{{'font-family:' + font.d}}" bindtap="{{font.e}}">{{font.a}}</view></scroll-view></view><view class="text-size-control"><text class="size-label">大小</text><slider class="size-slider" min="12" max="26" step="1" show-value value="{{C}}" bindchange="{{D}}" activeColor="#ffffff" backgroundColor="#666666" block-color="#ffffff" block-size="24"></slider></view><view class="text-color-options"><text class="color-label">颜色</text><view class="color-grid"><view wx:for="{{E}}" wx:for-item="color" wx:key="a" class="{{['color-item', color.b && 'active']}}" style="{{'background-color:' + color.c}}" bindtap="{{color.d}}"></view></view></view></view><view class="bottom-toolbar"><view class="toolbar-controls"><view class="control-btn prev" bindtap="{{F}}"><text class="icon">←</text></view><view class="control-btn next" bindtap="{{G}}"><text class="icon">→</text></view><view class="control-btn zoom-in" bindtap="{{H}}"><text class="icon">+</text></view><view class="control-btn zoom-out" bindtap="{{I}}"><text class="icon">-</text></view><view class="control-btn reset" bindtap="{{J}}"><text class="icon">⊡</text></view><view class="control-btn fullscreen" bindtap="{{K}}"><text class="icon">⤢</text></view></view><view class="layer-btn" bindtap="{{L}}"><text class="layer-text">图层</text></view></view><view class="tools-panel"><view class="tool-item" bindtap="{{M}}"><view class="tool-icon material-icon"></view><text class="tool-text">素材</text></view><view class="tool-item" bindtap="{{N}}"><view class="tool-icon template-icon"></view><text class="tool-text">模板</text></view><view class="tool-item" bindtap="{{O}}"><view class="tool-icon text-icon"></view><text class="tool-text">文字</text></view><view class="tool-item" bindtap="{{P}}"><view class="tool-icon mark-icon"></view><text class="tool-text">标记</text></view><view class="tool-item" bindtap="{{Q}}"><view class="tool-icon brush-icon"></view><text class="tool-text">画笔</text></view><view class="tool-item" bindtap="{{R}}"><view class="tool-icon add-image-icon"></view><text class="tool-text">加图</text></view></view><view wx:if="{{S}}" class="brush-panel"><view class="text-panel-header"><text class="apply-panel" bindtap="{{T}}">×</text></view><view class="brush-size-control"><text class="size-label">粗细</text><slider class="size-slider" min="1" max="30" step="1" show-value value="{{U}}" bindchange="{{V}}" activeColor="#ffffff" backgroundColor="#666666" block-color="#ffffff" block-size="24"></slider></view><view class="brush-color-options"><text class="color-label">颜色</text><scroll-view scroll-x="true" class="color-scroll"><view class="color-grid"><view wx:for="{{W}}" wx:for-item="color" wx:key="a" class="{{['color-item', color.b && 'active']}}" style="{{'background-color:' + color.c}}" bindtap="{{color.d}}"></view></view></scroll-view></view><view class="brush-style-options"><text class="style-label">笔状</text><view class="style-grid"><view class="{{['style-item', X && 'active']}}" bindtap="{{Y}}"><view class="style-line solid-line"></view><text class="style-text">实线</text></view><view class="{{['style-item', Z && 'active']}}" bindtap="{{aa}}"><view class="style-line dashed-line"></view><text class="style-text">虚线</text></view></view></view></view><canvas wx:if="{{ab}}" canvas-id="brushCanvas" class="brush-canvas" bindtouchstart="{{ac}}" bindtouchmove="{{ad}}" bindtouchend="{{ae}}"></canvas></view>