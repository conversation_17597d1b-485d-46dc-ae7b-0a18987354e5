"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      imageSrc: "",
      defaultImage: "",
      activeToolType: "",
      scale: 1,
      brushContext: null,
      blurValue: 2,
      isDrawing: false,
      lastX: 0,
      lastY: 0,
      showLayerPanel: false,
      showMaterialPanel: false,
      currentCategory: 0,
      currentSubcategory: 2,
      // Default to "艺术盆栽"
      layers: [
        {
          name: "背景图层",
          thumbnail: "",
          visible: true
        },
        {
          name: "文字图层",
          thumbnail: "",
          visible: true
        }
      ],
      categories: [
        {
          name: "室内植物",
          subcategories: [
            { name: "收藏", materials: [] },
            { name: "我的素材", materials: [] },
            {
              name: "艺术盆栽",
              materials: [
                { name: "宝莲灯盆栽", image: "https://b0.bdstatic.com/ugc/img/2024-12-28/42e36b01cb8ddae367b10ca4b0e4b919.png", premium: true },
                { name: "宝莲灯盆栽", image: "https://example.com/plant2.jpg", premium: true },
                { name: "宝莲灯盆栽", image: "https://example.com/plant3.jpg", premium: true },
                { name: "红梅盆栽", image: "https://example.com/plant4.jpg", premium: true },
                { name: "红梅盆栽", image: "https://example.com/plant5.jpg", premium: true },
                { name: "红梅盆栽", image: "https://example.com/plant6.jpg", premium: true }
              ]
            },
            { name: "花卉盆栽", materials: [] },
            { name: "造型植物", materials: [] },
            { name: "大型植物", materials: [] }
          ]
        },
        {
          name: "庭院植物",
          subcategories: [
            { name: "收藏", materials: [] },
            { name: "我的素材", materials: [] },
            { name: "庭院树木", materials: [] },
            { name: "观赏草本", materials: [] },
            { name: "地被植物", materials: [] }
          ]
        },
        {
          name: "组合造景",
          subcategories: [
            { name: "收藏", materials: [] },
            { name: "我的素材", materials: [] },
            { name: "现代组合", materials: [] },
            { name: "中式组合", materials: [] },
            { name: "欧式组合", materials: [] }
          ]
        },
        {
          name: "树木模块",
          subcategories: [
            { name: "收藏", materials: [] },
            { name: "我的素材", materials: [] },
            { name: "常绿乔木", materials: [] },
            { name: "落叶乔木", materials: [] },
            { name: "观花乔木", materials: [] }
          ]
        },
        {
          name: "容器&架构",
          subcategories: [
            { name: "收藏", materials: [] },
            { name: "我的素材", materials: [] },
            { name: "花盆花器", materials: [] },
            { name: "花架支架", materials: [] },
            { name: "装饰构件", materials: [] }
          ]
        }
      ],
      showTextPanel: false,
      currentText: "示例文字",
      currentFont: "sans-serif",
      textSize: 18,
      currentColor: "#ff0000",
      fontOptions: [
        { name: "思源黑体", value: "sans-serif" },
        { name: "三极字体", value: "serif" },
        { name: "汉鼎行书", value: "cursive" },
        { name: "思源宋体", value: "SimSun" },
        { name: "思源圆体", value: "Arial" }
      ],
      colorOptions: [
        "#ffffff",
        "#cccccc",
        "#999999",
        "#666666",
        "#333333",
        "#000000",
        "#ffff00",
        "#ffa500",
        "#ff8c00",
        "#ff4500",
        "#ff0000"
      ],
      showBrushPanel: false,
      brushSize: 10,
      brushColor: "#ff0000",
      brushStyle: "solid",
      materialLayers: [],
      // Array to store all material layers
      activeLayerIndex: -1,
      // Index of currently selected layer
      isDragging: false,
      // Flag for drag operation
      isResizing: false,
      // Flag for resize operation
      isRotating: false,
      // Flag for rotation operation
      startX: 0,
      // Starting X position for touch operations
      startY: 0,
      // Starting Y position for touch operations
      startWidth: 0,
      // Starting width for resize operation
      startHeight: 0,
      // Starting height for resize operation
      startRotation: 0,
      // Starting rotation angle for rotation operation
      lastTouchTime: 0,
      // For detecting double taps
      canvasWidth: 0,
      // Canvas width for boundary checking
      canvasHeight: 0,
      // Canvas height for boundary checking
      textLayers: [],
      // Array to store text layers
      activeTextLayerIndex: -1,
      // Index of currently selected text layer
      editingTextLayer: false
      // Flag to indicate we're editing an existing text layer
    };
  },
  onLoad() {
    this.initCanvas();
  },
  computed: {
    currentSubcategories() {
      if (this.categories.length === 0)
        return [];
      return this.categories[this.currentCategory].subcategories;
    },
    currentMaterials() {
      if (this.currentSubcategories.length === 0)
        return [];
      return this.currentSubcategories[this.currentSubcategory].materials;
    }
  },
  mounted() {
    setTimeout(() => {
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".image-area").boundingClientRect((data) => {
        if (data) {
          this.canvasWidth = data.width;
          this.canvasHeight = data.height;
        }
      }).exec();
    }, 300);
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    saveImage() {
      common_vendor.index.showLoading({ title: "保存中..." });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({ title: "保存成功", icon: "success" });
      }, 1e3);
    },
    prevStep() {
    },
    nextStep() {
    },
    zoomIn() {
      this.scale = Math.min(this.scale + 0.1, 3);
    },
    zoomOut() {
      this.scale = Math.max(this.scale - 0.1, 0.5);
    },
    resetView() {
      this.scale = 1;
    },
    toggleFullscreen() {
    },
    toggleLayerPanel() {
      this.showLayerPanel = !this.showLayerPanel;
    },
    toggleLayerVisibility(index) {
      this.layers[index].visible = !this.layers[index].visible;
    },
    selectTool(tool) {
      this.activeToolType = tool;
      if (tool == "material") {
        this.initCanvas();
      }
      if (tool == "text") {
        this.textLayers.map((layer) => {
          if (layer.isSelected) {
            this.currentText = layer.text;
            this.currentFont = layer.font;
            this.textSize = layer.size;
            this.currentColor = layer.color;
          }
        });
      }
    },
    initCanvas() {
      try {
        this.brushContext = common_vendor.index.createCanvasContext("brushCanvas", this);
        if (this.brushContext) {
          this.brushContext.setLineCap("round");
          this.brushContext.setLineJoin("round");
          this.updateBrushSettings();
        }
      } catch (error) {
        console.error("Error initializing canvas:", error);
      }
    },
    onBrushStart(e) {
      if (!this.brushContext)
        return;
      const touch = e.touches[0];
      this.isDrawing = true;
      this.lastX = touch.x;
      this.lastY = touch.y;
      this.brushContext.beginPath();
      this.brushContext.moveTo(this.lastX, this.lastY);
    },
    onBrushMove(e) {
      if (!this.isDrawing || !this.brushContext)
        return;
      const touch = e.touches[0];
      const currentX = touch.x;
      const currentY = touch.y;
      this.brushContext.beginPath();
      if (this.brushStyle === "dashed" && !this.brushContext.setLineDash) {
        this.drawDashedLine(this.lastX, this.lastY, currentX, currentY);
      } else {
        this.brushContext.moveTo(this.lastX, this.lastY);
        this.brushContext.lineTo(currentX, currentY);
      }
      this.brushContext.stroke();
      this.brushContext.draw(true);
      this.lastX = currentX;
      this.lastY = currentY;
    },
    onBrushEnd() {
      this.isDrawing = false;
    },
    closeMaterialPanel() {
      this.activeToolType = "";
    },
    selectMaterial(material) {
      if (!this.canvasWidth || !this.canvasHeight) {
        const query = common_vendor.index.createSelectorQuery().in(this);
        query.select(".image-area").boundingClientRect((data) => {
          if (data) {
            this.canvasWidth = data.width;
            this.canvasHeight = data.height;
          }
        }).exec();
      }
      const defaultWidth = 200;
      const defaultHeight = 200;
      const newLayer = {
        image: material.image,
        name: material.name,
        x: this.canvasWidth ? this.canvasWidth / 2 - defaultWidth / 2 : 100,
        y: this.canvasHeight ? this.canvasHeight / 2 - defaultHeight / 2 : 100,
        width: defaultWidth,
        height: defaultHeight,
        rotation: 0,
        isSelected: true,
        visible: true,
        premium: material.premium || false
      };
      this.materialLayers.forEach((layer) => {
        layer.isSelected = false;
      });
      this.materialLayers.push(newLayer);
      this.activeLayerIndex = this.materialLayers.length - 1;
      common_vendor.index.showToast({
        title: "已添加素材",
        icon: "success"
      });
      this.closeMaterialPanel();
    },
    selectCategory(index) {
      this.currentCategory = index;
      this.currentSubcategory = 0;
    },
    selectSubcategory(index) {
      this.currentSubcategory = index;
    },
    closeTextPanel() {
      this.showTextPanel = false;
    },
    applyTextChanges() {
      if (this.editingTextLayer && this.activeTextLayerIndex >= 0) {
        const layer = this.textLayers[this.activeTextLayerIndex];
        layer.text = this.currentText;
        layer.font = this.currentFont;
        layer.size = this.textSize;
        layer.color = this.currentColor;
        this.adjustTextLayerSize(layer);
        this.editingTextLayer = false;
      } else {
        const textLength = this.currentText.length;
        const estimatedWidth = Math.max(200, Math.min(textLength * this.textSize * 0.8, this.canvasWidth * 0.8));
        const estimatedHeight = Math.max(80, Math.min(this.textSize * 3, this.canvasHeight * 0.3));
        const newTextLayer = {
          type: "text",
          text: this.currentText,
          font: this.currentFont,
          size: this.textSize,
          color: this.currentColor,
          x: this.canvasWidth ? this.canvasWidth / 2 - estimatedWidth / 2 : 100,
          y: this.canvasHeight ? this.canvasHeight / 2 - estimatedHeight / 2 : 100,
          width: estimatedWidth,
          height: estimatedHeight,
          rotation: 0,
          isSelected: true,
          visible: true
        };
        this.textLayers.forEach((layer) => {
          layer.isSelected = false;
        });
        this.materialLayers.forEach((layer) => {
          layer.isSelected = false;
        });
        this.textLayers.push(newTextLayer);
        this.activeTextLayerIndex = this.textLayers.length - 1;
      }
      this.activeToolType = "";
      common_vendor.index.showToast({
        title: this.editingTextLayer ? "已更新文字" : "已添加文字",
        icon: "success"
      });
    },
    selectFont(font) {
      this.currentFont = font;
    },
    changeTextSize(e) {
      this.textSize = e.detail.value;
      console.log(this.textLayers, "layer.isSelected");
      this.textLayers.map((layer) => {
        if (layer.isSelected) {
          layer.size = e.detail.value;
        }
      });
    },
    selectColor(color) {
      this.currentColor = color;
      this.textLayers.map((layer) => {
        if (layer.isSelected) {
          layer.color = color;
        }
      });
    },
    changeBrushSize(e) {
      this.brushSize = e.detail.value;
      this.updateBrushSettings();
    },
    selectBrushColor(color) {
      this.brushColor = color;
      this.updateBrushSettings();
    },
    selectBrushStyle(style) {
      this.brushStyle = style;
      this.updateBrushSettings();
    },
    updateBrushSettings() {
      if (!this.brushContext)
        return;
      this.brushContext.setStrokeStyle(this.brushColor);
      this.brushContext.setLineWidth(this.brushSize);
      try {
        if (this.brushStyle === "dashed") {
          this.brushContext.setLineDash([this.brushSize, this.brushSize * 2]);
        } else {
          this.brushContext.setLineDash([]);
        }
      } catch (error) {
        console.warn("setLineDash not supported, using solid lines only");
      }
    },
    drawDashedLine(x1, y1, x2, y2) {
      const dashLen = this.brushSize * 2;
      const gapLen = this.brushSize * 2;
      const deltaX = x2 - x1;
      const deltaY = y2 - y1;
      const numDashes = Math.floor(Math.sqrt(deltaX * deltaX + deltaY * deltaY) / (dashLen + gapLen));
      const dashX = deltaX / (numDashes * 2);
      const dashY = deltaY / (numDashes * 2);
      let q = 0;
      this.brushContext.moveTo(x1, y1);
      while (q++ < numDashes) {
        x1 += dashX;
        y1 += dashY;
        this.brushContext.lineTo(x1, y1);
        x1 += dashX;
        y1 += dashY;
        this.brushContext.moveTo(x1, y1);
      }
      this.brushContext.lineTo(x2, y2);
    },
    onLayerTouchStart(e, index) {
      console.log("Material layer touch start:", index);
      e.stopPropagation();
      this.materialLayers.forEach((layer2, i) => {
        layer2.isSelected = i === index;
      });
      this.textLayers.forEach((layer2) => {
        layer2.isSelected = false;
      });
      this.activeLayerIndex = index;
      this.activeTextLayerIndex = -1;
      this.isDragging = true;
      this.onLayerDirectDrag = true;
      const touch = e.touches[0];
      this.startX = touch.clientX;
      this.startY = touch.clientY;
      const layer = this.materialLayers[index];
      this.startLayerX = layer.x;
      this.startLayerY = layer.y;
      this.startWidth = layer.width;
      this.startHeight = layer.height;
      this.startRotation = layer.rotation;
      console.log("Touch start position:", this.startX, this.startY);
      console.log("Layer initial position:", this.startLayerX, this.startLayerY);
    },
    // 直接在图层上处理移动
    onLayerTouchMove(e, index) {
      if (!this.isDragging || this.activeLayerIndex !== index)
        return;
      if (this.isResizing || this.isRotating)
        return;
      e.stopPropagation();
      const touch = e.touches[0];
      const deltaX = touch.clientX - this.startX;
      const deltaY = touch.clientY - this.startY;
      const layer = this.materialLayers[index];
      let newX = this.startLayerX + deltaX;
      let newY = this.startLayerY + deltaY;
      newX = Math.max(0, Math.min(newX, this.canvasWidth - layer.width));
      newY = Math.max(0, Math.min(newY, this.canvasHeight - layer.height));
      layer.x = newX;
      layer.y = newY;
      console.log("Moving layer to:", newX, newY);
    },
    // 结束拖动
    onLayerTouchEnd(e) {
      console.log("Layer touch end");
      e.stopPropagation();
      this.isDragging = false;
      this.isResizing = false;
      this.isRotating = false;
      this.onLayerDirectDrag = false;
    },
    // 旋转处理
    onRotationStart(e, index) {
      console.log("Rotation start for layer:", index);
      e.stopPropagation();
      this.isRotating = true;
      this.isDragging = false;
      this.isResizing = false;
      this.activeLayerIndex = index;
      const touch = e.touches[0];
      const layer = this.materialLayers[index];
      const centerX = layer.x + layer.width / 2;
      const centerY = layer.y + layer.height / 2;
      const angleRadians = Math.atan2(touch.clientY - centerY, touch.clientX - centerX);
      this.startRotation = layer.rotation;
      this.startAngle = angleRadians * (180 / Math.PI);
      console.log("Rotation start - center:", centerX, centerY);
      console.log("Start rotation:", this.startRotation, "Start angle:", this.startAngle);
    },
    // 调整大小处理
    onResizeStart(e, index) {
      console.log("Resize start for layer:", index);
      e.stopPropagation();
      this.isResizing = true;
      this.isDragging = false;
      this.isRotating = false;
      this.activeLayerIndex = index;
      const touch = e.touches[0];
      this.startX = touch.clientX;
      this.startY = touch.clientY;
      const layer = this.materialLayers[index];
      this.startWidth = layer.width;
      this.startHeight = layer.height;
      console.log("Resize start - initial size:", this.startWidth, this.startHeight);
    },
    // Canvas触摸移动处理所有操作
    onCanvasTouchMove(e) {
      if (this.isRotating && this.activeLayerIndex !== -1) {
        const touch = e.touches[0];
        const layer = this.materialLayers[this.activeLayerIndex];
        const centerX = layer.x + layer.width / 2;
        const centerY = layer.y + layer.height / 2;
        const angleRadians = Math.atan2(touch.clientY - centerY, touch.clientX - centerX);
        const currentAngle = angleRadians * (180 / Math.PI);
        const angleDiff = currentAngle - this.startAngle;
        layer.rotation = (this.startRotation + angleDiff) % 360;
        console.log("Rotating layer to:", layer.rotation);
      } else if (this.isResizing && this.activeLayerIndex !== -1) {
        const touch = e.touches[0];
        const deltaX = touch.clientX - this.startX;
        const deltaY = touch.clientY - this.startY;
        const layer = this.materialLayers[this.activeLayerIndex];
        this.startWidth / this.startHeight;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        const direction = deltaX > 0 || deltaY > 0 ? 1 : -1;
        const scaleFactor = 1 + direction * distance * 0.01;
        let newWidth = this.startWidth * scaleFactor;
        let newHeight = this.startHeight * scaleFactor;
        newWidth = Math.max(50, newWidth);
        newHeight = Math.max(50, newHeight);
        layer.width = newWidth;
        layer.height = newHeight;
        console.log("Resizing layer to:", newWidth, newHeight);
      } else if (this.isDragging && this.activeLayerIndex !== -1 && !this.onLayerDirectDrag) {
        const touch = e.touches[0];
        const deltaX = touch.clientX - this.startX;
        const deltaY = touch.clientY - this.startY;
        const layer = this.materialLayers[this.activeLayerIndex];
        let newX = this.startLayerX + deltaX;
        let newY = this.startLayerY + deltaY;
        newX = Math.max(0, Math.min(newX, this.canvasWidth - layer.width));
        newY = Math.max(0, Math.min(newY, this.canvasHeight - layer.height));
        layer.x = newX;
        layer.y = newY;
      } else if (this.activeTextLayerIndex !== -1)
        ;
    },
    onCanvasTouchEnd() {
      console.log("Canvas touch end - operations ended");
      this.isDragging = false;
      this.isResizing = false;
      this.isRotating = false;
      this.onLayerDirectDrag = false;
    },
    onResizeStart(e, index) {
      e.stopPropagation();
      this.isResizing = true;
      this.isDragging = false;
      this.activeLayerIndex = index;
      const touch = e.touches[0];
      this.startX = touch.clientX;
      this.startY = touch.clientY;
      const layer = this.materialLayers[index];
      this.startWidth = layer.width;
      this.startHeight = layer.height;
    },
    onRotationStart(e, index) {
      console.log(123123);
      e.stopPropagation();
      this.isRotating = true;
      this.isDragging = false;
      this.activeLayerIndex = index;
      const touch = e.touches[0];
      const layer = this.materialLayers[index];
      const centerX = layer.x + layer.width / 2;
      const centerY = layer.y + layer.height / 2;
      const angleRadians = Math.atan2(touch.clientY - centerY, touch.clientX - centerX);
      this.startRotation = angleRadians * (180 / Math.PI) - layer.rotation;
    },
    deleteMaterialLayer(index) {
      this.materialLayers.splice(index, 1);
      if (this.activeLayerIndex === index) {
        this.activeLayerIndex = -1;
      } else if (this.activeLayerIndex > index) {
        this.activeLayerIndex--;
      }
      common_vendor.index.showToast({
        title: "已删除图层",
        icon: "success"
      });
    },
    duplicateLayer(index) {
      const originalLayer = this.materialLayers[index];
      const duplicatedLayer = JSON.parse(JSON.stringify(originalLayer));
      duplicatedLayer.x += 20;
      duplicatedLayer.y += 20;
      this.materialLayers.forEach((layer) => {
        layer.isSelected = false;
      });
      duplicatedLayer.isSelected = true;
      this.materialLayers.push(duplicatedLayer);
      this.activeLayerIndex = this.materialLayers.length - 1;
      common_vendor.index.showToast({
        title: "已复制图层",
        icon: "success"
      });
    },
    // Toggle material layer visibility
    toggleMaterialLayerVisibility(index) {
      if (index >= 0 && index < this.materialLayers.length) {
        this.materialLayers[index].visible = !this.materialLayers[index].visible;
        common_vendor.index.showToast({
          title: this.materialLayers[index].visible ? "已显示图层" : "已隐藏图层",
          icon: "none",
          duration: 1500
        });
        if (this.materialLayers[index].isSelected && !this.materialLayers[index].visible)
          ;
      }
    },
    // Text layer touch handlers - updated to sync text data to panel
    onTextLayerTouchStart(e, index) {
      console.log(e, index, "???");
      e.stopPropagation();
      this.textLayers.forEach((layer2, i) => {
        layer2.isSelected = i === index;
      });
      this.materialLayers.forEach((layer2) => {
        layer2.isSelected = false;
      });
      this.activeTextLayerIndex = index;
      this.isDragging = true;
      const touch = e.touches[0];
      this.startX = touch.clientX;
      this.startY = touch.clientY;
      const layer = this.textLayers[index];
      this.startWidth = layer.width;
      this.startHeight = layer.height;
      this.startRotation = layer.rotation;
      this.syncTextDataToPanel(index);
      const now = (/* @__PURE__ */ new Date()).getTime();
      const timeSinceLastTouch = now - this.lastTouchTime;
      if (timeSinceLastTouch < 300) {
        this.editTextLayer(index);
      }
      this.lastTouchTime = now;
    },
    // Sync text data to editing panel
    syncTextDataToPanel(index) {
      const layer = this.textLayers[index];
      this.currentText = layer.text;
      this.currentFont = layer.font;
      this.textSize = layer.size;
      this.currentColor = layer.color;
      this.editingTextLayer = true;
      this.activeTextLayerIndex = index;
    },
    // Handle canvas touch move for text layers
    onCanvasTouchMove(e) {
      if (this.activeLayerIndex !== -1)
        ;
      if (this.activeTextLayerIndex !== -1) {
        if (this.isDragging && !this.isResizing && !this.isRotating) {
          const touch = e.touches[0];
          const deltaX = touch.clientX - this.startX;
          const deltaY = touch.clientY - this.startY;
          const layer = this.textLayers[this.activeTextLayerIndex];
          let newX = layer.x + deltaX;
          let newY = layer.y + deltaY;
          newX = Math.max(0, Math.min(newX, this.canvasWidth - layer.width));
          newY = Math.max(0, Math.min(newY, this.canvasHeight - layer.height));
          layer.x = newX;
          layer.y = newY;
          this.startX = touch.clientX;
          this.startY = touch.clientY;
        }
        if (this.isResizing) {
          const touch = e.touches[0];
          const deltaX = touch.clientX - this.startX;
          const deltaY = touch.clientY - this.startY;
          const layer = this.textLayers[this.activeTextLayerIndex];
          let newWidth = this.startWidth + deltaX;
          let newHeight = this.startHeight + deltaY;
          newWidth = Math.max(50, newWidth);
          newHeight = Math.max(30, newHeight);
          newWidth = Math.min(newWidth, this.canvasWidth - layer.x);
          newHeight = Math.min(newHeight, this.canvasHeight - layer.y);
          layer.width = newWidth;
          layer.height = newHeight;
        }
        if (this.isRotating) {
          const touch = e.touches[0];
          const layer = this.textLayers[this.activeTextLayerIndex];
          const centerX = layer.x + layer.width / 2;
          const centerY = layer.y + layer.height / 2;
          const angleRadians = Math.atan2(touch.clientY - centerY, touch.clientX - centerX);
          let angleDegrees = angleRadians * (180 / Math.PI);
          let newRotation = angleDegrees - this.startRotation;
          newRotation = (newRotation + 360) % 360;
          layer.rotation = newRotation;
        }
      }
    },
    // Text layer resize start
    onTextResizeStart(e, index) {
      e.stopPropagation();
      this.isResizing = true;
      this.isDragging = false;
      this.activeTextLayerIndex = index;
      const touch = e.touches[0];
      this.startX = touch.clientX;
      this.startY = touch.clientY;
      const layer = this.textLayers[index];
      this.startWidth = layer.width;
      this.startHeight = layer.height;
    },
    // Text layer rotation start
    onTextRotationStart(e, index) {
      e.stopPropagation();
      this.isRotating = true;
      this.isDragging = false;
      this.activeTextLayerIndex = index;
      const touch = e.touches[0];
      const layer = this.textLayers[index];
      const centerX = layer.x + layer.width / 2;
      const centerY = layer.y + layer.height / 2;
      const angleRadians = Math.atan2(touch.clientY - centerY, touch.clientX - centerX);
      this.startRotation = angleRadians * (180 / Math.PI) - layer.rotation;
    },
    // Delete text layer
    deleteTextLayer(index) {
      this.textLayers.splice(index, 1);
      if (this.activeTextLayerIndex === index) {
        this.activeTextLayerIndex = -1;
      } else if (this.activeTextLayerIndex > index) {
        this.activeTextLayerIndex--;
      }
      common_vendor.index.showToast({
        title: "已删除文字图层",
        icon: "success"
      });
    },
    // Edit text layer - open text panel with current text properties
    editTextLayer(index) {
      this.syncTextDataToPanel(index);
      this.activeToolType = "text";
    },
    // Duplicate text layer
    duplicateTextLayer(index) {
      const originalLayer = this.textLayers[index];
      const duplicatedLayer = JSON.parse(JSON.stringify(originalLayer));
      duplicatedLayer.x += 20;
      duplicatedLayer.y += 20;
      this.textLayers.forEach((layer) => {
        layer.isSelected = false;
      });
      this.materialLayers.forEach((layer) => {
        layer.isSelected = false;
      });
      duplicatedLayer.isSelected = true;
      this.textLayers.push(duplicatedLayer);
      this.activeTextLayerIndex = this.textLayers.length - 1;
      common_vendor.index.showToast({
        title: "已复制文字图层",
        icon: "success"
      });
    },
    // Toggle text layer visibility
    toggleTextLayerVisibility(index) {
      if (index >= 0 && index < this.textLayers.length) {
        this.textLayers[index].visible = !this.textLayers[index].visible;
        common_vendor.index.showToast({
          title: this.textLayers[index].visible ? "已显示文字图层" : "已隐藏文字图层",
          icon: "none",
          duration: 1500
        });
      }
    },
    // Select layer from panel (updated to handle text layers and sync data)
    selectLayerFromPanel(index, type = "material") {
      if (type === "material") {
        this.materialLayers.forEach((layer, i) => {
          layer.isSelected = i === index;
        });
        this.textLayers.forEach((layer) => {
          layer.isSelected = false;
        });
        this.activeLayerIndex = index;
        this.activeTextLayerIndex = -1;
      } else if (type === "text") {
        this.textLayers.forEach((layer, i) => {
          layer.isSelected = i === index;
        });
        this.materialLayers.forEach((layer) => {
          layer.isSelected = false;
        });
        this.activeTextLayerIndex = index;
        this.activeLayerIndex = -1;
        this.syncTextDataToPanel(index);
      }
    },
    // Adjust text layer size based on content
    adjustTextLayerSize(layer) {
      const textLength = layer.text.length;
      const lineCount = (layer.text.match(/\n/g) || []).length + 1;
      if (textLength > 0) {
        const estimatedWidth = Math.max(200, Math.min(textLength * layer.size * 0.8, this.canvasWidth * 0.8));
        layer.width = estimatedWidth;
      }
      if (lineCount > 1) {
        const estimatedHeight = Math.max(80, Math.min(lineCount * layer.size * 1.5, this.canvasHeight * 0.3));
        layer.height = estimatedHeight;
      }
    },
    // Select tool with proper initialization
    selectTool(tool) {
      this.activeToolType = tool;
      if (tool === "text") {
        if (!this.editingTextLayer) {
          this.currentText = "示例文字";
          this.currentFont = "sans-serif";
          this.textSize = 40;
          this.currentColor = "#ff0000";
        }
        if (!this.canvasWidth || !this.canvasHeight) {
          this.initCanvas();
        }
      }
    },
    // Duplicate text layer with proper sizing
    duplicateTextLayer(index) {
      const originalLayer = this.textLayers[index];
      const duplicatedLayer = JSON.parse(JSON.stringify(originalLayer));
      duplicatedLayer.x += 20;
      duplicatedLayer.y += 20;
      this.textLayers.forEach((layer) => {
        layer.isSelected = false;
      });
      this.materialLayers.forEach((layer) => {
        layer.isSelected = false;
      });
      duplicatedLayer.isSelected = true;
      this.textLayers.push(duplicatedLayer);
      this.activeTextLayerIndex = this.textLayers.length - 1;
      common_vendor.index.showToast({
        title: "已复制文字图层",
        icon: "success"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.saveImage && $options.saveImage(...args)),
    c: $data.imageSrc || $data.defaultImage,
    d: $data.activeToolType === "blur" ? `blur(${$data.blurValue}px)` : "none",
    e: `scale(${$data.scale})`,
    f: common_vendor.f($data.materialLayers, (layer, index, i0) => {
      return common_vendor.e({
        a: layer.image,
        b: layer.isSelected
      }, layer.isSelected ? {
        c: common_vendor.o(($event) => $options.deleteMaterialLayer(index), index),
        d: common_vendor.o(($event) => $options.onRotationStart($event, index), index),
        e: common_vendor.o(($event) => $options.duplicateLayer(index), index),
        f: common_vendor.o(($event) => $options.onResizeStart($event, index), index)
      } : {}, {
        g: index,
        h: layer.isSelected ? 1 : "",
        i: layer.x + "px",
        j: layer.y + "px",
        k: layer.width + "px",
        l: layer.height + "px",
        m: `rotate(${layer.rotation}deg)`,
        n: layer.isSelected ? 100 : 10 + index,
        o: layer.visible ? 1 : 0.3,
        p: common_vendor.o(($event) => $options.onLayerTouchStart($event, index), index),
        q: common_vendor.o(($event) => $options.onLayerTouchMove($event, index), index),
        r: common_vendor.o((...args) => $options.onLayerTouchEnd && $options.onLayerTouchEnd(...args), index)
      });
    }),
    g: common_vendor.f($data.textLayers, (layer, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(layer.text),
        b: layer.color,
        c: layer.font,
        d: layer.size + "px",
        e: layer.size * 1.2 + "px",
        f: layer.isSelected
      }, layer.isSelected ? {
        g: common_vendor.o(($event) => $options.deleteTextLayer(index), index),
        h: common_vendor.o(($event) => $options.onTextRotationStart($event, index), index),
        i: common_vendor.o(($event) => $options.duplicateTextLayer(index), index),
        j: common_vendor.o(($event) => $options.editTextLayer(index), index),
        k: common_vendor.o(($event) => $options.onTextResizeStart($event, index), index)
      } : {}, {
        l: index,
        m: layer.isSelected ? 1 : "",
        n: layer.x + "px",
        o: layer.y + "px",
        p: layer.width + "px",
        q: layer.height + "px",
        r: `rotate(${layer.rotation}deg)`,
        s: layer.isSelected ? 100 : 20 + index,
        t: layer.visible ? 1 : 0.3,
        v: common_vendor.o(($event) => $options.onTextLayerTouchStart($event, index), index)
      });
    }),
    h: common_vendor.o((...args) => _ctx.onCanvasTouchStart && _ctx.onCanvasTouchStart(...args)),
    i: common_vendor.o((...args) => $options.onCanvasTouchMove && $options.onCanvasTouchMove(...args)),
    j: common_vendor.o((...args) => $options.onCanvasTouchEnd && $options.onCanvasTouchEnd(...args)),
    k: $data.activeToolType == "material" || $data.activeToolType == "template"
  }, $data.activeToolType == "material" || $data.activeToolType == "template" ? {
    l: common_vendor.f($data.categories, (category, idx, i0) => {
      return {
        a: common_vendor.t(category.name),
        b: idx,
        c: $data.currentCategory === idx ? 1 : "",
        d: common_vendor.o(($event) => $options.selectCategory(idx), idx)
      };
    }),
    m: common_vendor.f($options.currentSubcategories, (subcategory, idx, i0) => {
      return {
        a: common_vendor.t(subcategory.name),
        b: idx,
        c: $data.currentSubcategory === idx ? 1 : "",
        d: common_vendor.o(($event) => $options.selectSubcategory(idx), idx)
      };
    }),
    n: common_vendor.f($options.currentMaterials, (item, index, i0) => {
      return common_vendor.e({
        a: item.image,
        b: common_vendor.t(item.name),
        c: item.premium
      }, item.premium ? {} : {}, {
        d: index,
        e: common_vendor.o(($event) => $options.selectMaterial(item), index)
      });
    }),
    o: common_vendor.o((...args) => $options.closeMaterialPanel && $options.closeMaterialPanel(...args))
  } : {}, {
    p: $data.showLayerPanel
  }, $data.showLayerPanel ? {
    q: common_vendor.o((...args) => $options.toggleLayerPanel && $options.toggleLayerPanel(...args)),
    r: $data.imageSrc || $data.defaultImage,
    s: common_vendor.f($data.textLayers, (layer, index, i0) => {
      return {
        a: layer.color,
        b: layer.font,
        c: common_vendor.t(layer.text.substring(0, 8) || "文字图层" + (index + 1)),
        d: common_vendor.t(layer.visible ? "👁️" : "👁️‍🗨️"),
        e: !layer.visible,
        f: common_vendor.o(($event) => $options.toggleTextLayerVisibility(index), "text-" + index),
        g: common_vendor.o(($event) => $options.deleteTextLayer(index), "text-" + index),
        h: "text-" + index,
        i: layer.isSelected ? 1 : "",
        j: !layer.visible ? 1 : "",
        k: common_vendor.o(($event) => $options.selectLayerFromPanel(index, "text"), "text-" + index)
      };
    }),
    t: common_vendor.f($data.materialLayers, (layer, index, i0) => {
      return {
        a: layer.image,
        b: common_vendor.t(layer.name || "素材图层" + (index + 1)),
        c: common_vendor.t(layer.visible ? "👁️" : "👁️‍🗨️"),
        d: common_vendor.o(($event) => $options.toggleMaterialLayerVisibility(index), "material-" + index),
        e: common_vendor.o(($event) => $options.deleteMaterialLayer(index), "material-" + index),
        f: "material-" + index,
        g: layer.isSelected ? 1 : "",
        h: common_vendor.o(($event) => $options.selectLayerFromPanel(index), "material-" + index)
      };
    }),
    v: common_vendor.o(($event) => $data.showLayerPanel = false)
  } : {}, {
    w: $data.activeToolType == "text"
  }, $data.activeToolType == "text" ? {
    x: common_vendor.o((...args) => $options.closeMaterialPanel && $options.closeMaterialPanel(...args)),
    y: common_vendor.o((...args) => $options.applyTextChanges && $options.applyTextChanges(...args)),
    z: $data.currentText,
    A: common_vendor.o(($event) => $data.currentText = $event.detail.value),
    B: common_vendor.f($data.fontOptions, (font, idx, i0) => {
      return {
        a: common_vendor.t(font.name),
        b: idx,
        c: $data.currentFont === font.value ? 1 : "",
        d: font.value,
        e: common_vendor.o(($event) => $options.selectFont(font.value), idx)
      };
    }),
    C: $data.textSize,
    D: common_vendor.o((...args) => $options.changeTextSize && $options.changeTextSize(...args)),
    E: common_vendor.f($data.colorOptions, (color, idx, i0) => {
      return {
        a: idx,
        b: $data.currentColor === color ? 1 : "",
        c: color,
        d: common_vendor.o(($event) => $options.selectColor(color), idx)
      };
    })
  } : {}, {
    F: common_vendor.o((...args) => $options.prevStep && $options.prevStep(...args)),
    G: common_vendor.o((...args) => $options.nextStep && $options.nextStep(...args)),
    H: common_vendor.o((...args) => $options.zoomIn && $options.zoomIn(...args)),
    I: common_vendor.o((...args) => $options.zoomOut && $options.zoomOut(...args)),
    J: common_vendor.o((...args) => $options.resetView && $options.resetView(...args)),
    K: common_vendor.o((...args) => $options.toggleFullscreen && $options.toggleFullscreen(...args)),
    L: common_vendor.o((...args) => $options.toggleLayerPanel && $options.toggleLayerPanel(...args)),
    M: common_vendor.o(($event) => $options.selectTool("material")),
    N: common_vendor.o(($event) => $options.selectTool("template")),
    O: common_vendor.o(($event) => $options.selectTool("text")),
    P: common_vendor.o(($event) => $options.selectTool("mark")),
    Q: common_vendor.o(($event) => $options.selectTool("brush")),
    R: common_vendor.o(($event) => $options.selectTool("addImage")),
    S: $data.activeToolType == "brush"
  }, $data.activeToolType == "brush" ? {
    T: common_vendor.o((...args) => $options.closeMaterialPanel && $options.closeMaterialPanel(...args)),
    U: $data.brushSize,
    V: common_vendor.o((...args) => $options.changeBrushSize && $options.changeBrushSize(...args)),
    W: common_vendor.f($data.colorOptions, (color, idx, i0) => {
      return {
        a: idx,
        b: $data.brushColor === color ? 1 : "",
        c: color,
        d: common_vendor.o(($event) => $options.selectBrushColor(color), idx)
      };
    }),
    X: $data.brushStyle === "solid" ? 1 : "",
    Y: common_vendor.o(($event) => $options.selectBrushStyle("solid")),
    Z: $data.brushStyle === "dashed" ? 1 : "",
    aa: common_vendor.o(($event) => $options.selectBrushStyle("dashed"))
  } : {}, {
    ab: $data.activeToolType === "brush"
  }, $data.activeToolType === "brush" ? {
    ac: common_vendor.o((...args) => $options.onBrushStart && $options.onBrushStart(...args)),
    ad: common_vendor.o((...args) => $options.onBrushMove && $options.onBrushMove(...args)),
    ae: common_vendor.o((...args) => $options.onBrushEnd && $options.onBrushEnd(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
