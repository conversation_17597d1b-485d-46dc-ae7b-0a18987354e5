<view class="data-v-a4b6909a" style="width:100%;height:100vh;position:relative;overflow:hidden"><view class="container data-v-a4b6909a"><view class="header data-v-a4b6909a"><button class="header-btn data-v-a4b6909a" bindtap="{{a}}">返回</button><button class="header-btn save data-v-a4b6909a" bindtap="{{b}}">保存</button></view><view class="main-area data-v-a4b6909a"><view class="data-v-a4b6909a" style="{{'position:relative;display:flex;align-items:center;justify-content:center' + ';' + ('width:' + k + ';' + ('height:' + l))}}"><image src="{{c}}" alt="" class="main-img data-v-a4b6909a" mode="aspectFit"/><block wx:if="{{r0}}"><canvas canvas-id="canvasId" id="canvasId" style="{{'width:' + d + ';' + ('height:' + e)}}" bindtouchstart="{{f}}" bindtouchmove="{{g}}" bindtouchend="{{h}}" width="400" class="{{['main-img', 'draggable-img', 'data-v-a4b6909a', i]}}" height="400"></canvas></block><canvas canvas-id="canvasIdPen" id="canvasIdPen" class="main-img draggable-img data-v-a4b6909a" style="position:absolute;top:-999px;z-index:3"></canvas><view wx:for="{{j}}" wx:for-item="img" wx:key="t" class="data-v-a4b6909a"><view wx:if="{{img.a}}" key="{{img.m}}" class="img-border-ops data-v-a4b6909a" style="{{'left:' + img.n + ';' + ('top:' + img.o) + ';' + ('width:' + img.p) + ';' + ('height:' + img.q) + ';' + ('transform:' + img.r) + ';' + ('transform-origin:' + img.s) + ';' + ('z-index:' + 2) + ';' + ('padding:' + '10px')}}"><view class="border-rect data-v-a4b6909a"></view><view class="corner-btn corner-del data-v-a4b6909a" bindtouchstart="{{img.b}}"> × </view><view class="corner-btn corner-copy data-v-a4b6909a" bindtouchstart="{{img.c}}"> + </view><view class="corner-border data-v-a4b6909a"></view><view class="corner-btn corner-rotate data-v-a4b6909a" bindtouchstart="{{img.d}}" bindtouchmove="{{img.e}}" bindtouchend="{{img.f}}"> ↻ </view><view class="corner-border data-v-a4b6909a"></view><view class="corner-btn corner-scale data-v-a4b6909a" bindtouchstart="{{img.j}}" bindtouchmove="{{img.k}}" bindtouchend="{{img.l}}"><label wx:if="{{img.g}}" class="data-v-a4b6909a">⇄</label><label wx:elif="{{img.h}}" class="data-v-a4b6909a" style="color:#ff5722">↔</label><label wx:elif="{{img.i}}" class="data-v-a4b6909a" style="color:#2196f3">↕</label><label wx:else class="data-v-a4b6909a" style="color:#4caf50">⇄</label></view><view class="corner-border data-v-a4b6909a"></view></view></view></view></view><view class="toolbar data-v-a4b6909a"><view class="toolbar-item data-v-a4b6909a" bindtap="{{n}}"><image src="{{m}}" class="toolbar-icon data-v-a4b6909a"></image><text class="data-v-a4b6909a">素材</text></view><view class="toolbar-item data-v-a4b6909a" bindtap="{{p}}"><image src="{{o}}" class="toolbar-icon data-v-a4b6909a"></image><text class="data-v-a4b6909a">模板</text></view><view class="toolbar-item data-v-a4b6909a" bindtap="{{r}}"><image src="{{q}}" class="toolbar-icon data-v-a4b6909a"></image><text class="data-v-a4b6909a">文字</text></view><view class="toolbar-item data-v-a4b6909a" bindtap="{{t}}"><image src="{{s}}" class="toolbar-icon data-v-a4b6909a"></image><text class="data-v-a4b6909a">画笔</text></view><view class="toolbar-item data-v-a4b6909a" bindtap="{{w}}"><image src="{{v}}" class="toolbar-icon data-v-a4b6909a"></image><text class="data-v-a4b6909a">加图</text></view></view><view class="layer-box data-v-a4b6909a"><view class="data-v-a4b6909a" style="width:40%;display:flex;align-items:center"><view class="data-v-a4b6909a" style="width:50%;text-align:center" bindtap="{{x}}"> ← </view><view class="data-v-a4b6909a" style="width:50%;text-align:center" bindtap="{{y}}"> → </view><view class="data-v-a4b6909a" style="width:50%;text-align:center" bindtap="{{z}}"> 清空 </view></view><view class="data-v-a4b6909a" style="width:25%;margin-right:20px"><button class="layer-btn data-v-a4b6909a" bindtap="{{B}}">{{A}}</button></view></view><view wx:if="{{C}}" class="sidebarBox data-v-a4b6909a"><view class="end-btn data-v-a4b6909a"><label class="data-v-a4b6909a" bindtap="{{D}}">×</label><label class="data-v-a4b6909a" bindtap="{{E}}">√</label></view><view class="sidebar data-v-a4b6909a"><view class="sidebar-section data-v-a4b6909a"><view wx:for="{{F}}" wx:for-item="item" wx:key="b" class="{{['sidebar-title', 'data-v-a4b6909a', item.c]}}" bindtap="{{item.d}}">{{item.a}}</view></view><view class="material-content data-v-a4b6909a"><view class="sidebar-menu data-v-a4b6909a"><block wx:if="{{G}}"><view wx:for="{{H}}" wx:for-item="item" wx:key="c" class="{{['sidebar-menu-item', 'data-v-a4b6909a', item.b]}}" bindtap="{{item.d}}">{{item.a}}</view></block></view><view class="material-row data-v-a4b6909a"><view wx:for="{{I}}" wx:for-item="item" wx:key="f" class="{{['material-card', 'data-v-a4b6909a', item.e && 'material-circle']}}" bindtap="{{item.g}}"><image class="material-img data-v-a4b6909a" src="{{item.a}}"></image><view class="material-title data-v-a4b6909a">{{item.b}}</view><view wx:if="{{item.c}}" class="material-tag data-v-a4b6909a">￥</view><view wx:if="{{item.d}}" class="material-vip data-v-a4b6909a">VIP</view></view></view></view></view></view><view wx:if="{{J}}" class="text-toolbar data-v-a4b6909a"><view class="end-btn data-v-a4b6909a"><label class="data-v-a4b6909a" bindtap="{{K}}">×</label><label class="data-v-a4b6909a" bindtap="{{L}}">√</label></view><view class="text-toolbar-row data-v-a4b6909a"><input class="text-input data-v-a4b6909a" placeholder="示例文字" value="{{M}}" bindinput="{{N}}"/></view><view class="text-toolbar-row font-row data-v-a4b6909a"><button wx:for="{{O}}" wx:for-item="font" wx:key="b" class="{{['data-v-a4b6909a', 'font-btn', font.c]}}" bindtap="{{font.d}}" style="{{'font-family:' + font.e}}">{{font.a}}</button></view><view class="text-toolbar-row size-row data-v-a4b6909a"><text class="label data-v-a4b6909a">大小</text><slider class="size-slider data-v-a4b6909a" min="10" max="200" value="{{P}}" show-value style="width:60%"/><text class="size-value data-v-a4b6909a">{{Q}}</text></view><view class="text-toolbar-row color-row data-v-a4b6909a"><text class="label data-v-a4b6909a">颜色</text><view class="color-list data-v-a4b6909a"><view wx:for="{{R}}" wx:for-item="color" wx:key="b" class="{{['data-v-a4b6909a', 'color-circle', color.c]}}" style="{{'background:' + color.d + ';' + ('border:' + color.e)}}" bindtap="{{color.f}}"><view wx:if="{{color.a}}" class="color-selected data-v-a4b6909a"></view></view></view></view></view><view wx:if="{{S}}" class="pen-toolbar data-v-a4b6909a"><view class="end-btn data-v-a4b6909a"><label class="data-v-a4b6909a" bindtap="{{T}}">×</label><label class="data-v-a4b6909a" bindtap="{{U}}">√</label></view><view class="pen-row data-v-a4b6909a"><text class="label data-v-a4b6909a">粗细</text><slider class="data-v-a4b6909a" min="1" max="30" value="{{V}}" show-value style="width:60%"/></view><view class="pen-row data-v-a4b6909a"><text class="label data-v-a4b6909a">颜色</text><view class="pen-color-list data-v-a4b6909a"><view wx:for="{{W}}" wx:for-item="color" wx:key="b" class="{{['data-v-a4b6909a', 'pen-color-circle', color.c]}}" style="{{'background:' + color.d + ';' + ('border:' + color.e)}}" bindtap="{{color.f}}"><view wx:if="{{color.a}}" class="pen-color-selected data-v-a4b6909a"></view></view></view></view><view class="pen-row data-v-a4b6909a"><text class="label data-v-a4b6909a">笔状</text><view class="pen-style-list data-v-a4b6909a"><view class="{{['data-v-a4b6909a', 'pen-style-item', Y]}}" bindtap="{{Z}}"><image class="data-v-a4b6909a" src="{{X}}" style="width:24px;height:24px"/><text class="data-v-a4b6909a" style="color:#8dd800;margin-left:4px">实线</text></view><view class="{{['data-v-a4b6909a', 'pen-style-item', ab]}}" bindtap="{{ac}}"><image class="data-v-a4b6909a" src="{{aa}}" style="width:24px;height:24px"/><text class="data-v-a4b6909a" style="color:#bbb;margin-left:4px">虚线</text></view></view></view><view wx:if="{{ad}}" class="pen-status data-v-a4b6909a"><text class="pen-status-text data-v-a4b6909a">正在画线: </text><view class="pen-status-color data-v-a4b6909a" style="{{'background:' + ae}}"></view><text class="pen-status-size data-v-a4b6909a">{{af}}px</text><text class="pen-status-style data-v-a4b6909a">{{ag}}</text></view></view><view wx:if="{{ah}}" class="template-bar data-v-a4b6909a"><view class="end-btn data-v-a4b6909a"><label class="data-v-a4b6909a" bindtap="{{ai}}">×</label></view><view class="template-tabs data-v-a4b6909a"><view class="{{['data-v-a4b6909a', 'template-tab', aj]}}">推荐</view><view class="template-tab data-v-a4b6909a">室内组景</view><view class="template-tab data-v-a4b6909a">组合盆</view><view class="template-tab data-v-a4b6909a">组景架构</view><view class="template-tab data-v-a4b6909a">花箱景观</view><view class="template-tab data-v-a4b6909a">积木造型</view></view><scroll-view scroll-x class="template-list data-v-a4b6909a"><view wx:for="{{ak}}" wx:for-item="item" wx:key="c" class="template-card data-v-a4b6909a" bindtap="{{item.d}}"><image class="template-img data-v-a4b6909a" src="{{item.a}}"></image><view class="template-info data-v-a4b6909a"><view class="template-title data-v-a4b6909a">{{item.b}}</view><view class="template-meta data-v-a4b6909a"><image src="https://b0.bdstatic.com/ugc/img/2024-12-28/42e36b01cb8ddae367b10ca4b0e4b919.png" class="template-vip-icon data-v-a4b6909a"/><text class="template-view data-v-a4b6909a">448</text></view></view></view></scroll-view></view><view wx:if="{{al}}" class="layer-panel data-v-a4b6909a"><view class="layer-tabs data-v-a4b6909a"><view class="{{['data-v-a4b6909a', 'layer-tab', am]}}">单选</view></view><scroll-view class="layer-list data-v-a4b6909a" scroll-y><view wx:for="{{an}}" wx:for-item="img" wx:key="k" class="layer-item data-v-a4b6909a"><view class="data-v-a4b6909a" style="display:flex;align-items:center"><image wx:if="{{img.a}}" src="{{img.b}}" class="layer-eye data-v-a4b6909a" bindtap="{{img.c}}" mode="aspectFit"/><image wx:else bindtap="{{img.d}}" src="{{img.e}}" class="layer-eye data-v-a4b6909a" mode="scaleToFill"/><view class="layer-thumb data-v-a4b6909a"><image wx:if="{{img.f}}" src="{{img.g}}" class="layer-img data-v-a4b6909a" mode="aspectFit"/><text wx:else class="layer-text data-v-a4b6909a" style="{{'color:' + '#ff5722'}}">{{img.h}}</text></view><view class="data-v-a4b6909a" bindtap="{{img.i}}" style="margin-left:10px;color:#fff"> ↑ </view></view><view class="data-v-a4b6909a" style="color:#fff;text-align:right" bindtap="{{img.j}}"> × </view></view></scroll-view><view class="layer-footer data-v-a4b6909a"><image src="{{ao}}" class="layer-footer-icon data-v-a4b6909a"/><text class="layer-footer-text data-v-a4b6909a" bindtap="{{ap}}">收起图层</text></view></view></view></view>