{"name": "base64-arraybuffer", "description": "Encode/decode base64 data into ArrayBuffers", "main": "dist/base64-arraybuffer.umd.js", "module": "dist/base64-arraybuffer.es5.js", "typings": "dist/types/index.d.ts", "version": "1.0.2", "homepage": "https://github.com/niklasvh/base64-arraybuffer", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://hertzen.com"}, "repository": {"type": "git", "url": "https://github.com/niklasvh/base64-arraybuffer"}, "bugs": {"url": "https://github.com/niklasvh/base64-arraybuffer/issues"}, "license": "MIT", "engines": {"node": ">= 0.6.0"}, "scripts": {"prebuild": "rimraf dist/", "build": "tsc --module commonjs && rollup -c rollup.config.ts", "format": "prettier --write \"{src,test}/**/*.ts\"", "lint": "tslint -c tslint.json --project tsconfig.json -t codeFrame src/**/*.ts test/**/*.ts", "mocha": "mocha --require ts-node/register test/*.ts", "test": "npm run lint && npm run mocha", "release": "standard-version"}, "devDependencies": {"@rollup/plugin-commonjs": "^19.0.0", "@rollup/plugin-node-resolve": "^13.0.0", "@rollup/plugin-typescript": "^8.2.1", "@types/mocha": "^8.2.2", "@types/node": "^16.0.0", "mocha": "9.0.2", "prettier": "^2.3.2", "rimraf": "3.0.2", "rollup": "^2.52.7", "rollup-plugin-json": "^4.0.0", "rollup-plugin-sourcemaps": "^0.6.3", "standard-version": "^9.3.0", "ts-node": "^10.0.0", "tslib": "^2.3.0", "tslint": "^6.1.3", "tslint-config-prettier": "^1.18.0", "typescript": "^4.3.5"}, "keywords": []}