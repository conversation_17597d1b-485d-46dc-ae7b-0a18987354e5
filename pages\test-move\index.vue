<template>
    <view class="page">
        <view class="header">
            <text class="title">移动测试页面</text>
        </view>
        
        <view class="test-area">
            <hezi></hezi>
        </view>
        
        <view class="info">
            <text class="info-text">拖动绿色方块测试移动功能</text>
        </view>
    </view>
</template>

<script>
import hezi from '../watermarkRemover/componets/hezi.vue'

export default {
    components: {
        hezi
    },
    data() {
        return {
            
        }
    },
    methods: {
        
    }
}
</script>

<style scoped>
.page {
    width: 100%;
    height: 100vh;
    background-color: #f8f8f8;
    display: flex;
    flex-direction: column;
}

.header {
    width: 100%;
    height: 100rpx;
    background-color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
}

.test-area {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40rpx;
}

.info {
    width: 100%;
    height: 120rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fff;
}

.info-text {
    font-size: 28rpx;
    color: #666;
}
</style>
