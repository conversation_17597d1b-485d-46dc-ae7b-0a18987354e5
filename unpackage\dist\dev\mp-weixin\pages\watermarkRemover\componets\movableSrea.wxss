
.container.data-v-e6d83125 {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    /* border: 1px solid red; */
}
.main-background-img.data-v-e6d83125 {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
}
.outer-box.data-v-e6d83125 {
    border: 2rpx solid #333;
    position: relative;
    z-index: 3;
}
.outer-box2.data-v-e6d83125 {

    background-color: transparent;
    background-color: #e8e8e8;
    background-image:
        linear-gradient(45deg, #d0d0d0 25%, transparent 25%),
        linear-gradient(-45deg, #d0d0d0 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #d0d0d0 75%),
        linear-gradient(-45deg, transparent 75%, #d0d0d0 75%);
    background-size: 16px 16px;
    background-position: 0 0, 0 8px, 8px -8px, -8px 0px;
}
.movable-content.data-v-e6d83125 {
    /* background-color: #4cd964; */
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 16rpx;
    position: absolute;
    /* font-size: 28rpx; */
    /* font-weight: bold; */
    transition: transform 0.1s all;
}
.custom-border.data-v-e6d83125 {
    position: relative;

    /* border-radius: 8px; */
    box-sizing: border-box;
    pointer-events: none;
    z-index: 10;
    padding: 7px;
}
.corner-btn.data-v-e6d83125 {
    width: 20px;
    height: 20px;
    position: absolute;
    z-index: 11;
    pointer-events: auto;
    border-radius: 50%;
}
.corner-route.data-v-e6d83125 {
    position: absolute;
    width: 10px;
    height: 10px;
    background: #fff;
    border: 2px solid #333;
    background-color: #fff;
    border-radius: 50%;
}
.corner-top.data-v-e6d83125 {
    top: -8px;
    left: calc(50% - 4px);
    transform: translateX(-50%);
}
.corner-right.data-v-e6d83125 {
    right: -15px;
    top: calc(50% - 7px);
    transform: translateX(-50%);
}
.corner-btm.data-v-e6d83125 {
    left: calc(50% - 7px);
    bottom: -8px;
    transform: translateX(-50%);
}
.corner-left.data-v-e6d83125 {
    left: -2px;
    top: calc(50% - 7px);
    transform: translateX(-50%);
}
.corner-bottom.data-v-e6d83125 {
    left: 50%;
}
.corner-del.data-v-e6d83125 {
    left: -10px;
    top: -10px;
}
.corner-copy.data-v-e6d83125 {
    left: -10px;
    bottom: -10px;
}
.corner-rotate.data-v-e6d83125 {
    right: -10px;
    top: -10px;
}
.corner-scale.data-v-e6d83125 {
    right: -10px;
    bottom: -10px;
}



/*  */

/*  */
.movableViews.data-v-e6d83125 {
    padding: 8px;
}
.border-line.data-v-e6d83125 {
    position: absolute;
    z-index: 122;
    width: 40rpx;
    height: 40rpx;
    /* border: 1px solid red; */
    pointer-events: auto;
}
.border-top.data-v-e6d83125 {
    left: -4px;
    top: -4px;
    border-left: 6px solid rgb(0, 165, 22);
    border-top: 6px solid rgb(0, 165, 22);
}
.border-bottom.data-v-e6d83125 {
    bottom: -4px;
    right: -4px;
    border-right: 6px solid rgb(0, 165, 22);
    border-bottom: 6px solid rgb(0, 165, 22);
}
.border-left.data-v-e6d83125 {
    left: -4px;
    bottom: -4px;
    border-left: 6px solid rgb(0, 165, 22);
    border-bottom: 6px solid rgb(0, 165, 22);
}
.border-right.data-v-e6d83125 {
    right: -4px;
    top: -4px;
    border-right: 6px solid rgb(0, 165, 22);
    border-top: 6px solid rgb(0, 165, 22);
}
.border-dot.data-v-e6d83125 {
    position: absolute;
    width: 10px;
    height: 10px;
    background: #fff;
    border: 2px solid #333;
    border-radius: 50%;
    z-index: 13;
    pointer-events: auto;
}

/* 四个角的拖动点样式 */
.corner-dot-top-left.data-v-e6d83125,
.corner-dot-top-right.data-v-e6d83125,
.corner-dot-bottom-left.data-v-e6d83125,
.corner-dot-bottom-right.data-v-e6d83125 {
    position: absolute;
    width: 14px;
    height: 14px;
    background: #007aff;
    border: 2px solid #fff;
    border-radius: 50%;
    z-index: 14;
    pointer-events: auto;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
.corner-dot-top-left.data-v-e6d83125:active,
.corner-dot-top-right.data-v-e6d83125:active,
.corner-dot-bottom-left.data-v-e6d83125:active,
.corner-dot-bottom-right.data-v-e6d83125:active {
    background: #0056cc;
    transform: scale(1.1);
}
