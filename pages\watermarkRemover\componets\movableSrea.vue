<template>
    <view class="container">
        <image v-if="dataBack" :src="dataBack.url" :style="{
            width: dimensions.outerWidth + 'px',
            height: dimensions.outerHeight + 'px'
        }" mode="aspectFit" class="main-background-img" />
        <!-- 外层容器 -->
        <view class="outer-box" :class="!dataBack.url ? 'outer-box2' : ''" id="outerBox" :style="{
            width: dimensions.outerWidth + 'px',
            height: dimensions.outerHeight + 'px',
        }">
            <!-- 可移动的内容 -->
            <view v-for="(it, index) in dataType" :key="index" class="movable-content" id="movableContent"
                @touchstart="(e) => handleTouchStart(e, it)" @touchmove="(e) => handleTouchMove(e, it)"
                @touchend="handleTouchEnd" :style="{
                    transform: `translate(${it.x}px, ${it.y}px) scale(${it.display ? 1.05 : 1}) rotate(${it.rotate}rad)`,
                    width: it.w + 'px',
                    height: it.h + 'px',
                }">

                <view class="custom-border" :style="{
                    width: (it.w + 17) + 'px', height: (it.h + 17) + 'px',
                    border: it.display ? '2px dashed rgb(0, 0, 0)' : 'none',
                    fontWeight: it.Weights ? '900' : 'none',
                }">
                    <image class="movableImage" :style="{
                        width: Math.abs(it.w) + 'px', height: Math.abs(it.h) + 'px', transform: (it.h < 0 ? 'scaleY(-1)' : '') + ' ' + (it.w < 0 ? 'scaleX(-1)' : '')
                    }" v-if="it.type != 'text'" :src="it.url" mode="scaleToFill" />

                    <view class="movableText" :style="{
                        width: it.w,
                        height: it.h,
                        fontSize: it.size + 'px',
                        color: it.color,
                        fontFamily: it.textTpey,
                    }" v-else>{{ it.text }}</view>

                    <view v-if="it.display && !editLine">
                        <view class="corner-route corner-top" @touchmove.stop="(e) => cornerTop(e, it)"></view>
                        <view class="corner-route corner-right" @touchmove.stop="(e) => cornerRight(e, it)"></view>
                        <view class="corner-route corner-btm" @touchmove.stop="(e) => cornerBtm(e, it)"></view>
                        <view class="corner-route corner-left" @touchmove.stop="(e) => cornerLeft(e, it)"></view>

                        <image class="corner-btn corner-del" mode="aspectFit" :src="del" @click.stop="deleteItem(it)" />
                        <image class="corner-btn corner-copy" mode="aspectFit" :src="jia" @click.stop="copyItem(it)" />
                        <image class="corner-btn corner-rotate" mode="aspectFit" :src="rotate"
                            @touchmove.stop="(e) => rotateItem(e, it)" @touchend.stop="(e) => rotateEnd(it)" />
                        <image class="corner-btn corner-scale" mode="aspectFit" :src="suof"
                            @touchmove.stop="(e) => scaleItem(e, it)" />
                    </view>

                    <!-- 四角边框和圆点 -->
                    <view v-if="it.display && editLine == 1">
                        <!-- 四条边 -->
                        <view class="border-line border-top" @touchmove.stop="(e) => handleTouchs(e, it, 'top')">
                        </view>
                        <view class="border-line border-bottom" @touchmove.stop="(e) => handleTouchs(e, it, 'bottom')">
                        </view>
                        <view class="border-line border-left" @touchmove.stop="(e) => handleTouchs(e, it, 'left')">
                        </view>
                        <view class="border-line border-right" @touchmove.stop="(e) => handleTouchs(e, it, 'right')">
                        </view>

                        <!-- 四个角的拖动点 -->
                        <view class="border-dot corner-dot-top-left"
                            :style="{ left: '-7px', top: '-7px' }"
                            @touchstart.stop="(e) => handleCornerStart(e, it, 'top-left')"
                            @touchmove.stop="(e) => handleTouchs(e, it, 'top-left')"
                            @touchend.stop="(e) => handleCornerEnd(e, it)"></view>
                        <view class="border-dot corner-dot-top-right"
                            :style="{ right: '-7px', top: '-7px' }"
                            @touchstart.stop="(e) => handleCornerStart(e, it, 'top-right')"
                            @touchmove.stop="(e) => handleTouchs(e, it, 'top-right')"
                            @touchend.stop="(e) => handleCornerEnd(e, it)"></view>
                        <view class="border-dot corner-dot-bottom-left"
                            :style="{ left: '-7px', bottom: '-7px' }"
                            @touchstart.stop="(e) => handleCornerStart(e, it, 'bottom-left')"
                            @touchmove.stop="(e) => handleTouchs(e, it, 'bottom-left')"
                            @touchend.stop="(e) => handleCornerEnd(e, it)"></view>
                        <view class="border-dot corner-dot-bottom-right"
                            :style="{ right: '-7px', bottom: '-7px' }"
                            @touchstart.stop="(e) => handleCornerStart(e, it, 'bottom-right')"
                            @touchmove.stop="(e) => handleTouchs(e, it, 'bottom-right')"
                            @touchend.stop="(e) => handleCornerEnd(e, it)"></view>

                        <!-- 四个边中点的圆点（保留原有功能） -->
                        <view class="border-dot dot-top"
                            :style="{ left: '50%', top: '0', transform: 'translate(-50%, -50%)' }"></view>
                        <view class="border-dot dot-bottom"
                            :style="{ left: '50%', bottom: '0', transform: 'translate(-50%, 50%)' }"></view>
                        <view class="border-dot dot-left"
                            :style="{ left: '0', top: '50%', transform: 'translate(-50%, -50%)' }"></view>
                        <view class="border-dot dot-right"
                            :style="{ right: '0', top: '50%', transform: 'translate(50%, -50%)' }"></view>
                    </view>

                    <!-- 四角边框和圆点 -->
                    <view v-if="it.display && editLine == 2">
                        <!-- 四个角点圆点 -->
                        <view class="border-dot dot-top-left" :style="{ left: '-7px', top: '-7px' }"
                            @touchstart.stop="(e) => handleDotStart(e, it, 'top-left')"
                            @touchmove.stop="(e) => handledot(e, it, 'top-left')"
                            @touchend.stop="(e) => handleDotEnd(e, it)"></view>
                        <view class="border-dot dot-top-right" :style="{ right: '-7px', top: '-7px' }"
                            @touchstart.stop="(e) => handleDotStart(e, it, 'top-right')"
                            @touchmove.stop="(e) => handledot(e, it, 'top-right')"
                            @touchend.stop="(e) => handleDotEnd(e, it)"></view>
                        <view class="border-dot dot-bottom-left" :style="{ left: '-7px', bottom: '-7px' }"
                            @touchstart.stop="(e) => handleDotStart(e, it, 'bottom-left')"
                            @touchmove.stop="(e) => handledot(e, it, 'bottom-left')"
                            @touchend.stop="(e) => handleDotEnd(e, it)"></view>
                        <view class="border-dot dot-bottom-right" :style="{ right: '-7px', bottom: '-7px' }"
                            @touchstart.stop="(e) => handleDotStart(e, it, 'bottom-right')"
                            @touchmove.stop="(e) => handledot(e, it, 'bottom-right')"
                            @touchend.stop="(e) => handleDotEnd(e, it)"></view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    props: {
        dataarray: {
            type: Array,
            default: []
        }
    },
    data() {
        return {
            // 记录元素位置
            position: {
                x: 50,
                y: 50
            },
            // 记录触摸起点
            startPoint: {
                x: 0,
                y: 0
            },
            // 元素初始位置
            initialPosition: {
                x: 0,
                y: 0
            },
            // 容器和元素尺寸（使用固定值，适合小程序）
            dimensions: {
                outerWidth: 375, // 600rpx / 2
                outerHeight: 300, // 600rpx / 2
                // 移动块尺寸
                contentWidth: 100, // 200rpx / 2
                contentHeight: 50 // 100rpx / 2
            },
            // 是否正在拖动
            isDragging: false,
            isRotate: 10,
            dataType: [],
            record: [],
            dataBack: {},

            editLine: 0,

            del: 'https://bpic.588ku.com/element_origin_min_pic/19/04/09/9ce46a196fe52024a687e6aed9ab40a5.jpg',
            jia: 'https://pic.616pic.com/ys_img/00/04/79/ISEtXRcNdo.jpg',
            rotate: 'https://ts3.tc.mm.bing.net/th/id/OIP-C.CYvN0JBCeEE4pu5UQpx_FQHaHa?r=0&rs=1&pid=ImgDetMain&o=7&rm=3',
            suof: 'https://ts1.tc.mm.bing.net/th/id/R-C.********************************?rik=y3JH%2bd3VUY9kpA&riu=http%3a%2f%2fbpic.588ku.com%2felement_pic%2f00%2f94%2f84%2f0156f2d0c0bc6b8.jpg&ehk=U1IKwhy8Bi5%2bXN1ptz4T71vgiaNDTB7tCz54ZrMVGSs%3d&risl=&pid=ImgRaw&r=0',
        };
    },
    mounted() {
        // 在小程序环境中，我们使用固定尺寸，不需要动态获取
        console.log(this.dataarray, '移动组件已加载');
        this.dataType = this.dataarray

        uni.$on('drawLines', (e) => {
            console.log(e, 'fff');
            this.dataType.push({
                ids: this.dataType.length + 1,
                ...e,
            })
        })

        uni.$on('edit', (e) => {
            if (e) {
                this.editLine = e.id
                console.log(e.id, 'id');

            }
        })

        uni.$on('selectScaleItem', (e) => {
            console.log(e, 'aaa');
            this.dataType.push({
                ids: this.dataType.length + 1,
                ...e,
            })
        })
    },
    methods: {
        handleDotStart(e, it, corner) {
            if (!e.touches || e.touches.length === 0) return;

            const img = this.findFiles(it);
            if (!img) return;

            const touch = e.touches[0];
            const x = touch.clientX || touch.pageX;
            const y = touch.clientY || touch.pageY;

            // 初始化拖拽数据（只在开始时记录）
            img._cornerDrag = {
                startX: x,
                startY: y,
                startWidth: img.w,
                startHeight: img.h,
                startPosX: img.x,
                startPosY: img.y,
                corner: corner,
                // 记录四个角的初始位置（重要：基于当前实际位置）
                topLeft: { x: img.x, y: img.y },
                topRight: { x: img.x + img.w, y: img.y },
                bottomLeft: { x: img.x, y: img.y + img.h },
                bottomRight: { x: img.x + img.w, y: img.y + img.h }
            };

            // 防止事件冒泡
            e.stopPropagation();
            e.preventDefault();
        },
        handledot(e, it, corner) {
            if (!e.touches || e.touches.length === 0) return;

            const img = this.findFiles(it);
            if (!img || !img._cornerDrag) return; // 必须先有初始化数据

            const touch = e.touches[0];
            const x = touch.clientX || touch.pageX;
            const y = touch.clientY || touch.pageY;

            // 计算当前触摸点相对于初始触摸点的偏移
            const deltaX = x - img._cornerDrag.startX;
            const deltaY = y - img._cornerDrag.startY;

            const minSize = 20; // 最小尺寸限制
            const { topLeft, topRight, bottomLeft, bottomRight } = img._cornerDrag;

            // 根据拖拽的角，以对角为锚点进行拉伸
            switch (corner) {
                case 'top-left': // 拖拽左上角，右下角固定
                    {
                        const anchorX = bottomRight.x; // 右下角X固定
                        const anchorY = bottomRight.y; // 右下角Y固定

                        // 新的左上角位置
                        const newTopLeftX = topLeft.x + deltaX;
                        const newTopLeftY = topLeft.y + deltaY;

                        // 计算新的宽高
                        let newWidth = anchorX - newTopLeftX;
                        let newHeight = anchorY - newTopLeftY;

                        // 限制最小尺寸
                        if (newWidth < minSize) {
                            newWidth = minSize;
                            img.x = anchorX - newWidth;
                        } else {
                            img.x = newTopLeftX;
                        }

                        if (newHeight < minSize) {
                            newHeight = minSize;
                            img.y = anchorY - newHeight;
                        } else {
                            img.y = newTopLeftY;
                        }

                        img.w = newWidth;
                        img.h = newHeight;
                    }
                    break;

                case 'bottom-left': // 拖拽左下角，右上角固定
                    {
                        const anchorX = topRight.x; // 右上角X固定
                        const anchorY = topRight.y; // 右上角Y固定

                        // 新的左下角位置
                        const newBottomLeftX = bottomLeft.x + deltaX;
                        const newBottomLeftY = bottomLeft.y + deltaY;

                        // 计算新的宽高
                        let newWidth = anchorX - newBottomLeftX;
                        let newHeight = newBottomLeftY - anchorY;

                        // 限制最小尺寸
                        if (newWidth < minSize) {
                            newWidth = minSize;
                            img.x = anchorX - newWidth;
                        } else {
                            img.x = newBottomLeftX;
                        }

                        if (newHeight < minSize) {
                            newHeight = minSize;
                        } else {
                            img.y = anchorY;
                        }

                        img.w = newWidth;
                        img.h = newHeight;
                    }
                    break;

                case 'top-right': // 拖拽右上角，左下角固定
                    {
                        const anchorX = bottomLeft.x; // 左下角X固定
                        const anchorY = bottomLeft.y; // 左下角Y固定

                        // 新的右上角位置
                        const newTopRightX = topRight.x + deltaX;
                        const newTopRightY = topRight.y + deltaY;

                        // 计算新的宽高
                        let newWidth = newTopRightX - anchorX;
                        let newHeight = anchorY - newTopRightY;

                        // 限制最小尺寸
                        if (newWidth < minSize) {
                            newWidth = minSize;
                        }

                        if (newHeight < minSize) {
                            newHeight = minSize;
                            img.y = anchorY - newHeight;
                        } else {
                            img.y = newTopRightY;
                        }

                        img.x = anchorX;
                        img.w = newWidth;
                        img.h = newHeight;
                    }
                    break;

                case 'bottom-right': // 拖拽右下角，左上角固定
                    {
                        const anchorX = topLeft.x; // 左上角X固定
                        const anchorY = topLeft.y; // 左上角Y固定

                        // 新的右下角位置
                        const newBottomRightX = bottomRight.x + deltaX;
                        const newBottomRightY = bottomRight.y + deltaY;

                        // 计算新的宽高
                        let newWidth = newBottomRightX - anchorX;
                        let newHeight = newBottomRightY - anchorY;

                        // 限制最小尺寸
                        newWidth = Math.max(minSize, newWidth);
                        newHeight = Math.max(minSize, newHeight);

                        img.x = anchorX;
                        img.y = anchorY;
                        img.w = newWidth;
                        img.h = newHeight;
                    }
                    break;
            }

            // 边界检查，确保图片不超出容器
            if (img.x < 0) {
                img.w += img.x;
                img.x = 0;
            }
            if (img.y < 0) {
                img.h += img.y;
                img.y = 0;
            }
            if (img.x + img.w > this.dimensions.outerWidth) {
                img.w = this.dimensions.outerWidth - img.x;
            }
            if (img.y + img.h > this.dimensions.outerHeight) {
                img.h = this.dimensions.outerHeight - img.y;
            }

            // 如果是文字，同步调整字体大小
            if (img.type === 'text') {
                const sizeRatio = Math.min(
                    img.w / img._cornerDrag.startWidth,
                    img.h / img._cornerDrag.startHeight
                );
                img.size = Math.max(12, (img._cornerDrag.startSize || img.size) * sizeRatio);
            }

            this.$forceUpdate();

            // 防止事件冒泡
            e.stopPropagation();
            e.preventDefault();
        },

        // 角落拖动开始
        handleCornerStart(e, it, corner) {
            if (!e.touches || e.touches.length === 0) return;

            const img = this.findFiles(it);
            if (!img) return;

            const touch = e.touches[0];
            const x = touch.clientX || touch.pageX;
            const y = touch.clientY || touch.pageY;

            // 初始化裁剪拖拽数据
            img._cropDrag = {
                startX: x,
                startY: y,
                corner: corner,
                // 记录原始图片尺寸（如果没有设置过）
                originalWidth: img.originalWidth || img.w,
                originalHeight: img.originalHeight || img.h,
                // 记录当前裁剪区域
                cropX: img.cropX || 0,
                cropY: img.cropY || 0,
                cropWidth: img.cropWidth || img.w,
                cropHeight: img.cropHeight || img.h,
                // 记录当前显示位置和尺寸
                startPosX: img.x,
                startPosY: img.y,
                startWidth: img.w,
                startHeight: img.h
            };

            // 保存原始图片尺寸
            if (!img.originalWidth) {
                img.originalWidth = img.w;
                img.originalHeight = img.h;
            }

            e.stopPropagation();
            e.preventDefault();
        },

        // 角落拖动结束
        handleCornerEnd(e, it) {
            const img = this.findFiles(it);
            if (!img || !img._cropDrag) return;

            // 清理拖拽数据
            delete img._cropDrag;
            delete img._lastTouch;

            e.stopPropagation();
            e.preventDefault();
        },

        // 边框拖动调整图片显示范围（裁剪区域），图片大小不变做裁剪
        handleTouchs(e, it, direction) {
            if (!e.touches || e.touches.length === 0) return;

            const img = this.findFiles(it);
            if (!img) return;

            const touch = e.touches[0];
            const x = touch.clientX || touch.pageX;
            const y = touch.clientY || touch.pageY;

            // 处理四个角的拖动裁剪
            if (['top-left', 'top-right', 'bottom-left', 'bottom-right'].includes(direction)) {
                if (!img._cropDrag) return;

                const deltaX = x - img._cropDrag.startX;
                const deltaY = y - img._cropDrag.startY;

                // 获取原始图片尺寸作为最大限制
                const maxWidth = img._cropDrag.originalWidth;
                const maxHeight = img._cropDrag.originalHeight;
                const minSize = 20; // 最小裁剪尺寸

                // 计算新的裁剪区域，基于固定的锚点
                let newX, newY, newWidth, newHeight;

                switch (direction) {
                    case 'top-left':
                        // 拖拽左上角，右下角作为锚点固定
                        const anchorRightX = img._cropDrag.startPosX + img._cropDrag.startWidth;
                        const anchorBottomY = img._cropDrag.startPosY + img._cropDrag.startHeight;

                        newX = Math.max(0, img._cropDrag.startPosX + deltaX);
                        newY = Math.max(0, img._cropDrag.startPosY + deltaY);
                        newWidth = anchorRightX - newX;
                        newHeight = anchorBottomY - newY;
                        break;

                    case 'top-right':
                        // 拖拽右上角，左下角作为锚点固定
                        const anchorLeftX = img._cropDrag.startPosX;
                        const anchorBottomY2 = img._cropDrag.startPosY + img._cropDrag.startHeight;

                        newX = anchorLeftX;
                        newY = Math.max(0, img._cropDrag.startPosY + deltaY);
                        newWidth = Math.min(this.dimensions.outerWidth - anchorLeftX, img._cropDrag.startWidth + deltaX);
                        newHeight = anchorBottomY2 - newY;
                        break;

                    case 'bottom-left':
                        // 拖拽左下角，右上角作为锚点固定
                        const anchorRightX2 = img._cropDrag.startPosX + img._cropDrag.startWidth;
                        const anchorTopY = img._cropDrag.startPosY;

                        newX = Math.max(0, img._cropDrag.startPosX + deltaX);
                        newY = anchorTopY;
                        newWidth = anchorRightX2 - newX;
                        newHeight = Math.min(this.dimensions.outerHeight - anchorTopY, img._cropDrag.startHeight + deltaY);
                        break;

                    case 'bottom-right':
                        // 拖拽右下角，左上角作为锚点固定
                        newX = img._cropDrag.startPosX;
                        newY = img._cropDrag.startPosY;
                        newWidth = Math.min(this.dimensions.outerWidth - newX, img._cropDrag.startWidth + deltaX);
                        newHeight = Math.min(this.dimensions.outerHeight - newY, img._cropDrag.startHeight + deltaY);
                        break;
                }

                // 限制最小尺寸
                newWidth = Math.max(minSize, newWidth);
                newHeight = Math.max(minSize, newHeight);

                // 限制最大尺寸不超过原图
                newWidth = Math.min(maxWidth, newWidth);
                newHeight = Math.min(maxHeight, newHeight);

                // 确保不超出容器边界
                if (newX + newWidth > this.dimensions.outerWidth) {
                    newWidth = this.dimensions.outerWidth - newX;
                }
                if (newY + newHeight > this.dimensions.outerHeight) {
                    newHeight = this.dimensions.outerHeight - newY;
                }

                // 对于左边和上边的拖拽，需要重新调整位置以保持锚点固定
                if (direction === 'top-left') {
                    const anchorRightX = img._cropDrag.startPosX + img._cropDrag.startWidth;
                    const anchorBottomY = img._cropDrag.startPosY + img._cropDrag.startHeight;
                    newX = anchorRightX - newWidth;
                    newY = anchorBottomY - newHeight;
                } else if (direction === 'bottom-left') {
                    const anchorRightX = img._cropDrag.startPosX + img._cropDrag.startWidth;
                    newX = anchorRightX - newWidth;
                } else if (direction === 'top-right') {
                    const anchorBottomY = img._cropDrag.startPosY + img._cropDrag.startHeight;
                    newY = anchorBottomY - newHeight;
                }

                // 更新图片位置和尺寸
                img.x = newX;
                img.y = newY;
                img.w = newWidth;
                img.h = newHeight;

                this.$forceUpdate();
                return;
            }

            // 处理边框拖动（保留原有功能）
            if (!img._lastTouch) {
                img._lastTouch = { x, y };
                return;
            }

            const dx = x - img._lastTouch.x;
            const dy = y - img._lastTouch.y;

            let newW = img.w + dx;
            let newH = img.h + dy;

            // 限制最小和最大值
            newW = Math.max(20, Math.min(newW, this.dimensions.outerWidth));
            newH = Math.max(20, Math.min(newH, this.dimensions.outerHeight));

            img.w = newW;
            img.h = newH;

            img._lastTouch = { x, y };
            this.$forceUpdate();
        },




        // 触摸开始
        handleTouchStart(e, item) {
            if (!e.touches || e.touches.length === 0) return;
            this.isDragging = true;

            // 记录触摸起点（使用pageX/pageY更适合小程序）
            this.startPoint = {
                x: e.touches[0].pageX || e.touches[0].clientX,
                y: e.touches[0].pageY || e.touches[0].clientY
            };

            // 记录元素当前位置
            this.initialPosition = {
                x: item.x,
                y: item.y
            };

            this.disp(item)
            // console.log('触摸开始:', this.startPoint, '当前位置:', item, y);
        },

        // 触摸移动
        handleTouchMove(e, item) {
            if (!e.touches || e.touches.length === 0) return;

            // 阻止默认行为
            e.preventDefault();
            e.stopPropagation();

            // 获取当前触摸点
            const currentX = e.touches[0].pageX || e.touches[0].clientX;
            const currentY = e.touches[0].pageY || e.touches[0].clientY;

            // 计算移动距离
            const moveX = currentX - this.startPoint.x;
            const moveY = currentY - this.startPoint.y;

            // 计算新位置
            let newX = this.initialPosition.x + moveX;
            let newY = this.initialPosition.y + moveY;

            // 限制边界（确保元素不会移出容器）
            const maxX = this.dimensions.outerWidth - item.w;
            const maxY = this.dimensions.outerHeight - item.h;

            newX = Math.max(0, Math.min(newX, maxX));
            newY = Math.max(0, Math.min(newY, maxY));

            // 更新位置
            this.position = {
                x: newX,
                y: newY
            };

            const img = this.findFiles(item);

            img.x = newX;
            img.y = newY;

            console.log('移动到:', this.dataType);
        },

        // 触摸结束
        handleTouchEnd() {
            this.isDragging = false;
            console.log('触摸结束，最终位置:', this.position);
            // 可以在这里添加触摸结束后的逻辑，比如吸附效果等

            // 清理角落拖拽数据
            this.dataType.forEach(item => {
                if (item._cornerDrag) {
                    delete item._cornerDrag;
                }
                if (item._lastTouch) {
                    delete item._lastTouch;
                }
            });

            console.log('触摸结束，最终位置:', this.position);
        },
        // 缩放
        scaleItem(e, it) {
            console.log(e, it, 123);
            // 获取触摸移动的距离，计算缩放比例

            if (e && e.touches && e.touches.length === 1) {
                var img = this.findFiles(it)
                // 单指缩放（通过垂直滑动改变高度，水平滑动改变宽度）
                const touch = e.touches[0];
                // 在uni-app中，touch对象通常有clientX/clientY或pageX/pageY
                const x = touch.clientX || touch.pageX;
                const y = touch.clientY || touch.pageY;

                if (!img._lastTouch) {
                    img._lastTouch = { x, y };
                    return;
                }
                const dx = x - img._lastTouch.x;
                const dy = y - img._lastTouch.y;

                let newW = img.w + dx;
                let newH = img.h + dy;

                // 限制最小和最大值
                newW = Math.max(20, Math.min(newW, this.dimensions.outerWidth));
                newH = Math.max(20, Math.min(newH, this.dimensions.outerHeight));

                img.w = newW;
                img.h = newH;
                // 如果放大的是文字则字体大小跟着缩放大小
                // if (img.type == 'text') {
                //     img.size = img.size + ((newW + newH) / 10)
                // } 
                img._lastTouch = { x, y };
                this.$forceUpdate();
            }
        },
        // 复制一份
        copyItem(e) {
            var img = this.findFiles(e)
            console.log(e, 'mmm');
            img.display = false
            this.dataType.push({
                ...img,
                ids: this.dataType.length + 1,
                name: img.name + (this.dataType.length + 1),
                sox: img.x + 20,
                soy: img.y + 20,
                x: img.x + 20,
                y: img.y + 20,
                display: true
            })
        },

        // 旋转 - 手指拖动旋转
        rotateItem(e, it) {
            const img = this.findFiles(it);

            if (!img || !e.touches || !e.touches.length) return;

            const touch = e.touches[0];
            const x = touch.pageX || touch.clientX;
            const y = touch.pageY || touch.clientY;

            // 使用简化的坐标计算方法
            // 估算画布在屏幕中的位置（根据您的布局调整这些值）
            const canvasTop = 120; // 顶部导航栏高度
            const canvasLeft = 20;  // 左边距

            // 计算元素中心点的屏幕坐标
            const centerX = canvasLeft + img.x + img.w / 2;
            const centerY = canvasTop + img.y + img.h / 2;

            // 计算触摸点相对于元素中心的向量
            const deltaX = x - centerX;
            const deltaY = y - centerY;

            // 计算角度
            const currentAngle = Math.atan2(deltaY, deltaX);

            // 如果是第一次触摸，记录初始角度
            if (img._startAngle === undefined) {
                img._startAngle = currentAngle;
                img._initialRotate = img.rotate || 0;
                return;
            }

            // 计算角度差值
            let angleDiff = currentAngle - img._startAngle;

            // 处理角度跨越边界的情况
            if (angleDiff > Math.PI) {
                angleDiff -= 2 * Math.PI;
            } else if (angleDiff < -Math.PI) {
                angleDiff += 2 * Math.PI;
            }

            // 更新旋转角度
            img.rotate = img._initialRotate + angleDiff;

        },

        // 旋转结束 - 清理临时变量
        rotateEnd(it) {
            const img = this.findFiles(it);
            if (!img) return;

            // 清理临时变量
            delete img._startAngle;
            delete img._initialRotate;
        },

        recordClick(e) {
            console.log(typeof e);
            if (typeof e == 'object') {
                this.record.push(this.dataType.filter(item => item.ids == e.ids)[0])
                this.dataType = this.dataType.filter(item => item.ids !== e.ids)
            } else {
                this.record.push(...this.dataType)
                this.dataType = []
            }
        },

        disp(e) {
            this.dataType.forEach(element => {
                if (element.ids == e.ids) {
                    element.display = true
                    this.selectedItem = element // 设置当前选中的元素
                } else {
                    element.display = false
                }
            });
        },
        // 数据ids匹配
        findFiles(e) {
            return this.dataType.find(f => f.ids == e.ids)
        },
        // 删除
        deleteItem(it) {
            this.recordClick(it)
        },


    }
};
</script>

<style scoped>
.container {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    /* border: 1px solid red; */
}

.main-background-img {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
}

.outer-box {
    border: 2rpx solid #333;
    position: relative;
    z-index: 3;
}

.outer-box2 {

    background-color: transparent;
    background-color: #e8e8e8;
    background-image:
        linear-gradient(45deg, #d0d0d0 25%, transparent 25%),
        linear-gradient(-45deg, #d0d0d0 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #d0d0d0 75%),
        linear-gradient(-45deg, transparent 75%, #d0d0d0 75%);
    background-size: 16px 16px;
    background-position: 0 0, 0 8px, 8px -8px, -8px 0px;
}

.movable-content {
    /* background-color: #4cd964; */
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 16rpx;
    position: absolute;
    /* font-size: 28rpx; */
    /* font-weight: bold; */
    transition: transform 0.1s all;
}



.custom-border {
    position: relative;

    /* border-radius: 8px; */
    box-sizing: border-box;
    pointer-events: none;
    z-index: 10;
    padding: 7px;
}

.corner-btn {
    width: 20px;
    height: 20px;
    position: absolute;
    z-index: 11;
    pointer-events: auto;
    border-radius: 50%;
}

.corner-route {
    position: absolute;
    width: 10px;
    height: 10px;
    background: #fff;
    border: 2px solid #333;
    background-color: #fff;
    border-radius: 50%;
}

.corner-top {
    top: -8px;
    left: calc(50% - 4px);
    transform: translateX(-50%);
}

.corner-right {
    right: -15px;
    top: calc(50% - 7px);
    transform: translateX(-50%);
}

.corner-btm {
    left: calc(50% - 7px);
    bottom: -8px;
    transform: translateX(-50%);
}

.corner-left {
    left: -2px;
    top: calc(50% - 7px);
    transform: translateX(-50%);
}

.corner-bottom {
    left: 50%;
}


.corner-del {
    left: -10px;
    top: -10px;
}

.corner-copy {
    left: -10px;
    bottom: -10px;
}

.corner-rotate {
    right: -10px;
    top: -10px;
}

.corner-scale {
    right: -10px;
    bottom: -10px;
}



/*  */

/*  */
.movableViews {
    padding: 8px;
}

.border-line {
    position: absolute;
    z-index: 122;
    width: 40rpx;
    height: 40rpx;
    /* border: 1px solid red; */
    pointer-events: auto;
}

.border-top {
    left: -4px;
    top: -4px;
    border-left: 6px solid rgb(0, 165, 22);
    border-top: 6px solid rgb(0, 165, 22);
}

.border-bottom {
    bottom: -4px;
    right: -4px;
    border-right: 6px solid rgb(0, 165, 22);
    border-bottom: 6px solid rgb(0, 165, 22);
}

.border-left {
    left: -4px;
    bottom: -4px;
    border-left: 6px solid rgb(0, 165, 22);
    border-bottom: 6px solid rgb(0, 165, 22);
}

.border-right {
    right: -4px;
    top: -4px;
    border-right: 6px solid rgb(0, 165, 22);
    border-top: 6px solid rgb(0, 165, 22);
}

.border-dot {
    position: absolute;
    width: 10px;
    height: 10px;
    background: #fff;
    border: 2px solid #333;
    border-radius: 50%;
    z-index: 13;
    pointer-events: auto;
}

/* 四个角的拖动点样式 */
.corner-dot-top-left,
.corner-dot-top-right,
.corner-dot-bottom-left,
.corner-dot-bottom-right {
    position: absolute;
    width: 14px;
    height: 14px;
    background: #007aff;
    border: 2px solid #fff;
    border-radius: 50%;
    z-index: 14;
    pointer-events: auto;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.corner-dot-top-left:active,
.corner-dot-top-right:active,
.corner-dot-bottom-left:active,
.corner-dot-bottom-right:active {
    background: #0056cc;
    transform: scale(1.1);
}
</style>