<template>
    <view class="temps">
        <view class="container">
            <!-- 顶部导航栏 -->
            <view class="header">
                <view class="header-btn" @click="goBack">
                    <text class="btn-text">返回</text>
                </view>
                <view class="header-btn headerVtn" @click="saveImage">
                    <text class="btn-text">保存</text>
                </view>
            </view>

            <!-- 主要内容区域 -->
            <view class="main-content">
                <view class="main-content-left" :style="{ width: canvasStyle.width, height: canvasStyle.height }">



                    <!-- <movable-area :class="dataBack ? '' : 'canvas-area'"
                        :style="{ width: canvasStyle.width, height: canvasStyle.height, zIdex: 2 }">
                        <movable-view class="movableViews"
                            v-for="(it, index) in data.filter(e => e.type != 'background')" @click="onClick(it)"
                            :out-of-bounds="false" :key="index" :x="it.sox" :y="it.soy" direction="all"
                            @change="(e) => onChange(e, it)" :style="{
                                width: it.w + 'px',
                                height: it.h + 'px',
                                zIndex: index + 1,
                            }" v-show="it.closes">

                            <view class="custom-border" :style="{
                                width: (it.w + 17) + 'px', height: (it.h + 17) + 'px',
                                border: it.display ? '2px dashed rgb(0, 0, 0)' : 'none',
                                transform: it.rotate ? `rotate(${it.rotate}rad)` : '',
                                transformOrigin: `${(it.w + 17) / 2}px ${(it.h + 17) / 2}px`,

                                fontWeight: it.Weights ? '900' : 'none',
                            }">
                                <image class="movableImage" :style="{
                                    width: it.w + 'px', height: it.h + 'px',
                                }" v-if="it.type != 'text'" :src="it.url" mode="scaleToFill" />

                                <view class="movableText" :style="{
                                    width: it.w,
                                    height: it.h,
                                    fontSize: it.size + 'px',
                                    color: it.color,
                                    fontFamily: it.textTpey,
                                }" v-else>{{ it.text }}</view>
                                <view v-if="it.display">
                                    <image class="corner-btn corner-del" mode="aspectFit" :src="del"
                                        @click.stop="deleteItem(it)" />
                                    <image class="corner-btn corner-copy" mode="aspectFit" :src="jia"
                                        @click.stop="copyItem(it)" />
                                    <image class="corner-btn corner-rotate" mode="aspectFit" :src="rotate"
                                        @touchmove.stop="(e) => rotateItem(e, it)"
                                        @touchend.stop="(e) => rotateEnd(it)" />
                                    <image class="corner-btn corner-scale" mode="aspectFit" :src="suof"
                                        @touchmove.stop="(e) => scaleItem(e, it)" />
                                </view>
                            </view>
                        </movable-view>
                    </movable-area> -->

                    <movableSrea ref="movableSreas"></movableSrea>
                    <drawLines ref="drawLiness" :width="canvasStyle.width" :height="canvasStyle.height"></drawLines>

                </view>

            </view>

            <!-- 底部工具栏 -->
            <view class="toolbar">
                <view class="toolbar-left">
                    <view class="tool-btn" @click="undo">
                        <text class="tool-icon">↶</text>
                    </view>
                    <view class="tool-btn" @click="redo">
                        <text class="tool-icon">↷</text>
                    </view>
                    <view class="tool-btn clear-btn" @click="clear">
                        <text class="tool-text">清空</text>
                    </view>
                    <view class="tool-btn clear-btn" @click="containersd = !containersd">
                        <text class="tool-text">图层</text>
                    </view>
                </view>

                <view style="width: 100%;height: 60px;">
                    <tables ref="tablesRef" :width="canvasStyle.width" :height="canvasStyle.height"></tables>
                </view>

            </view>
        </view>

        <!-- 画布图层 -->
        <view class="containersd" v-if="containersd">
            <view class="header-2">
                <text class="title-2">图层</text>
            </view>
            <view class="content-2">

                <view class="content-2-1" v-for="(item, index) in data" :key="index">
                    <view class="content-2-1-1">
                        <image v-if="item.closes" mode="aspectFit" @click="item.closes = !item.closes"
                            src="./images/display.png" class="icon-1">
                        </image>
                        <image v-else mode="aspectFit" @click="item.closes = !item.closes" src="./images/hide.png"
                            class="icon-1">
                        </image>
                        <view class="textIcon-2">
                            <text v-if="item.type === 'text'" style="font-size: 14px;">文</text>
                            <image v-else :src="item.url" class="icon-2"></image>
                        </view>
                        <view @click="countClick(item)">↑</view>
                    </view>

                    <view class="count-2">{{ item.name }}</view>

                    <view class="close-btn-2" @click="recordClick(item)">×</view>
                </view>

                <view v-if="!data.length" style="padding: 20px 0px;">
                    暂无数据...
                </view>
            </view>

            <view class="footer-2" @click="containersd = !containersd">
                <text class="footer-text-2">收起图标</text>
            </view>
        </view>

        <canvass :info="info" :width="canvasStyle.width" :height="canvasStyle.height"></canvass>
    </view>
</template>

<script>
import tables from './componets/table.vue'
import canvass from './componets/canvas.vue'
import drawLines from './componets/drawLines.vue'
import movableSrea from './componets/movableSrea.vue'
export default {
    props: {
        canvasStyle: {
            type: Object,
            default: {
                width: '375', // 单位是px ，rpx * 2 就行
                height: '300'// 单位是px ，rpx * 2 就行
            }
        },
        imageData: {
            type: Array,
            default: [
                // {
                //     ids: 0,
                //     name: '图片',
                //     x: 10,
                //     y: 10,
                //     w: null,
                //     h: null,
                //     type: 'background',
                //     size: 0,
                //     url: 'https://bpic.588ku.com/element_origin_min_pic/23/07/11/d32dabe266d10da8b21bd640a2e9b611.jpg!r650',
                //     text: '',
                //     rotate: 10,
                //     display: true,
                //     color: '',
                //     closes: true,
                // },
                {
                    ids: 2,
                    name: '图片',
                    x: 50,
                    y: 50,
                    w: 150,
                    h: 150,
                    type: '',
                    size: 0,
                    url: 'https://bpic.588ku.com/element_origin_min_pic/23/07/11/d32dabe266d10da8b21bd640a2e9b611.jpg!r650',
                    text: '',
                    rotate: 0,
                    display: true,
                    color: '',
                    closes: true,
                },

                // {
                //     ids: 1,
                //     name: '文字',
                //     x: 0,
                //     y: 0,
                //     w: 100,
                //     h: 40,
                //     type: 'text',
                //     size: 18,
                //     url: '',
                //     text: '哈哈哈哈',
                //     rotate: 10,
                //     display: false,
                //     color: '#8BC34A',
                //     closes: true,
                //     textTpey: '"KaiTi", serif'
                // }
            ]
        },
        returnType: {
            type: String,
            default: 'png', // 'png' 返回图片, 'all' 图片和数据, 'data' 数据  uni.$on('imageData',(e) => {})
        }
    },
    components: {
        tables,
        canvass,
        drawLines,
        movableSrea,
    },
    data() {
        return {
            containersd: false,
            currentTool: 'brush',
            // imageData: null,
            data: [],
            display: true,
            rotateTimer: null,
            selectedItem: null, // 当前选中的元素
            record: [], // 撤销记录
            Timeout: null,
            TimeoutNum: 300,
            dataBack: {},
            info: {}, // 画布信息
        }
    },
    watch: {
        // data: {
        //     handler(newVal, oldVal) {
        //         clearTimeout(this.Timeout)
        //         this.Timeout = setTimeout(() => {
        //             this.data.forEach((f) => {
        //                 f.sox = f.x
        //                 f.soy = f.y
        //             })
        //         }, this.TimeoutNum)
        //     },
        //     deep: true
        // }
    },
    mounted() {
        this.init()

        this.mainContentLeft()
    },
    methods: {
        mainContentLeft() {
            const query = uni.createSelectorQuery().in(this);

            // 选择目标标签并获取布局信息
            query.select('.main-content-left')
                .boundingClientRect(data => {
                    if (data) {
                        this.info = data
                        this.$refs.drawLiness.info = this.info
                        console.log('标签信息：', data);
                    } else {
                        console.log('未找到目标标签');
                    }
                })
                .exec(); // 执行查询
        },
        init() {
            this.dataBack = this.imageData.find(e => e.type == 'background') || {}
            this.data = this.imageData.filter(e => e.type !== 'background')
            // console.log(this.dataBack.url,'this.dataBack');
            this.$refs.movableSreas.dataBack = this.dataBack || null
            this.$refs.movableSreas.dataType = this.data
            this.$refs.movableSreas.dimensions.outerWidth = this.canvasStyle.width
            this.$refs.movableSreas.dimensions.outerHeight = this.canvasStyle.height

        },
        goBack() {
            uni.navigateBack();
        },

        saveImage() {
            const data = [this.$refs.movableSreas.dataBack, ...this.$refs.movableSreas.dataType]
            console.log('data', data);

            if (this.returnType == 'data') {
                uni.$emit('imageData', data)
            } else if (this.returnType == 'png') {
                uni.$emit('draws', data)
            } else {
                uni.$emit('imageData', data)
                uni.$emit('draws', data)
            }

        },

        undo() {
            console.log('重做操作');
            if (this.$refs.movableSreas.dataType.length > 0) {
                this.$refs.movableSreas.record.push(this.$refs.movableSreas.dataType[this.$refs.movableSreas.dataType.length - 1])
                this.$refs.movableSreas.dataType.pop()
            } else {
                uni.showToast({
                    title: '没有可重做的操作',
                    icon: 'none',
                    duration: 1500
                });
            }
        },

        redo() {
            console.log('撤销操作');
            if (this.$refs.movableSreas.record.length > 0) {
                this.$refs.movableSreas.dataType.push(this.$refs.movableSreas.record[this.$refs.movableSreas.record.length - 1])
                this.$refs.movableSreas.record.pop()
            } else {
                uni.showToast({
                    title: '没有可撤销的操作',
                    icon: 'none',
                    duration: 1500
                });
            }
        },
        swapWithPrevious(arr, condition) {
            // 找到目标对象的索引
            const targetIndex = arr.findIndex(condition);

            // 检查索引有效性（不能是第一个元素，否则没有前一个对象）
            if (targetIndex > 0) {
                // 保存前一个对象的索引
                const prevIndex = targetIndex - 1;

                // 交换两个对象的位置（解构赋值语法）
                [arr[prevIndex], arr[targetIndex]] = [arr[targetIndex], arr[prevIndex]];
            }

            return arr;
        },
        countClick(e) {
            const i = this.swapWithPrevious(this.$refs.movableSreas.dataType, (item) => item.ids === e.ids);
            this.$refs.movableSreas.dataType = [...i]
            this.data = this.$refs.movableSreas.dataType
        },
        clear() {
            uni.showModal({
                title: '确认',
                content: '确定要清空画布吗？',
                success: (res) => {
                    if (res.confirm) {
                        this.recordClick()
                    }
                }
            });
        },
        recordClick(e) {
            console.log(typeof e);
            if (typeof e == 'object') {
                this.$refs.movableSreas.record.push(this.$refs.movableSreas.dataType.filter(item => item.ids == e.ids)[0])
                this.$refs.movableSreas.dataType = this.$refs.movableSreas.dataType.filter(item => item.ids !== e.ids)
            } else {
                this.$refs.movableSreas.record.push(...this.$refs.movableSreas.dataType)
                this.$refs.movableSreas.dataType = []
            }

            this.data = this.$refs.movableSreas.dataType
        },
        showImagePicker() {
            uni.chooseImage({
                count: 1,
                success: (res) => {
                    // this.imageData = res.tempFilePaths[0];
                    // console.log('选择图片:', this.imageData);
                }
            });
        },

        // onClick(e) {
        //     this.disp(e)
        // },


    }
}
</script>

<style scoped>
.temps {
    width: 100%;
    height: 100vh;
    background-color: #2c2c2c;
}

.container {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #2c2c2c;
    position: fixed;
    top: 0px;
    left: 0px;
}

/* 顶部导航栏样式 */
.header {
    width: 100%;
    height: 55px;
    background-color: #2c2c2c;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;
    box-sizing: border-box;
}

.header-btn {
    padding: 6px 14px;
    /* border: 1px solid #8bc34a; */
    border-radius: 3px;
    background-color: transparent;
    min-width: 50px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.headerVtn {
    margin-right: 10px !important;
    width: 30px;
    height: 40rpx;
    border-radius: 10rpx;
    background-color: #4CAF50;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 14px;
    cursor: pointer;
}

.headerVtn image {
    width: 20px;
    height: 20px;
}

.btn-text {}

.headerVtn image {
    width: 20px;
    height: 20px;
}

.btn-text {
    color: #fff;
    font-size: 13px;
}

/* 主要内容区域样式 */
.main-content {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.canvas-area {
    background-color: transparent;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #e8e8e8;
    background-image:
        linear-gradient(45deg, #d0d0d0 25%, transparent 25%),
        linear-gradient(-45deg, #d0d0d0 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #d0d0d0 75%),
        linear-gradient(-45deg, transparent 75%, #d0d0d0 75%);
    background-size: 16px 16px;
    background-position: 0 0, 0 8px, 8px -8px, -8px 0px;
}

.canvas-back {
    background-position: center;
    background-size: 100%;
    background-repeat: no-repeat;
}

.canvas {
    border: 1px solid #ccc;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    max-width: 100%;
    max-height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

.canvas img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.canvas-text {}

.main-content-left {
    position: relative;
}


.placeholder {
    text-align: center;
}

.placeholder-text {
    color: #999;
    font-size: 16px;
}

/* 底部工具栏样式 */
.toolbar {
    width: 100%;
    height: 110px;
    background-color: #2c2c2c;
    /* display: flex;
    align-items: center;
    justify-content: space-between; */
    /* padding: 0 15px; */
    box-sizing: border-box;
    position: relative;
}

.toolbar-left {
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 100%;
    margin-top: 10rpx;
    margin-bottom: 10rpx;

}

.toolbar-center {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 2;
    justify-content: center;
}

.toolbar-right {
    display: flex;
    justify-content: flex-end;
    flex: 1;
}

.tool-btn {
    width: 36px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    background-color: transparent;
}

.main-tool {
    width: 42px;
    height: 42px;
    background-color: rgba(255, 255, 255, 0.08);
    border-radius: 6px;
}

.tool-icon-box {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.tool-icon {
    color: #fff;
    font-size: 16px;
}

.tool-text {
    color: #fff;
    font-size: 11px;
}

.tool-text-icon {
    color: #fff;
    font-size: 14px;
    font-weight: bold;
}

.clear-btn {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    padding: 0 8px;
    min-width: 40px;
}

.gallery-btn {
    background-color: #4a4a4a;
    border-radius: 4px;
    padding: 0 12px;
    min-width: 50px;
}

.tool-btn:active {
    background-color: rgba(255, 255, 255, 0.2);
}

.tool-label {
    color: #fff;
    font-size: 10px;
    margin-top: 2px;
    text-align: center;
}

.main-tool {
    flex-direction: column;
    height: 50px;
    padding: 4px 2px;
}

.containersd {
    width: 200px;
    /* height: 150px; */
    background-color: #333;
    border-radius: 10px;
    color: white;
    text-align: center;
    position: absolute;
    right: 0px;
    top: 20%;
}

.header-2 {
    height: 30px;
    line-height: 30px;
    border-bottom: 1px solid #ccc;
}

.title-2 {
    color: #fff;
}

.content-2 {
    width: 100%;
    /* padding: 10px; */
    max-height: 500px;
    overflow: auto;
}

.content-2-1 {
    width: 95%;
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    margin: auto;
    margin-top: 5px;
}

.content-2-1-1 {
    width: 40%;
    display: flex;
    align-items: center;
    justify-content: space-between;

}

.textIcon-2 {
    width: 25px;
    height: 25px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    color: #000;
}

.icon-1 {
    width: 15px;
    height: 15px;
}

.icon-2 {
    width: 100%;
    height: 100%;
}

.count-2 {
    font-size: 14px;
}

.close-btn-2 {
    font-size: 20px;
    cursor: pointer;
}

.footer-2 {
    height: 40px;
    line-height: 40px;
    border-top: 1px solid #ccc;
}

.footer-text-2 {
    font-size: 14px;
}
</style>