
.container.data-v-a4b6909a {
    background: #222;
    min-height: 85vh;
    height: 85vh;
    position: absolute;
    padding-bottom: 90rpx;
    overflow: hidden;
    width: 100%;
}
.header.data-v-a4b6909a {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 24rpx 0 0rpx;
    gap: 20rpx;
    width: 100%;
}
.header-btn.data-v-a4b6909a {
    background: transparent;
    border: 1rpx solid #b6ff3b;
    color: #b6ff3b;
    border-radius: 8rpx;
    /* padding: 8rpx 32rpx; */
    font-size: 28rpx;
}
.header-btn.save.data-v-a4b6909a {
    /* margin-left: 16rpx; */
}
.main-area.data-v-a4b6909a {
    margin: 40rpx auto 0 auto;
    background: #333;
    width: 100%;
    height: 81%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12rpx;
    position: absolute;
    overflow: hidden;
    /* border: 1px solid red; */
}
.upload-btn.data-v-a4b6909a {
    position: absolute;
    left: 20rpx;
    top: 20rpx;
    z-index: 2;
    background: #b6ff3b;
    color: #222;
    border-radius: 8rpx;
    padding: 8rpx 24rpx;
    font-size: 24rpx;
    border: none;
}
.main-img.data-v-a4b6909a {

    width: 100%;
    height: 950rpx;
    object-fit: contain;
    /* border: 1rpx solid red; */
}
.draggable-img.data-v-a4b6909a {
    position: absolute;
    z-index: 2;
    /* 可根据需要调整初始大小 */
    /*  */
    background-size: 20px 20px;
    position: absolute;
    background-position: 0 0, 10px 10px;
    /* border: 1px solid red; */
}
.autos.data-v-a4b6909a {
    background-color: #f5f5f5;
    background-image: linear-gradient(45deg, #e0e0e0 25%, transparent 25%, transparent 75%, #e0e0e0 75%),
        linear-gradient(45deg, #e0e0e0 25%, transparent 25%, transparent 75%, #e0e0e0 75%);
}
.toolbar.data-v-a4b6909a {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100vw;
    background: #111;
    display: flex;
    justify-content: space-around;
    align-items: center;
    height: 140rpx;
    border-top: 1rpx solid #333;
    z-index: 10;
}
.toolbar-item.data-v-a4b6909a {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #fff;
    font-size: 22rpx;
}
.toolbar-icon.data-v-a4b6909a {
    width: 40rpx;
    height: 40rpx;
    margin-bottom: 4rpx;
}
.layer-box.data-v-a4b6909a {
    position: fixed;
    right: 0rpx;
    bottom: 140rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100rpx;
    /* border: 1px solid red; */
    color: #fff;
}
.layer-btn.data-v-a4b6909a {
    background: #333;
    color: #fff;
    border-radius: 8rpx;
    padding: 12rpx 32rpx;
    font-size: 26rpx;
    border: none;
    z-index: 2;
    width: 100%;
}
.sidebarBox.data-v-a4b6909a {
    position: absolute;
    left: 0;
    bottom: 0rpx;
    width: 100%;
    background: #191919;
    /* border: 1px solid blue; */
    z-index: 3;
}
.sidebar.data-v-a4b6909a {
    width: 100%;
    height: 40vh;
    z-index: 5;
    display: flex;
    /* flex-direction: column; */
    padding-top: 20rpx;
}
.sidebar-section.data-v-a4b6909a {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    gap: 10rpx;
    padding: 0 10rpx;
}
.sidebar-title.data-v-a4b6909a {
    color: #bbb;
    font-size: 22rpx;
    margin-right: 16rpx;
    padding: 8rpx 0;
}
.sidebar-title.active.data-v-a4b6909a {
    color: #b6ff3b;
    border-bottom: 2rpx solid #b6ff3b;
}
.sidebar-menu.data-v-a4b6909a {
    /* margin-top: 30rpx; */
    display: flex;
    gap: 10rpx;
}
.sidebar-menu-item.data-v-a4b6909a {
    color: #bbb;
    font-size: 22rpx;
    padding: 8rpx 16rpx;
    border-radius: 6rpx;
}
.sidebar-menu-item.active.data-v-a4b6909a {
    background: #222;
    color: #b6ff3b;
}
.material-content.data-v-a4b6909a {
    /* position: absolute;
    left: 180rpx;
    top: 120rpx;
    right: 0; */
    /* height: 72vh; */
    background: transparent;
    z-index: 4;
    /* padding: 24rpx 0 0 0; */
    overflow-y: auto;
}
.material-row.data-v-a4b6909a {
    display: flex;
    flex-direction: row;
    gap: 24rpx;
    margin-bottom: 24rpx;
    justify-content: flex-start;
    padding-left: 24rpx;
}
.material-card.data-v-a4b6909a {
    background: #222;
    border-radius: 12rpx;
    width: 160rpx;
    height: 200rpx;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    align-items: center;
    position: relative;
    box-shadow: 0 2rpx 8rpx #0006;
    padding: 12rpx 0;
    border: 2px solid #222;
}
.material-circle.data-v-a4b6909a {
    border: 2px solid #fff;
}
.material-img.data-v-a4b6909a {
    width: 120rpx;
    height: 120rpx;
    object-fit: contain;
    margin-bottom: 8rpx;
}
.material-title.data-v-a4b6909a {
    color: #fff;
    font-size: 20rpx;
    margin-bottom: 4rpx;
    text-align: center;
}
.material-tag.data-v-a4b6909a {
    position: absolute;
    top: 8rpx;
    right: 8rpx;
    background: #b6ff3b;
    color: #222;
    border-radius: 50%;
    width: 32rpx;
    height: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20rpx;
    font-weight: bold;
}
.material-vip.data-v-a4b6909a {
    position: absolute;
    top: 8rpx;
    right: 8rpx;
    background: #ffd700;
    color: #222;
    border-radius: 8rpx;
    padding: 2rpx 8rpx;
    font-size: 16rpx;
    font-weight: bold;
}
.text-toolbar.data-v-a4b6909a {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0rpx;
    background: #222;
    border-radius: 16rpx 16rpx 0 0;
    padding: 32rpx 24rpx 24rpx 24rpx;
    z-index: 3;
    box-shadow: 0 -4rpx 24rpx #000a;
}
.text-toolbar-row.data-v-a4b6909a {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
}
.text-input.data-v-a4b6909a {
    flex: 1;
    background: #333;
    color: #fff;
    border: 1rpx solid #444;
    border-radius: 8rpx;
    padding: 12rpx 20rpx;
    font-size: 28rpx;
    outline: none;
}
.font-row.data-v-a4b6909a {
    flex-wrap: wrap;
    gap: 16rpx;
}
.font-btn.data-v-a4b6909a {
    background: #333;
    color: #fff;
    border: 1rpx solid #444;
    border-radius: 8rpx;
    padding: 8rpx 24rpx;
    font-size: 26rpx;
    margin-right: 12rpx;
    margin-bottom: 8rpx;
    transition: border 0.2s, color 0.2s;
}
.font-btn.active.data-v-a4b6909a {
    border: 2rpx solid #b6ff3b;
    color: #b6ff3b;
}
.size-row.data-v-a4b6909a {
    gap: 16rpx;
}
.label.data-v-a4b6909a {
    color: #bbb;
    font-size: 22rpx;
    margin-right: 12rpx;
}
.size-slider.data-v-a4b6909a {
    flex: 1;
    margin: 0 12rpx;
}
.size-value.data-v-a4b6909a {
    color: #b6ff3b;
    font-size: 24rpx;
    min-width: 48rpx;
    text-align: right;
}
.color-row.data-v-a4b6909a {
    gap: 16rpx;
}
.color-list.data-v-a4b6909a {
    display: flex;
    flex-direction: row;
    gap: 16rpx;
    margin-left: 12rpx;
}
.color-circle.data-v-a4b6909a {
    width: 36rpx;
    height: 36rpx;
    border-radius: 50%;
    border: 2rpx solid #888;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    box-sizing: border-box;
}
.color-circle.selected.data-v-a4b6909a {
    border: 2rpx solid #b6ff3b;
}
.color-selected.data-v-a4b6909a {
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    background: #fff;
    position: absolute;
    top: 10rpx;
    left: 10rpx;
    box-shadow: 0 0 4rpx #b6ff3b;
}
.end-btn.data-v-a4b6909a {
    width: 90%;
    height: 40rpx;
    text-align: right;
    color: #fff;
    margin: 10rpx auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}



/* 画线 */
.pen-toolbar.data-v-a4b6909a {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    background: #222;
    border-radius: 16rpx 16rpx 0 0;
    padding: 32rpx 24rpx 24rpx 24rpx;
    z-index: 3;
    box-shadow: 0 -4rpx 24rpx #000a;
}
.pen-row.data-v-a4b6909a {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
    gap: 16rpx;
}
.pen-color-list.data-v-a4b6909a {
    display: flex;
    flex-direction: row;
    gap: 16rpx;
    margin-left: 12rpx;
}
.pen-color-circle.data-v-a4b6909a {
    width: 36rpx;
    height: 36rpx;
    border-radius: 50%;
    border: 2rpx solid #888;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    box-sizing: border-box;
}
.pen-color-circle.selected.data-v-a4b6909a {
    border: 2rpx solid #b6ff3b;
}
.pen-color-selected.data-v-a4b6909a {
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    background: #fff;
    position: absolute;
    top: 10rpx;
    left: 10rpx;
    box-shadow: 0 0 4rpx #b6ff3b;
}
.pen-style-list.data-v-a4b6909a {
    display: flex;
    flex-direction: row;
    gap: 24rpx;
    margin-left: 12rpx;
}
.pen-style-item.data-v-a4b6909a {
    display: flex;
    align-items: center;
    background: #333;
    border: 1rpx solid #444;
    border-radius: 8rpx;
    padding: 8rpx 16rpx;
    font-size: 24rpx;
    color: #bbb;
    cursor: pointer;
    transition: border 0.2s, color 0.2s;
}
.pen-style-item.active.data-v-a4b6909a {
    border: 2rpx solid #b6ff3b;
    color: #8dd800;
    background: #222;
}

/* 模板 */
/* template-bar 样式补全 */
.template-bar.data-v-a4b6909a {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    background: #191919;
    border-radius: 16rpx 16rpx 0 0;
    z-index: 3;
    padding: 24rpx 0 0 0;
    box-shadow: 0 -4rpx 24rpx #000a;
}
.template-tabs.data-v-a4b6909a {
    display: flex;
    flex-direction: row;
    gap: 24rpx;
    padding: 0 24rpx;
    margin-bottom: 16rpx;
}
.template-tab.data-v-a4b6909a {
    color: #bbb;
    font-size: 24rpx;
    padding: 8rpx 16rpx;
    border-radius: 8rpx;
    min-width: 50px;
    text-align: center;
}
.template-tab.active.data-v-a4b6909a {
    color: #b6ff3b;
    background: #222;
}
.template-list.data-v-a4b6909a {
    display: flex;
    flex-direction: row;
    gap: 24rpx;
    padding: 0 24rpx 24rpx 24rpx;
    overflow-x: auto;
    white-space: nowrap;
}
.template-card.data-v-a4b6909a {
    display: inline-block;
    background: #222;
    border-radius: 12rpx;
    width: 320rpx;
    margin-right: 24rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 8rpx #0006;
    vertical-align: top;
}
.template-img.data-v-a4b6909a {
    width: 100%;
    height: 180rpx;
    object-fit: cover;
    display: block;
}
.template-info.data-v-a4b6909a {
    padding: 12rpx 16rpx 8rpx 16rpx;
}
.template-title.data-v-a4b6909a {
    color: #fff;
    font-size: 26rpx;
    margin-bottom: 8rpx;
}
.template-meta.data-v-a4b6909a {
    display: flex;
    align-items: center;
    gap: 8rpx;
}
.template-vip-icon.data-v-a4b6909a {
    width: 28rpx;
    height: 28rpx;
}
.template-view.data-v-a4b6909a {
    color: #bbb;
    font-size: 22rpx;
}

/* 图层面板样式 */
.layer-panel.data-v-a4b6909a {
    position: fixed;
    right: 15rpx;
    top: 200rpx;
    width: 320rpx;
    max-height: 60vh;
    background: #222;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 24rpx #000a;
    z-index: 20;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}
.layer-tabs.data-v-a4b6909a {
    display: flex;
    flex-direction: row;
    border-bottom: 1rpx solid #333;
    background: #191919;
}
.layer-tab.data-v-a4b6909a {
    flex: 1;
    text-align: center;
    color: #bbb;
    font-size: 22rpx;
    padding: 16rpx 0;
    cursor: pointer;
    transition: color 0.2s, background 0.2s;
}
.layer-tab.active.data-v-a4b6909a {
    color: #b6ff3b;
    background: #222;
    border-bottom: 2rpx solid #b6ff3b;
}
.layer-list.data-v-a4b6909a {
    flex: 1;
    overflow-y: auto;
    padding: 12rpx 0;
}
.layer-item.data-v-a4b6909a {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10rpx 16rpx;
    border-bottom: 1rpx solid #333;
    gap: 16rpx;
}
.layer-eye.data-v-a4b6909a {
    width: 32rpx;
    height: 32rpx;
    margin-right: 8rpx;
}
.layer-thumb.data-v-a4b6909a {
    width: 48rpx;
    height: 48rpx;
    background: #333;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}
.layer-img.data-v-a4b6909a {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.layer-text.data-v-a4b6909a {
    font-size: 22rpx;
    color: #fff;
    text-align: center;
    width: 100%;
}
.layer-footer.data-v-a4b6909a {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12rpx 0;
    background: #191919;
    border-top: 1rpx solid #333;
    gap: 8rpx;
}
.layer-footer-icon.data-v-a4b6909a {
    width: 28rpx;
    height: 28rpx;
}
.layer-footer-text.data-v-a4b6909a {
    color: #bbb;
    font-size: 22rpx;
}
.img-border-ops.data-v-a4b6909a {
    position: absolute;
    border: 2px dashed #b6ff3b;
    border-radius: 12rpx;
    box-sizing: border-box;
    pointer-events: none;
}
.border-rect.data-v-a4b6909a {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    border: 2px solid #1e01fc;
    border-radius: 12rpx;
    pointer-events: none;
}
.corner-btn.data-v-a4b6909a {
    position: absolute;
    width: 42rpx;
    height: 42rpx;
    background: #8dd800;
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    font-weight: bold;
    z-index: 2;
    pointer-events: auto;
    box-shadow: 0 2rpx 8rpx #0006;
    cursor: pointer;
    -webkit-user-select: none;
            user-select: none;
}
.corner-del.data-v-a4b6909a {
    left: -16rpx;
    top: -16rpx;
    background: #e53935;
}
.corner-copy.data-v-a4b6909a {
    left: -16rpx;
    bottom: -16rpx;
    background: #8dd800;
}
.corner-rotate.data-v-a4b6909a {
    right: -16rpx;
    top: -16rpx;
    background: #8dd800;
}
.corner-scale.data-v-a4b6909a {
    right: -16rpx;
    bottom: -16rpx;
    background: #8dd800;
}
