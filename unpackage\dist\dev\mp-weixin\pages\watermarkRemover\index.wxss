
.temps.data-v-44d2a7c9 {
    width: 100%;
    height: 100vh;
    background-color: #2c2c2c;
}
.container.data-v-44d2a7c9 {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #2c2c2c;
    position: fixed;
    top: 0px;
    left: 0px;
}

/* 顶部导航栏样式 */
.header.data-v-44d2a7c9 {
    width: 100%;
    height: 55px;
    background-color: #2c2c2c;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;
    box-sizing: border-box;
}
.header-btn.data-v-44d2a7c9 {
    padding: 6px 14px;
    /* border: 1px solid #8bc34a; */
    border-radius: 3px;
    background-color: transparent;
    min-width: 50px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.headerVtn.data-v-44d2a7c9 {
    margin-right: 10px !important;
    width: 30px;
    height: 40rpx;
    border-radius: 10rpx;
    background-color: #4CAF50;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 14px;
    cursor: pointer;
}
.headerVtn image.data-v-44d2a7c9 {
    width: 20px;
    height: 20px;
}
.btn-text.data-v-44d2a7c9 {}
.headerVtn image.data-v-44d2a7c9 {
    width: 20px;
    height: 20px;
}
.btn-text.data-v-44d2a7c9 {
    color: #fff;
    font-size: 13px;
}

/* 主要内容区域样式 */
.main-content.data-v-44d2a7c9 {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}
.canvas-area.data-v-44d2a7c9 {
    background-color: transparent;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #e8e8e8;
    background-image:
        linear-gradient(45deg, #d0d0d0 25%, transparent 25%),
        linear-gradient(-45deg, #d0d0d0 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #d0d0d0 75%),
        linear-gradient(-45deg, transparent 75%, #d0d0d0 75%);
    background-size: 16px 16px;
    background-position: 0 0, 0 8px, 8px -8px, -8px 0px;
}
.canvas-back.data-v-44d2a7c9 {
    background-position: center;
    background-size: 100%;
    background-repeat: no-repeat;
}
.canvas.data-v-44d2a7c9 {
    border: 1px solid #ccc;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    max-width: 100%;
    max-height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}
.canvas img.data-v-44d2a7c9 {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}
.canvas-text.data-v-44d2a7c9 {}
.main-content-left.data-v-44d2a7c9 {
    position: relative;
}
.placeholder.data-v-44d2a7c9 {
    text-align: center;
}
.placeholder-text.data-v-44d2a7c9 {
    color: #999;
    font-size: 16px;
}

/* 底部工具栏样式 */
.toolbar.data-v-44d2a7c9 {
    width: 100%;
    height: 110px;
    background-color: #2c2c2c;
    /* display: flex;
    align-items: center;
    justify-content: space-between; */
    /* padding: 0 15px; */
    box-sizing: border-box;
    position: relative;
}
.toolbar-left.data-v-44d2a7c9 {
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 100%;
    margin-top: 10rpx;
    margin-bottom: 10rpx;
}
.toolbar-center.data-v-44d2a7c9 {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 2;
    justify-content: center;
}
.toolbar-right.data-v-44d2a7c9 {
    display: flex;
    justify-content: flex-end;
    flex: 1;
}
.tool-btn.data-v-44d2a7c9 {
    width: 36px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    background-color: transparent;
}
.main-tool.data-v-44d2a7c9 {
    width: 42px;
    height: 42px;
    background-color: rgba(255, 255, 255, 0.08);
    border-radius: 6px;
}
.tool-icon-box.data-v-44d2a7c9 {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
.tool-icon.data-v-44d2a7c9 {
    color: #fff;
    font-size: 16px;
}
.tool-text.data-v-44d2a7c9 {
    color: #fff;
    font-size: 11px;
}
.tool-text-icon.data-v-44d2a7c9 {
    color: #fff;
    font-size: 14px;
    font-weight: bold;
}
.clear-btn.data-v-44d2a7c9 {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    padding: 0 8px;
    min-width: 40px;
}
.gallery-btn.data-v-44d2a7c9 {
    background-color: #4a4a4a;
    border-radius: 4px;
    padding: 0 12px;
    min-width: 50px;
}
.tool-btn.data-v-44d2a7c9:active {
    background-color: rgba(255, 255, 255, 0.2);
}
.tool-label.data-v-44d2a7c9 {
    color: #fff;
    font-size: 10px;
    margin-top: 2px;
    text-align: center;
}
.main-tool.data-v-44d2a7c9 {
    flex-direction: column;
    height: 50px;
    padding: 4px 2px;
}
.containersd.data-v-44d2a7c9 {
    width: 200px;
    /* height: 150px; */
    background-color: #333;
    border-radius: 10px;
    color: white;
    text-align: center;
    position: absolute;
    right: 0px;
    top: 20%;
}
.header-2.data-v-44d2a7c9 {
    height: 30px;
    line-height: 30px;
    border-bottom: 1px solid #ccc;
}
.title-2.data-v-44d2a7c9 {
    color: #fff;
}
.content-2.data-v-44d2a7c9 {
    width: 100%;
    /* padding: 10px; */
    max-height: 500px;
    overflow: auto;
}
.content-2-1.data-v-44d2a7c9 {
    width: 95%;
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    margin: auto;
    margin-top: 5px;
}
.content-2-1-1.data-v-44d2a7c9 {
    width: 40%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.textIcon-2.data-v-44d2a7c9 {
    width: 25px;
    height: 25px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    color: #000;
}
.icon-1.data-v-44d2a7c9 {
    width: 15px;
    height: 15px;
}
.icon-2.data-v-44d2a7c9 {
    width: 100%;
    height: 100%;
}
.count-2.data-v-44d2a7c9 {
    font-size: 14px;
}
.close-btn-2.data-v-44d2a7c9 {
    font-size: 20px;
    cursor: pointer;
}
.footer-2.data-v-44d2a7c9 {
    height: 40px;
    line-height: 40px;
    border-top: 1px solid #ccc;
}
.footer-text-2.data-v-44d2a7c9 {
    font-size: 14px;
}
