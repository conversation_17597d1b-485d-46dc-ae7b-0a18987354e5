
.containerff.data-v-b411d5cf {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
    position: relative;
}

/* 主要内容区域 */
.main-content.data-v-b411d5cf {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}
.content-area.data-v-b411d5cf {
    width: 100%;
    height: 100%;
    background-color: #fff;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
.placeholder-text.data-v-b411d5cf {
    color: #999;
    font-size: 16px;
}

/* 缩放面板样式 */
.scale-panel.data-v-b411d5cf {
    position: fixed;
    left: 0;
    bottom: 70px;
    background-color: #3c3c3c;
    z-index: 1000;
    /* animation: slideDown 0.3s ease-out; */
    overflow: auto;
    max-height: 56vh;
    width: 100%;
}

/* 
@keyframes slideDown {
    from {
        transform: translateY(-100%);
    }

    to {
        transform: translateY(0);
    }
} */

/* 搜索区域样式 */
.search-section.data-v-b411d5cf {
    background-color: #2c2c2c;
    padding: 15px;
}
.search-bar.data-v-b411d5cf {
    display: flex;
    align-items: center;
    background-color: #3c3c3c;
    border-radius: 20px;
    padding: 8px 15px;
    margin-bottom: 15px;
}
.search-icon.data-v-b411d5cf {
    color: #999;
    font-size: 16px;
    margin-right: 10px;
}
.search-input.data-v-b411d5cf {
    flex: 1;
    background: transparent;
    border: none;
    color: #fff;
    font-size: 14px;
    outline: none;
}
.search-input.data-v-b411d5cf::-webkit-input-placeholder {
    color: #999;
}
.search-input.data-v-b411d5cf::placeholder {
    color: #999;
}

/* 过滤标签样式 */
.filter-tabs.data-v-b411d5cf {
    display: flex;
    align-items: center;
    /* justify-content: center; */
    overflow-x: auto;
    width: 100%;
    height: 40px;
}
.filter-tab.data-v-b411d5cf {
    padding: 6px 12px;
    background-color: #555;
    border-radius: 15px;
    border: 1px solid transparent;
    transition: all 0.2s ease;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.filter-tab.active.data-v-b411d5cf {
    background-color: #4CAF50;
    border-color: #4CAF50;
}
.active.active.data-v-b411d5cf {
    background-color: #4CAF50;
    border-color: #4CAF50;
}
.filter-text.data-v-b411d5cf {
    color: #fff;
    font-size: 12px;
    white-space: nowrap;
}

/* 内容网格样式 */
.content-grid.data-v-b411d5cf {
    width: 100%;
    /* padding: 15px; */
    grid-template-columns: repeat(2, 1fr);
    /* gap: 15px; */
    display: flex;
    align-items: center;
    justify-content: space-around;
    flex-wrap: wrap;
    overflow: auto;
}
.content-box.data-v-b411d5cf {
    width: 100%;
    display: flex;
    /* align-items: center; */
}
.content-left.data-v-b411d5cf {
    width: 20%;
    height: 100%;
    color: #fff;

    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    font-size: 14px;
}
.content-color.data-v-b411d5cf {
    margin-top: 10px;
}
.content-right.data-v-b411d5cf {
    width: 80%;
    height: 100%;
}
.content-grids.data-v-b411d5cf {
    width: 80%;

    height: 220px;
    /* padding: 15px; */
    grid-template-columns: repeat(2, 1fr);
    /* gap: 15px; */
    display: flex;
    align-items: center;
    justify-content: space-around;
    flex-wrap: wrap;
    overflow: auto;
    background-color: none;
}
.content-gridss.data-v-b411d5cf {
    width: 100%;
    height: 220px;
    /* padding: 15px; */
    grid-template-columns: repeat(2, 1fr);
    /* gap: 15px; */
    display: flex;
    align-items: center;
    justify-content: space-around;
    flex-wrap: wrap;

    overflow: auto;
    background-color: none;
}
.content-card.data-v-b411d5cf {
    width: 48%;
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    /* background-color: #fff; */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
    margin-top: 10px;
}
.content-card.data-v-b411d5cf:active {
    transform: scale(0.98);
}
.card-image.data-v-b411d5cf {
    width: 100%;
    height: 120px;
    object-fit: cover;
}
.card-overlay.data-v-b411d5cf {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    padding: 15px 10px 10px;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
}
.card-title.data-v-b411d5cf {
    color: #fff;
    font-size: 13px;
    font-weight: 500;
    flex: 1;
}
.card-stats.data-v-b411d5cf {
    display: flex;
    align-items: center;
    gap: 4px;
}
.stats-icon.data-v-b411d5cf {
    color: #fff;
    font-size: 12px;
}
.stats-text.data-v-b411d5cf {
    color: #fff;
    font-size: 11px;
}

/* 画笔面板样式 */
.brush-panel.data-v-b411d5cf {
    position: fixed;
    bottom: 65px;
    left: 0;
    right: 0;
    background-color: #3c3c3c;
    border-radius: 12px 12px 0 0;
    padding: 10px 20px;
    z-index: 1000;
    animation: slideUp 0.3s ease-out;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
    max-height: 60vh;
    overflow-y: auto;
}
.brush-section.data-v-b411d5cf {
    margin-bottom: 10px;
}
.brush-section.data-v-b411d5cf:last-child {
    margin-bottom: 0;
}
.section-header.data-v-b411d5cf {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}
.section-title.data-v-b411d5cf {
    color: #fff;
    font-size: 16px;
    font-weight: 500;
}
.section-value.data-v-b411d5cf {
    color: #4CAF50;
    font-size: 16px;
    font-weight: bold;
}

/* 滑块样式 */
.slider-container.data-v-b411d5cf {
    padding: 0 5px;
    display: flex;
    align-items: center;
}
.brush-slider.data-v-b411d5cf {
    width: 100%;
    height: 15px;
}

/* 画笔颜色选择样式 */
.brush-color-palette.data-v-b411d5cf {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    margin-top: 15px;
}
.brush-color-item.data-v-b411d5cf {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: 3px solid transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    cursor: pointer;
}
.brush-color-item.active.data-v-b411d5cf {
    border-color: #fff;
    transform: scale(1.1);
}
.color-check.data-v-b411d5cf {
    color: #fff;
    font-size: 13px;
    font-weight: bold;
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
}

/* 笔状选择样式 */
.brush-type-container.data-v-b411d5cf {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}
.brush-type-item.data-v-b411d5cf {
    /* flex: 1; */
    display: flex;
    flex-direction: column;
    align-items: center;
    /* padding: 10px; */
    background-color: #555;
    border-radius: 8px;
    border: 2px solid transparent;
    transition: all 0.2s ease;
    cursor: pointer;
}
.brush-type-item.active.data-v-b411d5cf {
    border-color: #4CAF50;
    background-color: #4CAF50;
}
.brush-type-line.data-v-b411d5cf {
    width: 60px;
    height: 4px;
    background-color: #fff;
    /* margin-bottom: 10px; */
    border-radius: 2px;
}
.solid-line.data-v-b411d5cf {
    background-color: #fff;
}
.dashed-line.data-v-b411d5cf {
    background-image: repeating-linear-gradient(to right,
            #fff 0px,
            #fff 8px,
            transparent 8px,
            transparent 16px);
    background-color: transparent;
}
.brush-type-label.data-v-b411d5cf {
    color: #fff;
    font-size: 14px;
    text-align: center;
}

/* 复制面板样式 */
.copy-panel.data-v-b411d5cf {
    position: fixed;
    left: 0;
    bottom: 70px;
    background-color: #2c2c2c;
    z-index: 1000;
    animation: slideDown 0.3s ease-out;
    overflow-y: auto;
    height: 56vh;
    width: 100%;
}

/* 复制面板搜索区域 */
.copy-search-section.data-v-b411d5cf {
    background-color: #2c2c2c;
    padding: 15px;
    border-bottom: 1px solid #444;
}
.copy-search-bar.data-v-b411d5cf {
    display: flex;
    align-items: center;
    background-color: #3c3c3c;
    border-radius: 20px;
    padding: 8px 15px;
}
.copy-search-icon.data-v-b411d5cf {
    color: #999;
    font-size: 16px;
    margin-right: 10px;
}
.copy-search-input.data-v-b411d5cf {
    flex: 1;
    background: transparent;
    border: none;
    color: #fff;
    font-size: 14px;
    outline: none;
}
.copy-search-input.data-v-b411d5cf::-webkit-input-placeholder {
    color: #999;
}
.copy-search-input.data-v-b411d5cf::placeholder {
    color: #999;
}

/* 复制面板分类标签 */
.copy-category-tabs.data-v-b411d5cf {
    display: flex;
    background-color: #2c2c2c;
    padding: 0 15px 15px;
    gap: 8px;
    flex-wrap: wrap;
}
.copy-category-tab.data-v-b411d5cf {
    padding: 8px 0px;
    background-color: #444;
    border-radius: 20px;
    border: 1px solid transparent;
    transition: all 0.2s ease;
    cursor: pointer;
    margin: 0px 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.copy-category-tab.active.data-v-b411d5cf {
    background-color: #4CAF50;
    border-color: #4CAF50;
}
.copy-category-text.data-v-b411d5cf {
    color: #fff;
    font-size: 13px;
    font-weight: 500;
}

/* 复制面板内容网格 */
.copy-content-grid.data-v-b411d5cf {
    /* padding: 15px; */
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    background-color: #f5f5f5;
    min-height: calc(100vh - 200px);
    width: 100%;
}
.copy-content-item.data-v-b411d5cf {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
    cursor: pointer;
    width: 49%;
    height: 40px;
}
.copy-content-item.data-v-b411d5cf:active {
    transform: scale(0.98);
}
.copy-item-image.data-v-b411d5cf {
    width: 100%;
    height: 100px;
    object-fit: cover;
}
.copy-item-title.data-v-b411d5cf {
    padding: 8px;
    font-size: 12px;
    color: #333;
    text-align: center;
    background-color: #fff;
}

/* 文字面板样式 */
.text-panel.data-v-b411d5cf {
    position: fixed;
    bottom: 80px;
    left: 0;
    right: 0;
    background-color: #3c3c3c;
    border-radius: 12px 12px 0 0;
    padding: 0;
    z-index: 1000;
    /* animation: slideUp 0.3s ease-out; */
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
    max-height: 70vh;
    overflow-y: auto;
}
.panel-mask.data-v-b411d5cf {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2;
}

/* 面板头部 */
.panel-header.data-v-b411d5cf {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #555;
}
.panel-title.data-v-b411d5cf {
    color: #fff;
    font-size: 16px;
    font-weight: 500;
}
.panel-close.data-v-b411d5cf {
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #4CAF50;
    border-radius: 4px;
}
.close-icon.data-v-b411d5cf {
    color: #fff;
    font-size: 16px;
    font-weight: bold;
}

/* 字体选择区域 */
.font-section.data-v-b411d5cf {
    padding: 15px 20px;
    border-bottom: 1px solid #555;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.font-buttons.data-v-b411d5cf {
    display: flex;
    gap: 8px;
    width: 100%;
    height: 50px;
    align-items: center;
    flex-wrap: nowrap;
    overflow: auto;
}
.font-btn.data-v-b411d5cf {
    padding: 8px 12px;
    background-color: #555;
    border-radius: 6px;
    border: 1px solid transparent;
    transition: all 0.2s ease;
    margin: 0px 10px;
}
.font-btn.active.data-v-b411d5cf {
    background-color: #4CAF50;
    border-color: #4CAF50;
}
.font-text.data-v-b411d5cf {
    color: #fff;
    font-size: 13px;
    white-space: nowrap;
}

/* 字体大小调节区域 */
.size-section.data-v-b411d5cf {
    padding: 15px 20px;
    border-bottom: 1px solid #555;
}
.section-label.data-v-b411d5cf {
    color: #fff;
    font-size: 14px;
    margin-bottom: 10px;
    display: block;
}
.size-control.data-v-b411d5cf {
    display: flex;
    align-items: center;
    gap: 15px;
}
.size-slider.data-v-b411d5cf {
    flex: 1;
    height: 15px;
}
.size-value.data-v-b411d5cf {
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    min-width: 35px;
    text-align: right;
}

/* 颜色选择区域 */
.color-section.data-v-b411d5cf {
    padding: 15px 20px;
}
.color-palette.data-v-b411d5cf {
    gap: 12px;
    margin-top: 10px;
    height: 6vh;
    overflow: auto;
    display: flex;
    align-items: center;
    /* border: 1px solid red; */
}
.text-input-area.data-v-b411d5cf {
    height: 25px;
}
.textInput.data-v-b411d5cf {
    width: 90%;
    height: 25px;
    border: 1px solid rgb(221, 221, 221);
    border-radius: 10rpx;
    background-color: #fff;
    color: #000;
}
.fontWeights.data-v-b411d5cf {
    width: 25px;
    height: 25px;
    border-radius: 10rpx;
    text-align: center;
    line-height: 25px;
    font-size: 13px;
    background-color: #9E9E9E;
    color: #fff;
}
.backgroundColors.data-v-b411d5cf {
    background-color: #4CAF50 !important;
}
.color-item.data-v-b411d5cf {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    border: 3px solid transparent;
    transition: all 0.2s ease;
    cursor: pointer;
    margin-left: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.color-pos.data-v-b411d5cf {
    display: flex;
    align-items: center;
}
.color-item.active.data-v-b411d5cf {
    border-color: #fff;
    transform: scale(1.1);
}
.color-item.data-v-b411d5cf:active {
    transform: scale(0.95);
}

/* 应用按钮区域 */
.apply-section.data-v-b411d5cf {
    padding: 20px;
    border-top: 1px solid #555;
}
.apply-btn.data-v-b411d5cf {
    width: 100%;
    height: 44px;
    background-color: #4CAF50;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: background-color 0.2s ease;
}
.apply-btn.data-v-b411d5cf:active {
    background-color: #45a049;
}
.apply-text.data-v-b411d5cf {
    color: #fff;
    font-size: 16px;
    font-weight: 500;
}

/* 顶部标题栏样式 */
.header.data-v-b411d5cf {
    width: 100%;
    height: 100%;
    /* background-color: #fff; */
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #e9ecef;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.header-title.data-v-b411d5cf {
    color: #212529;
    font-size: 16px;
    font-weight: 600;
}

/* 主要内容区域样式 */
.main-content.data-v-b411d5cf {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}
.content-area.data-v-b411d5cf {
    width: 100%;
    height: 100%;
    /* background-color: #fff; */
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}
.tool-status.data-v-b411d5cf {
    width: 100%;
    height: 50px;
    /* background-color: #f8f9fa; */
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    padding: 0 20px;
    box-sizing: border-box;
    transition: background-color 0.3s ease;
}
.status-text.data-v-b411d5cf {
    color: #495057;
    font-size: 14px;
    font-weight: 500;
    transition: color 0.3s ease;
}
.work-area.data-v-b411d5cf {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}
.placeholder.data-v-b411d5cf {
    text-align: center;
    max-width: 300px;
}
.placeholder-text.data-v-b411d5cf {
    color: #6c757d;
    font-size: 15px;
    line-height: 1.5;
}

/* 底部工具栏样式 */
.toolbars.data-v-b411d5cf {
    width: 100%;
    height: 80px;
    background-color: #1a1a1a;
    display: flex;
    position: relative;
    z-index: 100;
    align-items: center;
    justify-content: space-evenly;
    padding: 8px 0px;
    box-sizing: border-box;
    /* border-top: 1px solid #333; */
}
.edits.data-v-b411d5cf {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
}
.tool-btn.data-v-b411d5cf {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 54px;
    border-radius: 4px;
    background-color: transparent;
    transition: all 0.2s ease;
    position: relative;
}
.tool-icon-box.data-v-b411d5cf {
    width: 22px;
    height: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 3px;
}
.tool-icon.data-v-b411d5cf {
    color: #ffffff;
    font-size: 15px;
    opacity: 0.9;
}
.tool-text-icon.data-v-b411d5cf {
    color: #ffffff;
    font-size: 13px;
    font-weight: 500;
    opacity: 0.9;
}
.tool-label.data-v-b411d5cf {
    color: #ffffff;
    font-size: 9px;
    text-align: center;
    line-height: 1;
    opacity: 0.8;
    font-weight: 400;
}
.tool-btn.data-v-b411d5cf:active {
    background-color: rgba(255, 255, 255, 0.1);
    transform: scale(0.95);
}

/* 选中状态 */
.tool-btn.active.data-v-b411d5cf {
    background-color: rgba(255, 255, 255, 0.08);
}
.tool-btn.active .tool-icon.data-v-b411d5cf,
.tool-btn.active .tool-text-icon.data-v-b411d5cf {
    opacity: 1;
    color: #4CAF50;
}
.tool-btn.active .tool-label.data-v-b411d5cf {
    opacity: 1;
    color: #4CAF50;
}

/* 响应式设计 */
@media screen and (max-width: 480px) {
.toolbar.data-v-b411d5cf {
        padding: 6px 10px;
}
.tool-btn.data-v-b411d5cf {
        width: 44px;
        height: 50px;
}
.tool-icon.data-v-b411d5cf {
        font-size: 14px;
}
.tool-text-icon.data-v-b411d5cf {
        font-size: 12px;
}
.tool-label.data-v-b411d5cf {
        font-size: 8px;
}
}
@media screen and (min-width: 768px) {
.toolbar.data-v-b411d5cf {
        height: 85px;
        padding: 10px 30px;
}
.tool-btn.data-v-b411d5cf {
        width: 55px;
        height: 65px;
}
.tool-icon.data-v-b411d5cf {
        font-size: 18px;
}
.tool-text-icon.data-v-b411d5cf {
        font-size: 15px;
}
.tool-label.data-v-b411d5cf {
        font-size: 11px;
}
}

/* 文字面板响应式设计 */
@media screen and (max-width: 480px) {
.text-panel.data-v-b411d5cf {
        bottom: 70px;
}
.font-buttons.data-v-b411d5cf {
        gap: 6px;
}
.font-btn.data-v-b411d5cf {
        padding: 6px 10px;
}
.font-text.data-v-b411d5cf {
        font-size: 12px;
}
.color-item.data-v-b411d5cf {
        width: 18px;
        height: 18px;
}
.color-palette.data-v-b411d5cf {
        gap: 10px;
}

    /* 缩放面板响应式 */
.content-grid.data-v-b411d5cf {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        padding: 12px;
}
.card-image.data-v-b411d5cf {
        height: 100px;
}
.filter-tabs.data-v-b411d5cf {
        gap: 6px;
}
.filter-tab.data-v-b411d5cf {
        padding: 5px 10px;
}
.filter-text.data-v-b411d5cf {
        font-size: 11px;
}

    /* 画笔面板小屏幕优化 */
.brush-color-palette.data-v-b411d5cf {
        gap: 10px;
}
.brush-color-item.data-v-b411d5cf {
        width: 32px;
        height: 32px;
}
.brush-type-container.data-v-b411d5cf {
        gap: 15px;
}
.brush-type-item.data-v-b411d5cf {
        padding: 8px 5px;
}
.brush-type-line.data-v-b411d5cf {
        width: 50px;
        height: 3px;
}

    /* 复制面板小屏幕优化 */
.copy-content-grid.data-v-b411d5cf {
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
        padding: 10px;
}
.copy-item-image.data-v-b411d5cf {
        height: 80px;
}
.copy-item-title.data-v-b411d5cf {
        font-size: 11px;
        padding: 6px;
}
.copy-category-tabs.data-v-b411d5cf {
        gap: 6px;
        padding: 0 10px 10px;
}
.copy-category-tab.data-v-b411d5cf {
        padding: 6px 12px;
        font-size: 12px;
}
}
@media screen and (min-width: 768px) {
.text-panel.data-v-b411d5cf {
        left: 50%;
        right: auto;
        transform: translateX(-50%);
        width: 400px;
        border-radius: 12px;
        bottom: 90px;
}

    /* 缩放面板大屏幕优化 */
.content-grid.data-v-b411d5cf {
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
        padding: 20px;
}
.card-image.data-v-b411d5cf {
        height: 140px;
}
.search-section.data-v-b411d5cf {
        padding: 20px;
}

    /* 画笔面板大屏幕优化 */
.brush-panel.data-v-b411d5cf {
        left: 50%;
        right: auto;
        transform: translateX(-50%);
        width: 400px;
        border-radius: 12px;
        bottom: 90px;
}
.brush-color-palette.data-v-b411d5cf {
        gap: 15px;
}
.brush-color-item.data-v-b411d5cf {
        width: 40px;
        height: 40px;
}
.brush-type-container.data-v-b411d5cf {
        gap: 25px;
}
.brush-type-item.data-v-b411d5cf {
        padding: 18px;
}
.brush-type-line.data-v-b411d5cf {
        width: 70px;
        height: 5px;
}

    /* 复制面板大屏幕优化 */
.copy-content-grid.data-v-b411d5cf {
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
        padding: 20px;
}
.copy-item-image.data-v-b411d5cf {
        height: 120px;
}
.copy-item-title.data-v-b411d5cf {
        font-size: 13px;
        padding: 10px;
}
.copy-category-tabs.data-v-b411d5cf {
        gap: 12px;
        padding: 0 20px 20px;
}
.copy-category-tab.data-v-b411d5cf {
        padding: 10px 20px;
        font-size: 14px;
}
}
