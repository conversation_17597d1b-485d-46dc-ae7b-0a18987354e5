"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  props: {
    dataarray: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      // 记录元素位置
      position: {
        x: 50,
        y: 50
      },
      // 记录触摸起点
      startPoint: {
        x: 0,
        y: 0
      },
      // 元素初始位置
      initialPosition: {
        x: 0,
        y: 0
      },
      // 容器和元素尺寸（使用固定值，适合小程序）
      dimensions: {
        outerWidth: 375,
        // 600rpx / 2
        outerHeight: 300,
        // 600rpx / 2
        // 移动块尺寸
        contentWidth: 100,
        // 200rpx / 2
        contentHeight: 50
        // 100rpx / 2
      },
      // 是否正在拖动
      isDragging: false,
      isRotate: 10,
      dataType: [],
      record: [],
      dataBack: {},
      editLine: 0,
      del: "https://bpic.588ku.com/element_origin_min_pic/19/04/09/9ce46a196fe52024a687e6aed9ab40a5.jpg",
      jia: "https://pic.616pic.com/ys_img/00/04/79/ISEtXRcNdo.jpg",
      rotate: "https://ts3.tc.mm.bing.net/th/id/OIP-C.CYvN0JBCeEE4pu5UQpx_FQHaHa?r=0&rs=1&pid=ImgDetMain&o=7&rm=3",
      suof: "https://ts1.tc.mm.bing.net/th/id/R-C.********************************?rik=y3JH%2bd3VUY9kpA&riu=http%3a%2f%2fbpic.588ku.com%2felement_pic%2f00%2f94%2f84%2f0156f2d0c0bc6b8.jpg&ehk=U1IKwhy8Bi5%2bXN1ptz4T71vgiaNDTB7tCz54ZrMVGSs%3d&risl=&pid=ImgRaw&r=0"
    };
  },
  mounted() {
    console.log(this.dataarray, "移动组件已加载");
    this.dataType = this.dataarray;
    common_vendor.index.$on("drawLines", (e) => {
      console.log(e, "fff");
      this.dataType.push({
        ids: this.dataType.length + 1,
        ...e
      });
    });
    common_vendor.index.$on("edit", (e) => {
      if (e) {
        this.editLine = e.id;
        console.log(e.id, "id");
      }
    });
    common_vendor.index.$on("selectScaleItem", (e) => {
      console.log(e, "aaa");
      this.dataType.push({
        ids: this.dataType.length + 1,
        ...e
      });
    });
  },
  methods: {
    handleDotStart(e, it, corner) {
      if (!e.touches || e.touches.length === 0)
        return;
      const img = this.findFiles(it);
      if (!img)
        return;
      const touch = e.touches[0];
      const x = touch.clientX || touch.pageX;
      const y = touch.clientY || touch.pageY;
      img._cornerDrag = {
        startX: x,
        startY: y,
        startWidth: img.w,
        startHeight: img.h,
        startPosX: img.x,
        startPosY: img.y,
        corner,
        // 记录四个角的初始位置（重要：基于当前实际位置）
        topLeft: { x: img.x, y: img.y },
        topRight: { x: img.x + img.w, y: img.y },
        bottomLeft: { x: img.x, y: img.y + img.h },
        bottomRight: { x: img.x + img.w, y: img.y + img.h }
      };
      e.stopPropagation();
      e.preventDefault();
    },
    handledot(e, it, corner) {
      if (!e.touches || e.touches.length === 0)
        return;
      const img = this.findFiles(it);
      if (!img || !img._cornerDrag)
        return;
      const touch = e.touches[0];
      const x = touch.clientX || touch.pageX;
      const y = touch.clientY || touch.pageY;
      const deltaX = x - img._cornerDrag.startX;
      const deltaY = y - img._cornerDrag.startY;
      const minSize = 20;
      const { topLeft, topRight, bottomLeft, bottomRight } = img._cornerDrag;
      switch (corner) {
        case "top-left":
          {
            const anchorX = bottomRight.x;
            const anchorY = bottomRight.y;
            const newTopLeftX = topLeft.x + deltaX;
            const newTopLeftY = topLeft.y + deltaY;
            let newWidth = anchorX - newTopLeftX;
            let newHeight = anchorY - newTopLeftY;
            if (newWidth < minSize) {
              newWidth = minSize;
              img.x = anchorX - newWidth;
            } else {
              img.x = newTopLeftX;
            }
            if (newHeight < minSize) {
              newHeight = minSize;
              img.y = anchorY - newHeight;
            } else {
              img.y = newTopLeftY;
            }
            img.w = newWidth;
            img.h = newHeight;
          }
          break;
        case "bottom-left":
          {
            const anchorX = topRight.x;
            const anchorY = topRight.y;
            const newBottomLeftX = bottomLeft.x + deltaX;
            const newBottomLeftY = bottomLeft.y + deltaY;
            let newWidth = anchorX - newBottomLeftX;
            let newHeight = newBottomLeftY - anchorY;
            if (newWidth < minSize) {
              newWidth = minSize;
              img.x = anchorX - newWidth;
            } else {
              img.x = newBottomLeftX;
            }
            if (newHeight < minSize) {
              newHeight = minSize;
            } else {
              img.y = anchorY;
            }
            img.w = newWidth;
            img.h = newHeight;
          }
          break;
        case "top-right":
          {
            const anchorX = bottomLeft.x;
            const anchorY = bottomLeft.y;
            const newTopRightX = topRight.x + deltaX;
            const newTopRightY = topRight.y + deltaY;
            let newWidth = newTopRightX - anchorX;
            let newHeight = anchorY - newTopRightY;
            if (newWidth < minSize) {
              newWidth = minSize;
            }
            if (newHeight < minSize) {
              newHeight = minSize;
              img.y = anchorY - newHeight;
            } else {
              img.y = newTopRightY;
            }
            img.x = anchorX;
            img.w = newWidth;
            img.h = newHeight;
          }
          break;
        case "bottom-right":
          {
            const anchorX = topLeft.x;
            const anchorY = topLeft.y;
            const newBottomRightX = bottomRight.x + deltaX;
            const newBottomRightY = bottomRight.y + deltaY;
            let newWidth = newBottomRightX - anchorX;
            let newHeight = newBottomRightY - anchorY;
            newWidth = Math.max(minSize, newWidth);
            newHeight = Math.max(minSize, newHeight);
            img.x = anchorX;
            img.y = anchorY;
            img.w = newWidth;
            img.h = newHeight;
          }
          break;
      }
      if (img.x < 0) {
        img.w += img.x;
        img.x = 0;
      }
      if (img.y < 0) {
        img.h += img.y;
        img.y = 0;
      }
      if (img.x + img.w > this.dimensions.outerWidth) {
        img.w = this.dimensions.outerWidth - img.x;
      }
      if (img.y + img.h > this.dimensions.outerHeight) {
        img.h = this.dimensions.outerHeight - img.y;
      }
      if (img.type === "text") {
        const sizeRatio = Math.min(
          img.w / img._cornerDrag.startWidth,
          img.h / img._cornerDrag.startHeight
        );
        img.size = Math.max(12, (img._cornerDrag.startSize || img.size) * sizeRatio);
      }
      this.$forceUpdate();
      e.stopPropagation();
      e.preventDefault();
    },
    // 角落拖动开始
    handleCornerStart(e, it, corner) {
      if (!e.touches || e.touches.length === 0)
        return;
      const img = this.findFiles(it);
      if (!img)
        return;
      const touch = e.touches[0];
      const x = touch.clientX || touch.pageX;
      const y = touch.clientY || touch.pageY;
      img._cropDrag = {
        startX: x,
        startY: y,
        corner,
        // 记录原始图片尺寸（如果没有设置过）
        originalWidth: img.originalWidth || img.w,
        originalHeight: img.originalHeight || img.h,
        // 记录当前裁剪区域
        cropX: img.cropX || 0,
        cropY: img.cropY || 0,
        cropWidth: img.cropWidth || img.w,
        cropHeight: img.cropHeight || img.h,
        // 记录当前显示位置和尺寸
        startPosX: img.x,
        startPosY: img.y,
        startWidth: img.w,
        startHeight: img.h
      };
      if (!img.originalWidth) {
        img.originalWidth = img.w;
        img.originalHeight = img.h;
      }
      e.stopPropagation();
      e.preventDefault();
    },
    // 角落拖动结束
    handleCornerEnd(e, it) {
      const img = this.findFiles(it);
      if (!img || !img._cropDrag)
        return;
      delete img._cropDrag;
      delete img._lastTouch;
      e.stopPropagation();
      e.preventDefault();
    },
    // 边框拖动调整图片显示范围（裁剪区域），图片大小不变做裁剪
    handleTouchs(e, it, direction) {
      if (!e.touches || e.touches.length === 0)
        return;
      const img = this.findFiles(it);
      if (!img)
        return;
      const touch = e.touches[0];
      const x = touch.clientX || touch.pageX;
      const y = touch.clientY || touch.pageY;
      if (["top-left", "top-right", "bottom-left", "bottom-right"].includes(direction)) {
        if (!img._cropDrag)
          return;
        const deltaX = x - img._cropDrag.startX;
        const deltaY = y - img._cropDrag.startY;
        const maxWidth = img._cropDrag.originalWidth;
        const maxHeight = img._cropDrag.originalHeight;
        const minSize = 20;
        let newX = img._cropDrag.startPosX;
        let newY = img._cropDrag.startPosY;
        let newWidth = img._cropDrag.startWidth;
        let newHeight = img._cropDrag.startHeight;
        switch (direction) {
          case "top-left":
            newX = img._cropDrag.startPosX + deltaX;
            newY = img._cropDrag.startPosY + deltaY;
            newWidth = img._cropDrag.startWidth - deltaX;
            newHeight = img._cropDrag.startHeight - deltaY;
            break;
          case "top-right":
            newY = img._cropDrag.startPosY + deltaY;
            newWidth = img._cropDrag.startWidth + deltaX;
            newHeight = img._cropDrag.startHeight - deltaY;
            break;
          case "bottom-left":
            newX = img._cropDrag.startPosX + deltaX;
            newWidth = img._cropDrag.startWidth - deltaX;
            newHeight = img._cropDrag.startHeight + deltaY;
            break;
          case "bottom-right":
            newWidth = img._cropDrag.startWidth + deltaX;
            newHeight = img._cropDrag.startHeight + deltaY;
            break;
        }
        if (newWidth < minSize) {
          if (direction.includes("left")) {
            newX = img._cropDrag.startPosX + img._cropDrag.startWidth - minSize;
          }
          newWidth = minSize;
        }
        if (newHeight < minSize) {
          if (direction.includes("top")) {
            newY = img._cropDrag.startPosY + img._cropDrag.startHeight - minSize;
          }
          newHeight = minSize;
        }
        if (newWidth > maxWidth) {
          if (direction.includes("left")) {
            newX = img._cropDrag.startPosX + img._cropDrag.startWidth - maxWidth;
          }
          newWidth = maxWidth;
        }
        if (newHeight > maxHeight) {
          if (direction.includes("top")) {
            newY = img._cropDrag.startPosY + img._cropDrag.startHeight - maxHeight;
          }
          newHeight = maxHeight;
        }
        if (newX < 0) {
          newWidth += newX;
          newX = 0;
        }
        if (newY < 0) {
          newHeight += newY;
          newY = 0;
        }
        if (newX + newWidth > this.dimensions.outerWidth) {
          newWidth = this.dimensions.outerWidth - newX;
        }
        if (newY + newHeight > this.dimensions.outerHeight) {
          newHeight = this.dimensions.outerHeight - newY;
        }
        img.x = newX;
        img.y = newY;
        img.w = newWidth;
        img.h = newHeight;
        this.$forceUpdate();
        return;
      }
      if (!img._lastTouch) {
        img._lastTouch = { x, y };
        return;
      }
      const dx = x - img._lastTouch.x;
      const dy = y - img._lastTouch.y;
      let newW = img.w + dx;
      let newH = img.h + dy;
      newW = Math.max(20, Math.min(newW, this.dimensions.outerWidth));
      newH = Math.max(20, Math.min(newH, this.dimensions.outerHeight));
      img.w = newW;
      img.h = newH;
      img._lastTouch = { x, y };
      this.$forceUpdate();
    },
    // 触摸开始
    handleTouchStart(e, item) {
      if (!e.touches || e.touches.length === 0)
        return;
      this.isDragging = true;
      this.startPoint = {
        x: e.touches[0].pageX || e.touches[0].clientX,
        y: e.touches[0].pageY || e.touches[0].clientY
      };
      this.initialPosition = {
        x: item.x,
        y: item.y
      };
      this.disp(item);
    },
    // 触摸移动
    handleTouchMove(e, item) {
      if (!e.touches || e.touches.length === 0)
        return;
      e.preventDefault();
      e.stopPropagation();
      const currentX = e.touches[0].pageX || e.touches[0].clientX;
      const currentY = e.touches[0].pageY || e.touches[0].clientY;
      const moveX = currentX - this.startPoint.x;
      const moveY = currentY - this.startPoint.y;
      let newX = this.initialPosition.x + moveX;
      let newY = this.initialPosition.y + moveY;
      const maxX = this.dimensions.outerWidth - item.w;
      const maxY = this.dimensions.outerHeight - item.h;
      newX = Math.max(0, Math.min(newX, maxX));
      newY = Math.max(0, Math.min(newY, maxY));
      this.position = {
        x: newX,
        y: newY
      };
      const img = this.findFiles(item);
      img.x = newX;
      img.y = newY;
      console.log("移动到:", this.dataType);
    },
    // 触摸结束
    handleTouchEnd() {
      this.isDragging = false;
      console.log("触摸结束，最终位置:", this.position);
      this.dataType.forEach((item) => {
        if (item._cornerDrag) {
          delete item._cornerDrag;
        }
        if (item._lastTouch) {
          delete item._lastTouch;
        }
      });
      console.log("触摸结束，最终位置:", this.position);
    },
    // 缩放
    scaleItem(e, it) {
      console.log(e, it, 123);
      if (e && e.touches && e.touches.length === 1) {
        var img = this.findFiles(it);
        const touch = e.touches[0];
        const x = touch.clientX || touch.pageX;
        const y = touch.clientY || touch.pageY;
        if (!img._lastTouch) {
          img._lastTouch = { x, y };
          return;
        }
        const dx = x - img._lastTouch.x;
        const dy = y - img._lastTouch.y;
        let newW = img.w + dx;
        let newH = img.h + dy;
        newW = Math.max(20, Math.min(newW, this.dimensions.outerWidth));
        newH = Math.max(20, Math.min(newH, this.dimensions.outerHeight));
        img.w = newW;
        img.h = newH;
        img._lastTouch = { x, y };
        this.$forceUpdate();
      }
    },
    // 复制一份
    copyItem(e) {
      var img = this.findFiles(e);
      console.log(e, "mmm");
      img.display = false;
      this.dataType.push({
        ...img,
        ids: this.dataType.length + 1,
        name: img.name + (this.dataType.length + 1),
        sox: img.x + 20,
        soy: img.y + 20,
        x: img.x + 20,
        y: img.y + 20,
        display: true
      });
    },
    // 旋转 - 手指拖动旋转
    rotateItem(e, it) {
      const img = this.findFiles(it);
      if (!img || !e.touches || !e.touches.length)
        return;
      const touch = e.touches[0];
      const x = touch.pageX || touch.clientX;
      const y = touch.pageY || touch.clientY;
      const canvasTop = 120;
      const canvasLeft = 20;
      const centerX = canvasLeft + img.x + img.w / 2;
      const centerY = canvasTop + img.y + img.h / 2;
      const deltaX = x - centerX;
      const deltaY = y - centerY;
      const currentAngle = Math.atan2(deltaY, deltaX);
      if (img._startAngle === void 0) {
        img._startAngle = currentAngle;
        img._initialRotate = img.rotate || 0;
        return;
      }
      let angleDiff = currentAngle - img._startAngle;
      if (angleDiff > Math.PI) {
        angleDiff -= 2 * Math.PI;
      } else if (angleDiff < -Math.PI) {
        angleDiff += 2 * Math.PI;
      }
      img.rotate = img._initialRotate + angleDiff;
    },
    // 旋转结束 - 清理临时变量
    rotateEnd(it) {
      const img = this.findFiles(it);
      if (!img)
        return;
      delete img._startAngle;
      delete img._initialRotate;
    },
    recordClick(e) {
      console.log(typeof e);
      if (typeof e == "object") {
        this.record.push(this.dataType.filter((item) => item.ids == e.ids)[0]);
        this.dataType = this.dataType.filter((item) => item.ids !== e.ids);
      } else {
        this.record.push(...this.dataType);
        this.dataType = [];
      }
    },
    disp(e) {
      this.dataType.forEach((element) => {
        if (element.ids == e.ids) {
          element.display = true;
          this.selectedItem = element;
        } else {
          element.display = false;
        }
      });
    },
    // 数据ids匹配
    findFiles(e) {
      return this.dataType.find((f) => f.ids == e.ids);
    },
    // 删除
    deleteItem(it) {
      this.recordClick(it);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.dataBack
  }, $data.dataBack ? {
    b: $data.dataBack.url,
    c: $data.dimensions.outerWidth + "px",
    d: $data.dimensions.outerHeight + "px"
  } : {}, {
    e: common_vendor.f($data.dataType, (it, index, i0) => {
      return common_vendor.e({
        a: it.type != "text"
      }, it.type != "text" ? {
        b: Math.abs(it.w) + "px",
        c: Math.abs(it.h) + "px",
        d: (it.h < 0 ? "scaleY(-1)" : "") + " " + (it.w < 0 ? "scaleX(-1)" : ""),
        e: it.url
      } : {
        f: common_vendor.t(it.text),
        g: it.w,
        h: it.h,
        i: it.size + "px",
        j: it.color,
        k: it.textTpey
      }, {
        l: it.display && !$data.editLine
      }, it.display && !$data.editLine ? {
        m: common_vendor.o((e) => _ctx.cornerTop(e, it), index),
        n: common_vendor.o((e) => _ctx.cornerRight(e, it), index),
        o: common_vendor.o((e) => _ctx.cornerBtm(e, it), index),
        p: common_vendor.o((e) => _ctx.cornerLeft(e, it), index),
        q: $data.del,
        r: common_vendor.o(($event) => $options.deleteItem(it), index),
        s: $data.jia,
        t: common_vendor.o(($event) => $options.copyItem(it), index),
        v: $data.rotate,
        w: common_vendor.o((e) => $options.rotateItem(e, it), index),
        x: common_vendor.o((e) => $options.rotateEnd(it), index),
        y: $data.suof,
        z: common_vendor.o((e) => $options.scaleItem(e, it), index)
      } : {}, {
        A: it.display && $data.editLine == 1
      }, it.display && $data.editLine == 1 ? {
        B: common_vendor.o((e) => $options.handleTouchs(e, it, "top"), index),
        C: common_vendor.o((e) => $options.handleTouchs(e, it, "bottom"), index),
        D: common_vendor.o((e) => $options.handleTouchs(e, it, "left"), index),
        E: common_vendor.o((e) => $options.handleTouchs(e, it, "right"), index),
        F: common_vendor.o((e) => $options.handleCornerStart(e, it, "top-left"), index),
        G: common_vendor.o((e) => $options.handleTouchs(e, it, "top-left"), index),
        H: common_vendor.o((e) => $options.handleCornerEnd(e, it), index),
        I: common_vendor.o((e) => $options.handleCornerStart(e, it, "top-right"), index),
        J: common_vendor.o((e) => $options.handleTouchs(e, it, "top-right"), index),
        K: common_vendor.o((e) => $options.handleCornerEnd(e, it), index),
        L: common_vendor.o((e) => $options.handleCornerStart(e, it, "bottom-left"), index),
        M: common_vendor.o((e) => $options.handleTouchs(e, it, "bottom-left"), index),
        N: common_vendor.o((e) => $options.handleCornerEnd(e, it), index),
        O: common_vendor.o((e) => $options.handleCornerStart(e, it, "bottom-right"), index),
        P: common_vendor.o((e) => $options.handleTouchs(e, it, "bottom-right"), index),
        Q: common_vendor.o((e) => $options.handleCornerEnd(e, it), index)
      } : {}, {
        R: it.display && $data.editLine == 2
      }, it.display && $data.editLine == 2 ? {
        S: common_vendor.o((e) => $options.handleDotStart(e, it, "top-left"), index),
        T: common_vendor.o((e) => $options.handledot(e, it, "top-left"), index),
        U: common_vendor.o((e) => _ctx.handleDotEnd(e, it), index),
        V: common_vendor.o((e) => $options.handleDotStart(e, it, "top-right"), index),
        W: common_vendor.o((e) => $options.handledot(e, it, "top-right"), index),
        X: common_vendor.o((e) => _ctx.handleDotEnd(e, it), index),
        Y: common_vendor.o((e) => $options.handleDotStart(e, it, "bottom-left"), index),
        Z: common_vendor.o((e) => $options.handledot(e, it, "bottom-left"), index),
        aa: common_vendor.o((e) => _ctx.handleDotEnd(e, it), index),
        ab: common_vendor.o((e) => $options.handleDotStart(e, it, "bottom-right"), index),
        ac: common_vendor.o((e) => $options.handledot(e, it, "bottom-right"), index),
        ad: common_vendor.o((e) => _ctx.handleDotEnd(e, it), index)
      } : {}, {
        ae: it.w + 17 + "px",
        af: it.h + 17 + "px",
        ag: it.display ? "2px dashed rgb(0, 0, 0)" : "none",
        ah: it.Weights ? "900" : "none",
        ai: index,
        aj: common_vendor.o((e) => $options.handleTouchStart(e, it), index),
        ak: common_vendor.o((e) => $options.handleTouchMove(e, it), index),
        al: common_vendor.o((...args) => $options.handleTouchEnd && $options.handleTouchEnd(...args), index),
        am: `translate(${it.x}px, ${it.y}px) scale(${it.display ? 1.05 : 1}) rotate(${it.rotate}rad)`,
        an: it.w + "px",
        ao: it.h + "px"
      });
    }),
    f: common_vendor.n(!$data.dataBack.url ? "outer-box2" : ""),
    g: $data.dimensions.outerWidth + "px",
    h: $data.dimensions.outerHeight + "px"
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-e6d83125"]]);
wx.createComponent(Component);
