"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const tables = () => "./componets/table.js";
const canvass = () => "./componets/canvas.js";
const drawLines = () => "./componets/drawLines.js";
const movableSrea = () => "./componets/movableSrea.js";
const _sfc_main = {
  props: {
    canvasStyle: {
      type: Object,
      default: {
        width: "375",
        // 单位是px ，rpx * 2 就行
        height: "300"
        // 单位是px ，rpx * 2 就行
      }
    },
    imageData: {
      type: Array,
      default: [
        // {
        //     ids: 0,
        //     name: '图片',
        //     x: 10,
        //     y: 10,
        //     w: null,
        //     h: null,
        //     type: 'background',
        //     size: 0,
        //     url: 'https://bpic.588ku.com/element_origin_min_pic/23/07/11/d32dabe266d10da8b21bd640a2e9b611.jpg!r650',
        //     text: '',
        //     rotate: 10,
        //     display: true,
        //     color: '',
        //     closes: true,
        // },
        {
          ids: 2,
          name: "图片",
          x: 50,
          y: 50,
          w: 150,
          h: 150,
          type: "",
          size: 0,
          url: "https://bpic.588ku.com/element_origin_min_pic/23/07/11/d32dabe266d10da8b21bd640a2e9b611.jpg!r650",
          text: "",
          rotate: 0,
          display: true,
          color: "",
          closes: true
        }
        // {
        //     ids: 1,
        //     name: '文字',
        //     x: 0,
        //     y: 0,
        //     w: 100,
        //     h: 40,
        //     type: 'text',
        //     size: 18,
        //     url: '',
        //     text: '哈哈哈哈',
        //     rotate: 10,
        //     display: false,
        //     color: '#8BC34A',
        //     closes: true,
        //     textTpey: '"KaiTi", serif'
        // }
      ]
    },
    returnType: {
      type: String,
      default: "png"
      // 'png' 返回图片, 'all' 图片和数据, 'data' 数据  uni.$on('imageData',(e) => {})
    }
  },
  components: {
    tables,
    canvass,
    drawLines,
    movableSrea
  },
  data() {
    return {
      containersd: false,
      currentTool: "brush",
      // imageData: null,
      data: [],
      display: true,
      rotateTimer: null,
      selectedItem: null,
      // 当前选中的元素
      record: [],
      // 撤销记录
      Timeout: null,
      TimeoutNum: 300,
      dataBack: {},
      info: {}
      // 画布信息
    };
  },
  watch: {
    // data: {
    //     handler(newVal, oldVal) {
    //         clearTimeout(this.Timeout)
    //         this.Timeout = setTimeout(() => {
    //             this.data.forEach((f) => {
    //                 f.sox = f.x
    //                 f.soy = f.y
    //             })
    //         }, this.TimeoutNum)
    //     },
    //     deep: true
    // }
  },
  mounted() {
    this.init();
    this.mainContentLeft();
  },
  methods: {
    mainContentLeft() {
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".main-content-left").boundingClientRect((data) => {
        if (data) {
          this.info = data;
          this.$refs.drawLiness.info = this.info;
          console.log("标签信息：", data);
        } else {
          console.log("未找到目标标签");
        }
      }).exec();
    },
    init() {
      this.dataBack = this.imageData.find((e) => e.type == "background") || {};
      this.data = this.imageData.filter((e) => e.type !== "background");
      this.$refs.movableSreas.dataBack = this.dataBack || null;
      this.$refs.movableSreas.dataType = this.data;
      this.$refs.movableSreas.dimensions.outerWidth = this.canvasStyle.width;
      this.$refs.movableSreas.dimensions.outerHeight = this.canvasStyle.height;
    },
    goBack() {
      common_vendor.index.navigateBack();
    },
    saveImage() {
      const data = [this.$refs.movableSreas.dataBack, ...this.$refs.movableSreas.dataType];
      console.log("data", data);
      if (this.returnType == "data") {
        common_vendor.index.$emit("imageData", data);
      } else if (this.returnType == "png") {
        common_vendor.index.$emit("draws", data);
      } else {
        common_vendor.index.$emit("imageData", data);
        common_vendor.index.$emit("draws", data);
      }
    },
    undo() {
      console.log("重做操作");
      if (this.$refs.movableSreas.dataType.length > 0) {
        this.$refs.movableSreas.record.push(this.$refs.movableSreas.dataType[this.$refs.movableSreas.dataType.length - 1]);
        this.$refs.movableSreas.dataType.pop();
      } else {
        common_vendor.index.showToast({
          title: "没有可重做的操作",
          icon: "none",
          duration: 1500
        });
      }
    },
    redo() {
      console.log("撤销操作");
      if (this.$refs.movableSreas.record.length > 0) {
        this.$refs.movableSreas.dataType.push(this.$refs.movableSreas.record[this.$refs.movableSreas.record.length - 1]);
        this.$refs.movableSreas.record.pop();
      } else {
        common_vendor.index.showToast({
          title: "没有可撤销的操作",
          icon: "none",
          duration: 1500
        });
      }
    },
    swapWithPrevious(arr, condition) {
      const targetIndex = arr.findIndex(condition);
      if (targetIndex > 0) {
        const prevIndex = targetIndex - 1;
        [arr[prevIndex], arr[targetIndex]] = [arr[targetIndex], arr[prevIndex]];
      }
      return arr;
    },
    countClick(e) {
      const i = this.swapWithPrevious(this.$refs.movableSreas.dataType, (item) => item.ids === e.ids);
      this.$refs.movableSreas.dataType = [...i];
      this.data = this.$refs.movableSreas.dataType;
    },
    clear() {
      common_vendor.index.showModal({
        title: "确认",
        content: "确定要清空画布吗？",
        success: (res) => {
          if (res.confirm) {
            this.recordClick();
          }
        }
      });
    },
    recordClick(e) {
      console.log(typeof e);
      if (typeof e == "object") {
        this.$refs.movableSreas.record.push(this.$refs.movableSreas.dataType.filter((item) => item.ids == e.ids)[0]);
        this.$refs.movableSreas.dataType = this.$refs.movableSreas.dataType.filter((item) => item.ids !== e.ids);
      } else {
        this.$refs.movableSreas.record.push(...this.$refs.movableSreas.dataType);
        this.$refs.movableSreas.dataType = [];
      }
      this.data = this.$refs.movableSreas.dataType;
    },
    showImagePicker() {
      common_vendor.index.chooseImage({
        count: 1,
        success: (res) => {
        }
      });
    }
    // onClick(e) {
    //     this.disp(e)
    // },
  }
};
if (!Array) {
  const _component_movableSrea = common_vendor.resolveComponent("movableSrea");
  const _component_drawLines = common_vendor.resolveComponent("drawLines");
  const _component_tables = common_vendor.resolveComponent("tables");
  const _component_canvass = common_vendor.resolveComponent("canvass");
  (_component_movableSrea + _component_drawLines + _component_tables + _component_canvass)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.saveImage && $options.saveImage(...args)),
    c: common_vendor.sr("movableSreas", "44d2a7c9-0"),
    d: common_vendor.sr("drawLiness", "44d2a7c9-1"),
    e: common_vendor.p({
      width: $props.canvasStyle.width,
      height: $props.canvasStyle.height
    }),
    f: $props.canvasStyle.width,
    g: $props.canvasStyle.height,
    h: common_vendor.o((...args) => $options.undo && $options.undo(...args)),
    i: common_vendor.o((...args) => $options.redo && $options.redo(...args)),
    j: common_vendor.o((...args) => $options.clear && $options.clear(...args)),
    k: common_vendor.o(($event) => $data.containersd = !$data.containersd),
    l: common_vendor.sr("tablesRef", "44d2a7c9-2"),
    m: common_vendor.p({
      width: $props.canvasStyle.width,
      height: $props.canvasStyle.height
    }),
    n: $data.containersd
  }, $data.containersd ? common_vendor.e({
    o: common_vendor.f($data.data, (item, index, i0) => {
      return common_vendor.e({
        a: item.closes
      }, item.closes ? {
        b: common_vendor.o(($event) => item.closes = !item.closes, index),
        c: common_assets._imports_0
      } : {
        d: common_vendor.o(($event) => item.closes = !item.closes, index),
        e: common_assets._imports_1
      }, {
        f: item.type === "text"
      }, item.type === "text" ? {} : {
        g: item.url
      }, {
        h: common_vendor.o(($event) => $options.countClick(item), index),
        i: common_vendor.t(item.name),
        j: common_vendor.o(($event) => $options.recordClick(item), index),
        k: index
      });
    }),
    p: !$data.data.length
  }, !$data.data.length ? {} : {}, {
    q: common_vendor.o(($event) => $data.containersd = !$data.containersd)
  }) : {}, {
    r: common_vendor.p({
      info: $data.info,
      width: $props.canvasStyle.width,
      height: $props.canvasStyle.height
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-44d2a7c9"]]);
wx.createPage(MiniProgramPage);
