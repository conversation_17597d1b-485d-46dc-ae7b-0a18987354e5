"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  props: {
    // 背景图
    imgesData: {
      type: String,
      default: ""
    },
    // 素材列表
    materialLists: {
      type: Array,
      default: [
        {
          name: "室内植物",
          child: [
            {
              name: "收藏",
              order: [
                {
                  name: "宝莲灯盆栽",
                  image: "https://img.shetu66.com/2023/07/18/1689659210837955.png"
                },
                {
                  name: "哈哈哈",
                  image: "https://tse1-mm.cn.bing.net/th/id/OIP-C.uuMRp41SjL9ukaBDDBWz5wHaNK?rs=1&pid=ImgDetMain"
                }
              ]
            },
            {
              name: "我的素材",
              order: [
                {
                  name: "宝莲灯盆栽",
                  image: "https://tse1-mm.cn.bing.net/th/id/OIP-C.uuMRp41SjL9ukaBDDBWz5wHaNK?rs=1&pid=ImgDetMain"
                }
              ]
            },
            {
              name: "艺术盆栽",
              order: [
                {
                  name: "宝莲灯盆栽",
                  image: "https://tse4-mm.cn.bing.net/th/id/OIP-C.vyXDY_jejkSCTPCB-wyh5AHaEK?rs=1&pid=ImgDetMain"
                }
              ]
            }
          ]
        },
        {
          name: "我的素材",
          child: [
            {
              name: "我的",
              order: [
                {
                  name: "宝莲灯盆栽",
                  image: "https://img.shetu66.com/2023/07/18/1689659210837955.png"
                }
              ]
            }
          ]
        }
      ]
    },
    // 模板列表
    templateLists: {
      type: Array,
      default: [{ name: "宝莲灯盆栽", image: "https://img.shetu66.com/2023/07/18/1689659210837955.png" }]
    },
    // 是否返回图片下载
    returnImage: {
      type: Boolean,
      default: true
    },
    // 是否下载图片
    download: {
      type: Boolean,
      default: true
    },
    // 画布样式宽高
    canvasStyle: {
      type: Object,
      default: {
        width: "100%",
        height: "950rpx"
      }
    },
    // 回显传入数据就行
    imageData: {
      type: Array,
      default: [{
        "createBy": null,
        "createTime": null,
        "updateBy": null,
        "updateTime": null,
        "remark": null,
        "id": 36,
        "createCode": "20250627162531A003",
        "x": null,
        "y": null,
        "w": "1280",
        "h": "960",
        "rotate": null,
        "selected": null,
        "switchStatue": null,
        "type": "backgorund",
        "text": null,
        "font": null,
        "isText": null,
        "size": null,
        "style": null,
        "path": null,
        "url": "https://tse1-mm.cn.bing.net/th/id/OIP-C.uuMRp41SjL9ukaBDDBWz5wHaNK?rs=1&pid=ImgDetMain",
        "scaleStart": null,
        switch: true
      }]
    },
    // 需要返回的地址 page: true 为 tabBar页面  false 为普通页面
    returnUrl: {
      type: Object,
      default: {
        page: false,
        url: ""
      }
    }
  },
  data() {
    return {
      images: [],
      // {url, x, y, w, h, selected}
      LayerList: [],
      sectionIndex: 0,
      menuItemIndex: 0,
      imgesMuns: null,
      imgesMunsData: {
        w: "",
        h: ""
      },
      imagesOnes: [],
      dragging: false,
      startTouch: { x: 0, y: 0 },
      startPos: { x: 0, y: 0 },
      ctx: null,
      materialVisible: "",
      textInput: "",
      // 文字相关
      textFont: "Arial",
      textSize: 36,
      textColor: "#fff",
      fonts: [
        { name: "Arial", label: "Arial" },
        { name: "PingFang SC", label: "苹方" },
        { name: "SimHei", label: "黑体" },
        { name: "FZShuTi", label: "舒体" }
      ],
      textColors: [
        "#fff",
        "#222",
        "#b6ff3b",
        "#e53935",
        "#ffd700",
        "#00bcd4",
        "#ff9800",
        "#8dd800"
      ],
      materialList: [],
      templateList: [],
      materialidx: 0,
      // 画笔相关
      penSize: 6,
      penColor: "#8dd800",
      penColors: [
        "#8dd800",
        "#fff",
        "#222",
        "#e53935",
        "#ffd700",
        "#00bcd4",
        "#ff9800"
      ],
      penStyle: "solid",
      _rotating: false,
      // 旋转
      _rotateStart: null,
      _rotateStartStart: false,
      // 图层
      layerBtn: false,
      // 缩放
      _scaling: false,
      _scaleStart: null,
      _scaleType: "both",
      // 缩放类型：'horizontal', 'vertical', 'both'
      arring: [],
      arringTure: false,
      finish: false,
      isDrawing: false,
      penPath: [],
      penLastPoint: null,
      currentStroke: null,
      currentPenColor: null,
      currentPenSize: null,
      currentPenStyle: null
      // 清空
    };
  },
  async onLoad() {
    const listYOPu = this.imageData;
    if (listYOPu) {
      try {
        const app = listYOPu;
        if (app && app.length > 0) {
          if (app[0].type == "backgorund") {
            this.background(app[0].url);
            if (app.slice(1).length) {
              this.images = app.slice(1);
            }
            this.drawImgs();
          } else {
            this.images = app;
            this.drawImgs();
          }
        }
      } catch (e) {
        console.error("解析存储数据失败:", e);
      }
    }
    this.canvasV();
  },
  watch: {
    arring: {
      handler() {
        console.log(this.arring, this.images, "this.arring.length == this.images.length");
        if (this.arring.length == this.images.length && this.finish) {
          if (this.download) {
            common_vendor.index.canvasToTempFilePath({
              canvasId: "canvasId",
              success: (res) => {
                common_vendor.index.saveImageToPhotosAlbum({
                  filePath: res.tempFilePath,
                  success: () => {
                    common_vendor.index.showToast({ title: "保存成功", icon: "success" });
                    this.finish = false;
                    this.arring = [];
                    common_vendor.index.hideLoading();
                  },
                  fail: () => {
                    common_vendor.index.showToast({ title: "保存失败", icon: "none" });
                  }
                });
              },
              fail: () => {
                common_vendor.index.showToast({ title: "生成图片失败", icon: "none" });
              }
            }, this);
          }
          if (this.returnImage) {
            common_vendor.index.canvasToTempFilePath({
              canvasId: "canvasId",
              success: (res) => {
                console.log(res, "resss");
                common_vendor.index.showToast({ title: "保存成功", icon: "success" });
                this.finish = false;
                common_vendor.index.$emit("returnImage", res.tempFilePath);
                common_vendor.index.hideLoading();
                this.arring = [];
              },
              fail: () => {
                common_vendor.index.showToast({ title: "生成图片失败", icon: "none" });
              }
            }, this);
          }
        }
      },
      deep: true
    }
  },
  computed: {
    // LayerListOne() {
    //     this.LayerList = this.images.reverse()
    //     return this.LayerList
    // }
  },
  methods: {
    background(e) {
      common_vendor.index.getImageInfo({
        src: e,
        success: (img) => {
          this.imgesMuns = img.path;
          this.imgesMunsData = {
            w: img.width,
            h: img.height
          };
        }
      });
    },
    canvasV() {
      console.log(this.arring, "arring");
      this.ctx = common_vendor.index.createCanvasContext("canvasId", this);
      this.materialList = this.materialLists;
      this.templateList = this.templateLists;
      if (this.imgesData && !this.imgesMuns) {
        this.background(this.imgesData);
      }
    },
    goHome() {
      if (this.returnUrl.url) {
        common_vendor.index.showToast({
          title: "未设置返回地址",
          icon: "none"
        });
      } else if (this.returnUrl.page) {
        common_vendor.index.switchTab({
          url: this.returnUrl.url
        });
      } else if (!this.returnUrl.page) {
        common_vendor.index.navigateTo({
          url: this.returnUrl.url
        });
      }
    },
    save() {
      if (this.imgesMuns) {
        this.images.unshift({
          type: "backgorund",
          url: this.imgesMuns,
          w: this.imgesMunsData.w,
          h: this.imgesMunsData.h,
          switch: true
        });
        this.arringTure = true;
        this.finish = true;
        this.drawImgs();
      } else {
        this.finish = true;
        this.drawImgs();
      }
      this.$emit("save", this.images);
    },
    sidebar(type) {
      this.materialVisible = "";
      var that = this;
      if (type == "material") {
        console.log(that.materialList[that.sectionIndex].child[that.menuItemIndex].order[that.materialidx].image, "this.materialList[this.materialidx]");
        common_vendor.index.getImageInfo({
          src: that.materialList[that.sectionIndex].child[that.menuItemIndex].order[that.materialidx].image,
          // that.materialList[that.materialidx].image
          success: function(img) {
            console.log(img.width);
            console.log(img.height);
            console.log(img, "11111");
            that.images.push({
              type: "material",
              name: that.materialList[that.sectionIndex].child[that.menuItemIndex].order[that.materialidx].name,
              url: img.path,
              x: 100 + that.images.length * 20,
              y: 100 + that.images.length * 20,
              w: img.width / 4,
              h: img.height / 4,
              selected: true,
              switch: true,
              rotate: 0,
              _scaleStart: { x: 0, y: 0, w: 0, h: 0 }
            });
            that.$nextTick(() => {
              that.drawImgs();
            });
          }
        });
      } else if (type == "text") {
        if (this.textInput.trim()) {
          this.images.push({
            type: "text",
            text: this.textInput,
            font: this.textFont,
            size: this.textSize,
            color: this.textColor,
            x: 100 + this.images.length * 20,
            y: 100 + this.images.length * 20,
            w: null,
            // 宽高后面计算
            h: null,
            selected: true,
            switch: true,
            isText: true,
            rotate: 0,
            _scaleStart: { x: 0, y: 0, w: 0, h: 0 }
          });
          setTimeout(() => {
            this.drawImgs();
          });
        }
      } else if (type == "pen") {
        console.log("点击确认按钮，检查是否有线条需要保存");
        console.log("当前状态 - isDrawing:", this.isDrawing, "penPath长度:", this.penPath ? this.penPath.length : 0);
        if (this.penPath && this.penPath.length > 0) {
          console.log("发现未保存的线条，正在保存...");
          this.handlePenEnd();
        } else {
          console.log("没有未保存的线条，直接重置状态");
          this.resetPenState();
        }
        console.log("画笔模式结束");
        console.log("当前images数组:", this.images);
      } else if (type == "template") {
        common_vendor.index.getImageInfo({
          src: that.materialList[this.materialidx].image,
          // that.materialList[that.materialidx].image
          success: function(img) {
            console.log(img.width);
            console.log(img.height);
            console.log(img, "11111");
            that.images.push({
              type: "add",
              name: that.materialList[this.materialidx].name,
              url: img.path,
              x: 0,
              y: 0,
              w: 400,
              h: 500,
              selected: true,
              switch: true,
              rotate: 0,
              _scaleStart: { x: 0, y: 0, w: 0, h: 0 }
            });
            that.$nextTick(() => {
              that.drawImgs();
            });
          }
        });
      } else
        ;
      this.images.forEach((item, idx) => {
        item.selected = idx === this.images.length - 1;
      });
    },
    chooseImage() {
      common_vendor.index.chooseImage({
        count: 9,
        success: (res) => {
          res.tempFilePaths.forEach((path) => {
            this.images.push({
              type: "add",
              url: path,
              x: 100 + this.images.length * 20,
              y: 100 + this.images.length * 20,
              w: 120,
              h: 120,
              selected: false,
              rotate: 0
            });
          });
          this.$nextTick(() => {
            this.drawImgs();
          });
        }
      });
    },
    // 图层向上一格
    up(item) {
      const idx = this.images.findIndex((img) => img === item);
      if (idx > 0) {
        const temp = this.images[idx - 1];
        this.images[idx - 1] = this.images[idx];
        this.images[idx] = temp;
        this.drawImgs();
      }
    },
    // 旋转
    touchStartRotate() {
      this._rotateStartStart = true;
    },
    touchmoveRotate(event) {
      const touch = event.touches[0];
      const img = this.images.find((item) => item.selected);
      if (!img)
        return;
      const touchX = touch.x !== void 0 ? touch.x : touch.clientX;
      const touchY = touch.y !== void 0 ? touch.y : touch.clientY;
      const centerX = img.x + img.w / 2;
      const centerY = img.y + img.h / 2;
      const currentAngle = Math.atan2(touchY - centerY, touchX - centerX);
      if (!this._rotateStart) {
        this._rotateStart = {
          startAngle: currentAngle,
          startRotate: img.rotate || 0,
          lastAngle: currentAngle,
          totalDelta: 0
        };
        console.log("开始旋转，初始角度:", (currentAngle * 180 / Math.PI).toFixed(1), "度");
      } else {
        let deltaAngle = currentAngle - this._rotateStart.lastAngle;
        if (deltaAngle > Math.PI) {
          deltaAngle -= 2 * Math.PI;
        } else if (deltaAngle < -Math.PI) {
          deltaAngle += 2 * Math.PI;
        }
        this._rotateStart.totalDelta += deltaAngle;
        img.rotate = (this._rotateStart.startRotate + this._rotateStart.totalDelta) * 1.5;
        this._rotateStart.lastAngle = currentAngle;
        console.log("旋转中 - 增量:", (deltaAngle * 180 / Math.PI).toFixed(1), "度, 总角度:", (img.rotate * 180 / Math.PI).toFixed(1), "度");
        this.drawImgs();
      }
    },
    touchEndRotate() {
      this._rotateStartStart = false;
      this._rotateStart = null;
    },
    touchEndScale() {
      this._scaling = false;
      this._scaleStart = null;
      this._scaleType = "both";
      console.log("缩放结束");
    },
    // 删除功能
    cornerDelete() {
      const selectedIndex = this.images.findIndex((item) => item.selected);
      if (selectedIndex !== -1) {
        const deletedItem = this.images[selectedIndex];
        common_vendor.index.showModal({
          title: "确认删除",
          content: `确定要删除这个${this.getItemTypeName(deletedItem.type)}吗？`,
          confirmText: "删除",
          cancelText: "取消",
          confirmColor: "#e53935",
          success: (res) => {
            if (res.confirm) {
              this.imagesOnes.push(deletedItem);
              this.images.splice(selectedIndex, 1);
              console.log("已删除元素:", deletedItem.type, "剩余元素数量:", this.images.length);
              this.drawImgs();
              common_vendor.index.showToast({
                title: "删除成功",
                icon: "success",
                duration: 1500
              });
            }
          }
        });
      } else {
        common_vendor.index.showToast({
          title: "请先选择要删除的元素",
          icon: "none",
          duration: 2e3
        });
      }
    },
    // 获取元素类型的中文名称
    getItemTypeName(type) {
      const typeNames = {
        "material": "素材",
        "add": "图片",
        "template": "模板",
        "text": "文字",
        "pen": "画笔"
      };
      return typeNames[type] || "元素";
    },
    // 复制功能
    cornerCopy() {
      console.log("<<<");
      const selectedItem = this.images.find((item) => item.selected);
      if (selectedItem) {
        const copiedItem = {
          ...selectedItem,
          x: selectedItem.x + 20,
          y: selectedItem.y + 20,
          selected: false
          // 新复制的元素不选中
        };
        if (selectedItem.type === "pen" && selectedItem.path) {
          copiedItem.path = [...selectedItem.path];
        }
        selectedItem.selected = false;
        this.images.push(copiedItem);
        console.log("已复制元素:", selectedItem.type, "总元素数量:", this.images.length);
        this.drawImgs();
        common_vendor.index.showToast({
          title: "复制成功",
          icon: "none",
          duration: 1500
        });
      } else {
        common_vendor.index.showToast({
          title: "请先选择要复制的元素",
          icon: "none",
          duration: 2e3
        });
      }
    },
    // 缩放
    rotateScale(event) {
      if (!this._scaling || !this._scaleStart)
        return;
      const touch = event.touches[0];
      const img = this.images.find((item) => item.selected);
      if (!img)
        return;
      const touchX = touch.x !== void 0 ? touch.x : touch.clientX;
      const touchY = touch.y !== void 0 ? touch.y : touch.clientY;
      const deltaX = touchX - this._scaleStart.x;
      const deltaY = touchY - this._scaleStart.y;
      const absDeltaX = Math.abs(deltaX);
      const absDeltaY = Math.abs(deltaY);
      let scaleType = "both";
      let newW = this._scaleStart.w;
      let newH = this._scaleStart.h;
      if (absDeltaX > absDeltaY && absDeltaX > 10) {
        scaleType = "horizontal";
        newW = Math.max(20, this._scaleStart.w + deltaX);
        newH = this._scaleStart.h;
      } else if (absDeltaY > absDeltaX && absDeltaY > 10) {
        scaleType = "vertical";
        newW = this._scaleStart.w;
        newH = Math.max(20, this._scaleStart.h + deltaY);
      } else if (absDeltaX > 10 || absDeltaY > 10) {
        scaleType = "both";
        const avgDelta = (deltaX + deltaY) / 2;
        newW = Math.max(20, this._scaleStart.w + avgDelta);
        newH = Math.max(20, this._scaleStart.h + avgDelta);
      }
      newW = Math.min(500, newW);
      newH = Math.min(500, newH);
      this._scaleType = scaleType;
      img.x = this._scaleStart.originX;
      img.y = this._scaleStart.originY;
      img.w = newW;
      img.h = newH;
      console.log(`缩放类型: ${scaleType}, 偏移: (${deltaX.toFixed(0)}, ${deltaY.toFixed(0)}), 新尺寸: ${newW.toFixed(0)}x${newH.toFixed(0)}`);
      this.drawImgs();
    },
    touchStartScale(event) {
      const touch = event.touches[0];
      const img = this.images.find((item) => item.selected);
      if (!img)
        return;
      const touchX = touch.x !== void 0 ? touch.x : touch.clientX;
      const touchY = touch.y !== void 0 ? touch.y : touch.clientY;
      this._scaling = true;
      this._scaleStart = {
        x: touchX,
        y: touchY,
        w: img.w,
        h: img.h,
        originX: img.x,
        // 左上角X坐标
        originY: img.y
        // 左上角Y坐标
      };
      console.log("开始缩放（左上角为中心），初始尺寸:", img.w, "x", img.h);
    },
    async drawImgs() {
      if (!this.ctx) {
        this.canvasV();
        console.log("没有");
      }
      this.ctx.clearRect(0, 0, 400, 400);
      console.log(this.images, "没有");
      if (this.finish) {
        common_vendor.index.showLoading({
          title: "加载中..."
        });
      }
      if (this.finish && !this.images.length) {
        common_vendor.index.showToast({ title: "请添加内容", icon: "error" });
        common_vendor.index.hideLoading();
        return;
      }
      var v = false;
      this.images.forEach((img) => {
        if (img.switch) {
          if (img.type == "backgorund") {
            console.log(img.url, "img.url");
            const canvasW = 400;
            const canvasH = 500;
            const scale = canvasH / img.h;
            const drawW = img.w * scale;
            const drawH = canvasH;
            const offsetX = (canvasW - drawW) / 2;
            const offsetY = 0;
            this.ctx.drawImage(img.url, offsetX, offsetY, drawW, drawH);
            if (this.finish) {
              console.log(this.finish, "====<");
              this.arring.push(true);
            }
          } else if (img.type == "material" || img.type == "add" || img.type == "template" || img.type == "penIamge") {
            console.log(img.rotate, "旋转角度");
            this.ctx.save();
            const cx = img.x + img.w / 2;
            const cy = img.y + img.h / 2;
            this.ctx.translate(cx, cy);
            this.ctx.rotate(img.rotate || 0);
            if (img.url.startsWith("https:")) {
              v = true;
              console.log(img.url, 11111);
              var that = this;
              common_vendor.index.getImageInfo({
                src: img.url,
                success(e) {
                  img.url = e.path;
                  console.log(that.ctx, "e.path");
                  that.ctx.drawImage(img.url, -img.w / 2, -img.h / 2, img.w, img.h);
                  that.ctx.restore();
                  that.ctx.draw();
                  if (that.finish) {
                    that.arring.push(true);
                  }
                  v = false;
                }
              });
            } else {
              console.log(2222);
              this.ctx.drawImage(img.url, -img.w / 2, -img.h / 2, img.w, img.h);
              this.ctx.restore();
              if (this.finish) {
                this.arring.push(true);
              }
            }
          } else if (img.type == "text") {
            if (img.text) {
              this.ctx.setFontSize(img.size || 36);
              this.ctx.setFillStyle(img.color || "#fff");
              this.ctx.setFontFamily && this.ctx.setFontFamily(img.font || "Arial");
              this.ctx.setTextAlign("left");
              this.ctx.setTextBaseline("top");
              const metrics = this.ctx.measureText(img.text);
              img.w = metrics.width;
              img.h = img.size || 36;
              this.ctx.save();
              const cx = img.x + img.w / 2;
              const cy = img.y + img.h / 2;
              this.ctx.translate(cx, cy);
              this.ctx.rotate(img.rotate || 0);
              this.ctx.fillText(img.text, -img.w / 2, -img.h / 2);
              this.ctx.restore();
              if (this.finish) {
                console.log("<<<<--3");
                this.arring.push(true);
              }
            }
          } else if (img.type == "pen") {
            if (img.path && img.path.length > 1) {
              this.ctx.beginPath();
              this.ctx.setStrokeStyle(img.color || "#8dd800");
              this.ctx.setLineWidth(img.size || 6);
              if (img.style === "dashed") {
                this.ctx.setLineDash && this.ctx.setLineDash([10, 8], 0);
              } else {
                this.ctx.setLineDash && this.ctx.setLineDash([], 0);
              }
              this.ctx.save();
              const cx = img.x + img.w / 2;
              const cy = img.y + img.h / 2;
              this.ctx.translate(cx, cy);
              this.ctx.rotate(img.rotate || 0);
              const firstPoint = img.path[0];
              this.ctx.moveTo(firstPoint.x - cx, firstPoint.y - cy);
              for (let i = 1; i < img.path.length; i++) {
                this.ctx.lineTo(img.path[i].x - cx, img.path[i].y - cy);
              }
              this.ctx.stroke();
              this.ctx.restore();
              this.ctx.setLineDash && this.ctx.setLineDash([], 0);
              if (this.finish) {
                console.log("<<<<--4");
                this.arring.push(true);
              }
            }
          }
        }
      });
      if (!v) {
        this.ctx.draw();
      }
    },
    onTouchStart(e) {
      const touch = e.touches[0];
      let found = false;
      for (let i = this.images.length - 1; i >= 0; i--) {
        const img = this.images[i];
        if (touch.x >= img.x && touch.x <= img.x + img.w && touch.y >= img.y && touch.y <= img.y + img.h) {
          this.images.forEach((item) => item.selected = false);
          img.selected = true;
          this.dragging = true;
          this.startTouch = { x: touch.x, y: touch.y };
          this.startPos = { x: img.x, y: img.y };
          found = true;
          this.images.splice(i, 1);
          this.images.push(img);
          console.log(img, "image");
          break;
        }
      }
      if (!found) {
        this.images.forEach((item) => item.selected = false);
        this.dragging = false;
      }
    },
    onTouchMove(e) {
      console.log(this.materialVisible, this.dragging, "划线");
      if (!this.dragging && this.materialVisible != "pen")
        return;
      if (this.materialVisible == "pen") {
        const touch = e.touches[0];
        if (!this.penLastPoint) {
          this.penLastPoint = { x: touch.x, y: touch.y };
          if (!this.penPath)
            this.penPath = [];
          this.penPath.push({ x: touch.x, y: touch.y });
          this.currentPenColor = this.penColor;
          this.currentPenSize = this.penSize;
          this.currentPenStyle = this.penStyle;
          console.log("开始新线条，颜色:", this.currentPenColor, "大小:", this.currentPenSize);
        } else {
          this.penPath.push({ x: touch.x, y: touch.y });
          if (!this.ctx) {
            this.ctx = common_vendor.index.createCanvasContext("canvasId", this);
          }
          this.ctx.beginPath();
          this.ctx.setStrokeStyle(this.currentPenColor);
          this.ctx.setLineWidth(this.currentPenSize);
          if (this.currentPenStyle === "dashed") {
            this.ctx.setLineDash && this.ctx.setLineDash([10, 8], 0);
          } else {
            this.ctx.setLineDash && this.ctx.setLineDash([], 0);
          }
          this.ctx.moveTo(this.penLastPoint.x, this.penLastPoint.y);
          this.ctx.lineTo(touch.x, touch.y);
          this.ctx.stroke();
          this.ctx.draw(true);
          this.penLastPoint = { x: touch.x, y: touch.y };
        }
      } else {
        const touch = e.touches[0];
        const img = this.images.find((item) => item.selected);
        if (img) {
          const dx = touch.x - this.startTouch.x;
          const dy = touch.y - this.startTouch.y;
          img.x = this.startPos.x + dx;
          img.y = this.startPos.y + dy;
          this.drawImgs();
        }
      }
    },
    onTouchEnd() {
      this.dragging = false;
      this._rotating = false;
      this._scaling = false;
      if (this.materialVisible === "pen") {
        this.handlePenEnd();
      }
    },
    ringhtClick() {
      if (!this.imagesOnes.length)
        return common_vendor.index.showToast({
          title: "没有上一步",
          icon: "none",
          duration: 1500
        });
      this.images.push(this.imagesOnes[this.imagesOnes.length - 1]);
      this.imagesOnes.pop();
      this.drawImgs();
    },
    leftClick() {
      if (this.images.length)
        return common_vendor.index.showToast({
          title: "没有下一步",
          icon: "none",
          duration: 1500
        });
      if (this.images.length > 0) {
        this.imagesOnes.push(this.images[this.images.length - 1]);
        this.images.pop();
        this.drawImgs();
      }
    },
    leftClickOff(idx) {
      if (this.images.length > 0 && idx >= 0 && idx < this.images.length) {
        this.imagesOnes.push(this.images[idx]);
        this.images.splice(idx, 1);
        this.drawImgs();
      }
    },
    // 关闭
    downs() {
      this.materialVisible = "";
    },
    // 隐藏图层
    hideIf(img) {
      if (img.selected) {
        img.selected = false;
      }
      img.switch = !img.switch;
      if (img.switch) {
        this.images.forEach((item, idx) => {
          item.selected = false;
        });
        img.selected = true;
      }
      this.drawImgs();
    },
    // 清空
    clear() {
      common_vendor.index.showModal({
        title: "确认删除",
        content: `确定要把所有内容清空吗？`,
        confirmText: "删除",
        cancelText: "取消",
        confirmColor: "#e53935",
        success: (res) => {
          if (res.confirm) {
            this.imagesOnes = this.images;
            this.images = [];
            this.drawImgs();
            common_vendor.index.showToast({
              title: "删除成功",
              icon: "success",
              duration: 1500
            });
          }
        }
      });
    },
    // ========== 新的画笔功能方法 ==========
    // 处理画笔移动
    handlePenMove(e) {
      const touch = e.touches[0];
      const currentPoint = { x: touch.x, y: touch.y };
      if (!this.isDrawing) {
        this.startNewStroke(currentPoint);
      } else {
        this.continueStroke(currentPoint);
      }
    },
    // 开始新的线条
    startNewStroke(point) {
      console.log("开始新线条，颜色:", this.penColor, "大小:", this.penSize);
      this.isDrawing = true;
      this.penPath = [point];
      this.penLastPoint = point;
      this.currentStroke = {
        color: this.penColor,
        size: this.penSize,
        style: this.penStyle,
        path: this.penPath
      };
      if (!this.ctx) {
        this.ctx = common_vendor.index.createCanvasContext("canvasId", this);
      }
    },
    // 继续当前线条
    continueStroke(point) {
      if (!this.isDrawing || !this.penLastPoint)
        return;
      this.penPath.push(point);
      this.ctx.beginPath();
      this.ctx.setStrokeStyle(this.currentStroke.color);
      this.ctx.setLineWidth(this.currentStroke.size);
      if (this.currentStroke.style === "dashed") {
        this.ctx.setLineDash && this.ctx.setLineDash([10, 8], 0);
      } else {
        this.ctx.setLineDash && this.ctx.setLineDash([], 0);
      }
      this.ctx.moveTo(this.penLastPoint.x, this.penLastPoint.y);
      this.ctx.lineTo(point.x, point.y);
      this.ctx.stroke();
      this.ctx.draw(true);
      this.penLastPoint = point;
    },
    // 结束画笔
    async handlePenEnd() {
      console.log("handlePenEnd 被调用");
      if (!this.penPath || this.penPath.length < 2) {
        console.log("没有有效的线条可保存，路径为空或点数不足");
        this.resetPenState();
        return;
      }
      if (!this.currentStroke) {
        console.log("没有当前线条属性，使用默认属性");
        this.currentStroke = {
          color: this.penColor,
          size: this.penSize,
          style: this.penStyle
        };
      }
      const xs = this.penPath.map((p) => p.x);
      const ys = this.penPath.map((p) => p.y);
      const minX = Math.min(...xs);
      const maxX = Math.max(...xs);
      const minY = Math.min(...ys);
      const maxY = Math.max(...ys);
      console.log(this.penPath, "this.penPath");
      const newPenItem = {
        type: "pen",
        path: this.penPath,
        color: this.currentStroke.color,
        size: this.currentStroke.size,
        style: this.currentStroke.style,
        selected: false,
        switch: true,
        x: minX,
        y: minY,
        w: Math.max(1, maxX - minX),
        h: Math.max(1, maxY - minY),
        rotate: 0
      };
      this.saveHistoryState();
      this.images.push(newPenItem);
      console.log("✅ 新线条已成功添加到images数组!");
      this.resetPenState();
      this.drawImgs();
      this.optimizePath(newPenItem);
      common_vendor.index.showToast({
        title: "线条已保存",
        icon: "success",
        duration: 1e3
      });
    },
    // 重置画笔状态
    resetPenState() {
      this.isDrawing = false;
      this.currentStroke = null;
      this.penPath = [];
      this.penLastPoint = null;
    },
    // 优化路径数据，减少点的数量
    optimizePath(path) {
      const tempCtx = common_vendor.index.createCanvasContext("canvasIdPen", this);
      this.width = path.w;
      this.height = path.h;
      console.log(path, "path");
      tempCtx.beginPath();
      tempCtx.setStrokeStyle(path.color || "#8dd800");
      tempCtx.setLineWidth(path.size || 6);
      if (path.style === "dashed") {
        tempCtx.setLineDash && tempCtx.setLineDash([10, 8], 0);
      } else {
        tempCtx.setLineDash && tempCtx.setLineDash([], 0);
      }
      tempCtx.save();
      const cx = path.x + path.w / 2;
      const cy = path.y + path.h / 2;
      tempCtx.translate(cx, cy);
      tempCtx.rotate(path.rotate || 0);
      const firstPoint = path.path[0];
      tempCtx.moveTo(firstPoint.x - cx, firstPoint.y - cy);
      for (let i = 1; i < path.path.length; i++) {
        tempCtx.lineTo(path.path[i].x - cx, path.path[i].y - cy);
      }
      tempCtx.stroke();
      tempCtx.restore();
      tempCtx.draw();
      common_vendor.index.canvasToTempFilePath({
        canvasId: "canvasIdPen",
        x: path.x,
        y: path.y,
        width: path.w + 20,
        height: path.h + 20,
        success: (res) => {
          console.log(res, "✅ ttt!");
          this.aff = res.tempFilePath;
          this.images[this.images.length - 1] = {
            ...path,
            path: null,
            url: res.tempFilePath,
            w: path.w + 15,
            h: path.h + 15,
            type: "penIamge"
          };
          console.log(this.images, "✅ 新线条已成功添加到images数组!");
          tempCtx.clearRect();
          this.drawImgs();
        }
      }, this);
    },
    // 保存当前状态用于撤销
    saveHistoryState() {
      const currentState = JSON.parse(JSON.stringify(this.images));
      if (!this.historyStates) {
        this.historyStates = [];
      }
      if (this.historyStates.length > 20) {
        this.historyStates.shift();
      }
      this.historyStates.push(currentState);
      console.log("已保存历史状态，当前历史记录数:", this.historyStates.length);
    },
    // 撤销上一步操作
    undoLastAction() {
      if (!this.historyStates || this.historyStates.length === 0) {
        common_vendor.index.showToast({
          title: "没有可撤销的操作",
          icon: "none",
          duration: 1500
        });
        return;
      }
      if (!this.redoStates) {
        this.redoStates = [];
      }
      this.redoStates.push(JSON.parse(JSON.stringify(this.images)));
      const previousState = this.historyStates.pop();
      this.images = previousState;
      this.drawImgs();
      common_vendor.index.showToast({
        title: "已撤销上一步操作",
        icon: "success",
        duration: 1e3
      });
    },
    // 重做上一步被撤销的操作
    redoLastAction() {
      if (!this.redoStates || this.redoStates.length === 0) {
        common_vendor.index.showToast({
          title: "没有可重做的操作",
          icon: "none",
          duration: 1500
        });
        return;
      }
      this.saveHistoryState();
      const nextState = this.redoStates.pop();
      this.images = nextState;
      this.drawImgs();
      common_vendor.index.showToast({
        title: "已重做操作",
        icon: "success",
        duration: 1e3
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goHome && $options.goHome(...args)),
    b: common_vendor.o((...args) => $options.save && $options.save(...args)),
    c: $data.imgesMuns,
    d: $props.canvasStyle.width,
    e: $props.canvasStyle.height,
    f: common_vendor.o((...args) => $options.onTouchStart && $options.onTouchStart(...args)),
    g: common_vendor.o((...args) => $options.onTouchMove && $options.onTouchMove(...args)),
    h: common_vendor.o((...args) => $options.onTouchEnd && $options.onTouchEnd(...args)),
    i: common_vendor.n($data.imgesMuns ? "" : "autos"),
    j: common_vendor.f($data.images, (img, idx, i0) => {
      return common_vendor.e({
        a: img.selected
      }, img.selected ? common_vendor.e({
        b: common_vendor.o((...args) => $options.cornerDelete && $options.cornerDelete(...args), idx),
        c: common_vendor.o((...args) => $options.cornerCopy && $options.cornerCopy(...args), idx),
        d: common_vendor.o((...args) => $options.touchStartRotate && $options.touchStartRotate(...args), idx),
        e: common_vendor.o((...args) => $options.touchmoveRotate && $options.touchmoveRotate(...args), idx),
        f: common_vendor.o((...args) => $options.touchEndRotate && $options.touchEndRotate(...args), idx),
        g: !$data._scaling
      }, !$data._scaling ? {} : $data._scaleType === "horizontal" ? {} : $data._scaleType === "vertical" ? {} : {}, {
        h: $data._scaleType === "horizontal",
        i: $data._scaleType === "vertical",
        j: common_vendor.o((...args) => $options.touchStartScale && $options.touchStartScale(...args), idx),
        k: common_vendor.o((...args) => $options.rotateScale && $options.rotateScale(...args), idx),
        l: common_vendor.o((...args) => $options.touchEndScale && $options.touchEndScale(...args), idx),
        m: "border-" + idx,
        n: img.x + "px",
        o: img.y + "px",
        p: img.w + "px",
        q: img.h + "px",
        r: img.rotate ? `rotate(${img.rotate}rad)` : "",
        s: `${(img.w + 5) / 2}px ${(img.h + 5) / 2}px`
      }) : {}, {
        t: idx
      });
    }),
    k: $props.canvasStyle.width,
    l: $props.canvasStyle.height,
    m: common_assets._imports_0$1,
    n: common_vendor.o(($event) => $data.materialVisible = "material"),
    o: common_assets._imports_1$1,
    p: common_vendor.o(($event) => $data.materialVisible = "template"),
    q: common_assets._imports_2,
    r: common_vendor.o(($event) => $data.materialVisible = "text"),
    s: common_assets._imports_3,
    t: common_vendor.o(($event) => $data.materialVisible = "pen"),
    v: common_assets._imports_4,
    w: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args)),
    x: common_vendor.o((...args) => $options.leftClick && $options.leftClick(...args)),
    y: common_vendor.o((...args) => $options.ringhtClick && $options.ringhtClick(...args)),
    z: common_vendor.o((...args) => $options.clear && $options.clear(...args)),
    A: common_vendor.t(!$data.layerBtn ? "图层" : "收起图层"),
    B: common_vendor.o(($event) => $data.layerBtn = !$data.layerBtn),
    C: $data.materialVisible == "material"
  }, $data.materialVisible == "material" ? common_vendor.e({
    D: common_vendor.o((...args) => $options.downs && $options.downs(...args)),
    E: common_vendor.o(($event) => $options.sidebar("material")),
    F: common_vendor.f($data.materialList, (item, idex, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: idex,
        c: common_vendor.n($data.sectionIndex == idex ? "active" : ""),
        d: common_vendor.o(($event) => $data.sectionIndex = idex, idex)
      };
    }),
    G: $data.materialList[$data.sectionIndex].child
  }, $data.materialList[$data.sectionIndex].child ? {
    H: common_vendor.f($data.materialList[$data.sectionIndex].child, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: common_vendor.n($data.menuItemIndex == index ? "active" : ""),
        c: index,
        d: common_vendor.o(($event) => $data.menuItemIndex = index, index)
      };
    })
  } : {}, {
    I: common_vendor.f($data.materialList[$data.sectionIndex].child[$data.menuItemIndex].order, (item, idx, i0) => {
      return common_vendor.e({
        a: item.image,
        b: common_vendor.t(item.name),
        c: idx === 0
      }, idx === 0 ? {} : {}, {
        d: idx !== 0
      }, idx !== 0 ? {} : {}, {
        e: $data.materialidx == idx ? 1 : "",
        f: idx,
        g: common_vendor.o(($event) => $data.materialidx = idx, idx)
      });
    })
  }) : {}, {
    J: $data.materialVisible == "text"
  }, $data.materialVisible == "text" ? {
    K: common_vendor.o((...args) => $options.downs && $options.downs(...args)),
    L: common_vendor.o(($event) => $options.sidebar("text")),
    M: $data.textInput,
    N: common_vendor.o(($event) => $data.textInput = $event.detail.value),
    O: common_vendor.f($data.fonts, (font, idx, i0) => {
      return {
        a: common_vendor.t(font.label),
        b: idx,
        c: common_vendor.n({
          active: $data.textFont === font.name
        }),
        d: common_vendor.o(($event) => $data.textFont = font.name, idx),
        e: font.name
      };
    }),
    P: $data.textSize,
    Q: common_vendor.t($data.textSize),
    R: common_vendor.f($data.textColors, (color, idx, i0) => {
      return common_vendor.e({
        a: $data.textColor === color
      }, $data.textColor === color ? {} : {}, {
        b: idx,
        c: common_vendor.n({
          selected: $data.textColor === color
        }),
        d: color,
        e: $data.textColor === color ? "2px solid #b6ff3b" : "2px solid #888",
        f: common_vendor.o(($event) => $data.textColor = color, idx)
      });
    })
  } : {}, {
    S: $data.materialVisible == "pen"
  }, $data.materialVisible == "pen" ? common_vendor.e({
    T: common_vendor.o((...args) => $options.downs && $options.downs(...args)),
    U: common_vendor.o(($event) => $options.sidebar("pen")),
    V: $data.penSize,
    W: common_vendor.f($data.penColors, (color, idx, i0) => {
      return common_vendor.e({
        a: $data.penColor === color
      }, $data.penColor === color ? {} : {}, {
        b: color,
        c: common_vendor.n({
          selected: $data.penColor === color
        }),
        d: color,
        e: $data.penColor === color ? "2px solid #b6ff3b" : "2px solid #888",
        f: common_vendor.o(($event) => $data.penColor = color, color)
      });
    }),
    X: common_assets._imports_5,
    Y: common_vendor.n({
      active: $data.penStyle === "solid"
    }),
    Z: common_vendor.o(($event) => $data.penStyle = "solid"),
    aa: common_assets._imports_6,
    ab: common_vendor.n({
      active: $data.penStyle === "dashed"
    }),
    ac: common_vendor.o(($event) => $data.penStyle = "dashed"),
    ad: $data.isDrawing && $data.currentStroke
  }, $data.isDrawing && $data.currentStroke ? {
    ae: $data.currentStroke.color,
    af: common_vendor.t($data.currentStroke.size),
    ag: common_vendor.t($data.currentStroke.style === "dashed" ? "虚线" : "实线")
  } : {}) : {}, {
    ah: $data.materialVisible == "template"
  }, $data.materialVisible == "template" ? {
    ai: common_vendor.o((...args) => $options.downs && $options.downs(...args)),
    aj: common_vendor.n({
      active: true
    }),
    ak: common_vendor.f($data.templateList, (item, idx, i0) => {
      return {
        a: item.image,
        b: common_vendor.t(item.name),
        c: idx,
        d: common_vendor.o(($event) => $options.sidebar("template"), idx)
      };
    })
  } : {}, {
    al: $data.layerBtn
  }, $data.layerBtn ? {
    am: common_vendor.n({
      active: true
    }),
    an: common_vendor.f($data.images, (img, idx, i0) => {
      return common_vendor.e({
        a: img.switch
      }, img.switch ? {
        b: common_assets._imports_7,
        c: common_vendor.o(($event) => $options.hideIf(img), idx)
      } : {
        d: common_vendor.o(($event) => $options.hideIf(img), idx),
        e: common_assets._imports_8
      }, {
        f: img.url
      }, img.url ? {
        g: img.url
      } : {
        h: common_vendor.t(img.type == "pen" ? "线条" : img.type == "backgorund" ? "背景" : "文字")
      }, {
        i: common_vendor.o(($event) => $options.up(img), idx),
        j: common_vendor.o(($event) => $options.leftClickOff(idx), idx),
        k: idx
      });
    }),
    ao: common_assets._imports_9,
    ap: common_vendor.o(($event) => $data.layerBtn = false)
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-a4b6909a"]]);
wx.createPage(MiniProgramPage);
